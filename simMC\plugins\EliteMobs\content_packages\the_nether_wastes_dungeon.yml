isEnabled: true
name: '&2[lvl 050] &3The Nether Wastes Dungeon'
customInfo:
- '&fAn unexplored part of the Nether.'
- '&6Credits: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>_'
dungeonSizeCategory: DUNGEON
worldName: em_id_the_nether_wastes
environment: NORMAL
protect: true
playerInfo: 'Difficulty: &45-man hard content!'
regionEnterMessage: '&bTraverse the wastes and see what you can find.'
regionLeaveMessage: '&bYou have left The Nether Wastes!'
startLocation: em_id_the_nether_wastes,41.5,82.5,55.5,-167,0
teleportLocation: em_id_the_nether_wastes,38.5,84,64.5,-155,0
dungeonObjectives:
- filename=em_id_the_nether_wastes_miniboss_5_shroud_p1.yml
contentType: INSTANCED_DUNGEON
dungeonConfigFolderName: em_id_the_nether_wastes
contentLevel: 50
difficulties:
- name: normal
  id: 0
  levelSync: 52
- name: hard
  id: 1
  levelSync: 50
- name: mythic
  id: 2
  levelSync: 48
setupMenuDescription: []
