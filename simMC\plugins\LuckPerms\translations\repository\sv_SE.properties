luckperms.logs.actionlog-prefix=LOGG
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORT
luckperms.commandsystem.available-commands=Använd {0} för att se tillgängliga kommandon
luckperms.commandsystem.command-not-recognised=Okänt kommando
luckperms.commandsystem.no-permission=Du har inte tillåtelse att använda detta kommando\!
luckperms.commandsystem.no-permission-subcommands=Du har inte tillåtelse att använda några underordnade kommandon
luckperms.commandsystem.already-executing-command=Ett annat kommando körs just nu, var god och vänta...
luckperms.commandsystem.usage.sub-commands-header=Underordnade Kommandon
luckperms.commandsystem.usage.usage-header=Kommando Användning
luckperms.commandsystem.usage.arguments-header=Argument
luckperms.first-time.no-permissions-setup=Det verkar som att inga behörigheter har installerats än\!
luckperms.first-time.use-console-to-give-access=Innan du kan använda några kommandon av LuckPerms i spelet, så bör du använda konsolen för att ge dig själv behörighet
luckperms.first-time.console-command-prompt=Öppna konsolen och kör
luckperms.first-time.next-step=Efter att du har avklarat det, så kan då börja att behandla behörigheter och grupper
luckperms.first-time.wiki-prompt=Osäker på var du ska börja? Kolla här\: {0}
luckperms.login.try-again=Försök igen senare
luckperms.login.loading-database-error=Ett databasfel inträffade vid laddning av behörighetsdata
luckperms.login.server-admin-check-console-errors=Om du är en server administratör, kontrollera konsolen för eventuella fel
luckperms.login.server-admin-check-console-info=Vänligen kontrollera server konsolen för mer information
luckperms.login.data-not-loaded-at-pre=Behörighetsdata för din användare kunde inte laddas under för-inloggningsstadiet
luckperms.login.unable-to-continue=kan inte försätta
luckperms.login.craftbukkit-offline-mode-error=detta beror sannolikt på en konflikt mellan CraftBukkit och online-mode inställningen
luckperms.login.unexpected-error=Ett oväntat fel inträffade när dina behörighetsdata skulle ställas in
luckperms.opsystem.disabled=Det vanliga OP systemet är inaktiverat på servern
luckperms.opsystem.sponge-warning=Observera att Server Operator status inte har någon effekt på Sponge permission checks när ett permissionplugin är installerat, du måste redigera användardata direkt
luckperms.duration.unit.years.plural={0} år
luckperms.duration.unit.years.singular={0} år
luckperms.duration.unit.years.short={0}å
luckperms.duration.unit.months.plural={0} månader
luckperms.duration.unit.months.singular={0} månad
luckperms.duration.unit.months.short={0} mån
luckperms.duration.unit.weeks.plural={0} veckor
luckperms.duration.unit.weeks.singular={0} vecka
luckperms.duration.unit.weeks.short={0}v
luckperms.duration.unit.days.plural={0} dagar
luckperms.duration.unit.days.singular={0} dag
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} timmar
luckperms.duration.unit.hours.singular={0} timma
luckperms.duration.unit.hours.short={0}t
luckperms.duration.unit.minutes.plural={0} minuter
luckperms.duration.unit.minutes.singular={0} minut
luckperms.duration.unit.minutes.short={0}m
luckperms.duration.unit.seconds.plural={0} sekunder
luckperms.duration.unit.seconds.singular={0} sekund
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} sedan
luckperms.command.misc.invalid-code=Ogiltig kod
luckperms.command.misc.response-code-key=svarskod
luckperms.command.misc.error-message-key=meddelande
luckperms.command.misc.bytebin-unable-to-communicate=Det gick inte att kommunicera med bytebin
luckperms.command.misc.webapp-unable-to-communicate=Det gick inte att kommunicera med webappen
luckperms.command.misc.check-console-for-errors=Kontrollera konsolen efter error
luckperms.command.misc.file-must-be-in-data=Filen {0} måste vara direkt underordnad för datakatalogen
luckperms.command.misc.wait-to-finish=Vänta på att den ska slutföras och försök igen
luckperms.command.misc.invalid-priority=Ogiltig prioritet {0}
luckperms.command.misc.expected-number=Förväntade ett tal
luckperms.command.misc.date-parse-error=Kunde inte tolka datumet {0}
luckperms.command.misc.date-in-past-error=Du kan inte ställa in ett datum i förflutnan\!
luckperms.command.misc.page=sida {0} av {1}
luckperms.command.misc.page-entries={0} poster
luckperms.command.misc.none=Inget
luckperms.command.misc.loading.error.unexpected=Ett okänt fel uppstod
luckperms.command.misc.loading.error.user=Användaren är inte laddad
luckperms.command.misc.loading.error.user-specific=Kunde inte ladda användaren {0}
luckperms.command.misc.loading.error.user-not-found=En användare för {0} hittades inte
luckperms.command.misc.loading.error.user-save-error=Ett fel uppstod när användardata skulle sparas för {0}
luckperms.command.misc.loading.error.user-not-online=Användaren {0} är inte online
luckperms.command.misc.loading.error.user-invalid={0} är inte ett giltigt användarnamn/uuid
luckperms.command.misc.loading.error.user-not-uuid=Användaren {0} är inte ett giltigt uuid
luckperms.command.misc.loading.error.group=Gruppen lästes inte in
luckperms.command.misc.loading.error.all-groups=Det går inte att läsa in alla grupper
luckperms.command.misc.loading.error.group-not-found=En grupp med namnet {0} kunde inte hittas
luckperms.command.misc.loading.error.group-save-error=Det gick inte att spara gruppdata för {0}
luckperms.command.misc.loading.error.group-invalid={0} är inte ett giltigt gruppnamn
luckperms.command.misc.loading.error.track=Spåret är inte laddat
luckperms.command.misc.loading.error.all-tracks=Kunde inte ladda alla spår
luckperms.command.misc.loading.error.track-not-found=Ett spår vid namn {0} kunde inte hittas
luckperms.command.misc.loading.error.track-save-error=Ett fel uppstod när spårdata skulle sparas för {0}
luckperms.command.misc.loading.error.track-invalid={0} är inte ett giltigt spårnamn
luckperms.command.editor.no-match=Kan inte öppna redigeraren, inga objekt matchade önskad typ
luckperms.command.editor.start=Förbereder en ny redigeringssession, var god vänta...
luckperms.command.editor.url=Klicka på länken nedan för att öppna redigeraren
luckperms.command.editor.unable-to-communicate=Det gick inte att kommunicera med redigeraren
luckperms.command.editor.apply-edits.success=Webbläsardata tillämpades på {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} och {2} {3}
luckperms.command.editor.apply-edits.success.additions=tillagda
luckperms.command.editor.apply-edits.success.additions-singular=tillägg
luckperms.command.editor.apply-edits.success.deletions=borttagningar
luckperms.command.editor.apply-edits.success.deletions-singular=borttagning
luckperms.command.editor.apply-edits.no-changes=Inga ändringar har gjorts från webbredigeraren, datan innehåller inga nya ändringar
luckperms.command.editor.apply-edits.unknown-type=Kan inte verkställa ändringarna till den angivna objekttypen
luckperms.command.editor.apply-edits.unable-to-read=Det gick inte att läsa data med den angivna koden
luckperms.command.search.searching.permission=Söker efter användare och grupper med {0}
luckperms.command.search.searching.inherit=Söker efter användare och grupper som ärver från {0}
luckperms.command.search.result=Hittade {0} resultat från {1} användare och {2} grupper
luckperms.command.search.result.default-notice=Notera\: När du söker efter medlemmar i default-gruppen visas inte offline-spelare utan ytterligare behörigheter\!
luckperms.command.search.showing-users=Visar användarposter
luckperms.command.search.showing-groups=Visar gruppposter
luckperms.command.tree.start=Skapar behörighetsträd, vänligen vänta...
luckperms.command.tree.empty=Kunde inte generera träd, inga resultat hittades
luckperms.command.tree.url=URL för behörighetsträd
luckperms.command.verbose.invalid-filter={0} är inte ett giltigt verbose filter
luckperms.command.verbose.enabled=Utförlig loggning {0} för kontroller matchande {1}
luckperms.command.verbose.command-exec=Tvingar {0} att köra kommandot {1} och rapportera alla kontroller gjorda...
luckperms.command.verbose.off=Verbose loggning {0}
luckperms.command.verbose.command-exec-complete=Kommandot utfört
luckperms.command.verbose.command.no-checks=Kommando utförandet var avklarat, men ingen tillståndskontroll kördes
luckperms.command.verbose.command.possibly-async=Detta kan vara för att pluginet kör kommandon i bakgrunden (asynk)
luckperms.command.verbose.command.try-again-manually=Du kan fortfarande använda verbose manuellt för att upptäcka liknande kontroller
luckperms.command.verbose.enabled-recording=Utförlig inspelning {0} för kontroller matchande {1}
luckperms.command.verbose.uploading=Mångordig loggning {0}, laddar upp resultat...
luckperms.command.verbose.url=Mångordig URL resultat
luckperms.command.verbose.enabled-term=aktiverad
luckperms.command.verbose.disabled-term=avaktiverad
luckperms.command.verbose.query-any=NÅGOT
luckperms.command.info.running-plugin=Körs
luckperms.command.info.platform-key=Plattform
luckperms.command.info.server-brand-key=Serverns varumärke
luckperms.command.info.server-version-key=Serverversionen
luckperms.command.info.storage-key=Lagring
luckperms.command.info.storage-type-key=Typ
luckperms.command.info.storage.meta.split-types-key=Typer
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Ansluten
luckperms.command.info.storage.meta.file-size-key=Filstorlek
luckperms.command.info.extensions-key=Tillägg
luckperms.command.info.messaging-key=Meddelanden
luckperms.command.info.instance-key=Instans
luckperms.command.info.static-contexts-key=Statiska sammanhang
luckperms.command.info.online-players-key=Spelare online
luckperms.command.info.online-players-unique={0} unika
luckperms.command.info.uptime-key=Upptid
luckperms.command.info.local-data-key=Lokal data
luckperms.command.info.local-data={0} användare, {1} grupper, {2} spår
luckperms.command.generic.create.success={0} har skapats
luckperms.command.generic.create.error=Det uppstod ett fel vid skapandet av {0}
luckperms.command.generic.create.error-already-exists={0} finns redan\!
luckperms.command.generic.delete.success={0} har tagits bort
luckperms.command.generic.delete.error=Det uppstod ett fel vid borttagningen av {0}
luckperms.command.generic.delete.error-doesnt-exist={0} finns inte\!
luckperms.command.generic.rename.success={0} har nu bytt namn till {1}
luckperms.command.generic.clone.success={0} klonades framgångsrikt till {1}
luckperms.command.generic.info.parent.title=Överordnade grupper
luckperms.command.generic.info.parent.temporary-title=Tillfälliga överordnade grupper
luckperms.command.generic.info.expires-in=slutar gälla om
luckperms.command.generic.info.inherited-from=ärvt från
luckperms.command.generic.info.inherited-from-self=själv
luckperms.command.generic.show-tracks.title={0}s spår
luckperms.command.generic.show-tracks.empty={0} är inte på något spår
luckperms.command.generic.clear.node-removed={0} noder togs bort
luckperms.command.generic.clear.node-removed-singular={0} nod togs bort
luckperms.command.generic.clear={0}s noder rensades i kontext {1}
luckperms.command.generic.permission.info.title={0}s behörigheter
luckperms.command.generic.permission.info.empty={0} har inte några behörigheter satta
luckperms.command.generic.permission.info.click-to-remove=Klicka för att ta bort noden från {0}
luckperms.command.generic.permission.check.info.title=Behörighetsinformation {0}
luckperms.command.generic.permission.check.info.directly={0} har {1} satt till {2} i kontext {3}
luckperms.command.generic.permission.check.info.inherited={0} ärver {1} satt till {2} från {3} i kontext {4}
luckperms.command.generic.permission.check.info.not-directly={0} har inte {1} satt
luckperms.command.generic.permission.check.info.not-inherited={0} ärver inte {1}
luckperms.command.generic.permission.check.result.title=Behörighetskontroll för {0}
luckperms.command.generic.permission.check.result.result-key=Resultat
luckperms.command.generic.permission.check.result.processor-key=Processor
luckperms.command.generic.permission.check.result.cause-key=Orsak
luckperms.command.generic.permission.check.result.context-key=Sammanhanget
luckperms.command.generic.permission.set=Sätt {0} till {1} för {2} i kontext {3}
luckperms.command.generic.permission.already-has={0} har redan {1} satt i kontext {2}
luckperms.command.generic.permission.set-temp=Sätt {0} till {1} för {2} under en varaktighet av {3} i kontexten {4}
luckperms.command.generic.permission.already-has-temp={0} har redan {1} tillfälligt satt i kontext {2}
luckperms.command.generic.permission.unset=Ta bort {0} för {1} i sammanhanget {2}
luckperms.command.generic.permission.doesnt-have={0} har inte {1} satt i kontext {2}
luckperms.command.generic.permission.unset-temp=Ta bort temporära behörigheten {0} för {1} i kontext {2}
luckperms.command.generic.permission.subtract=Sätt {0} till {1} för {2} under en varaktighet av {3} i kontexten {4}, {5} mindre än förut
luckperms.command.generic.permission.doesnt-have-temp={0} har inte {1} tillfälligt satt i kontext {2}
luckperms.command.generic.permission.clear={0} s behörigheter rensades i kontext {1}
luckperms.command.generic.parent.info.title={0}s Föräldrar
luckperms.command.generic.parent.info.empty={0} har inga föräldrar definierade
luckperms.command.generic.parent.info.click-to-remove=Klicka för att ta bort denna förälder från {0}
luckperms.command.generic.parent.add={0} ärver nu behörigheter från {1} i sammanhanget {2}
luckperms.command.generic.parent.add-temp={0} ärver nu behörigheter från {1} i en varaktighet av {2} i kontext {3}
luckperms.command.generic.parent.set={0} fick sina befintliga föräldragrupper rensade, och ärver nu bara {1} i sammanhang {2}
luckperms.command.generic.parent.set-track={0} fick sina befintliga föräldragrupper på spår {1} rensade och ärver nu bara {2} i sammanhang {3}
luckperms.command.generic.parent.remove={0} ärver inte längre behörigheter från {1} i kontext {2}
luckperms.command.generic.parent.remove-temp={0} ärver inte längre tillfälliga behörigheter från {1} i kontext {2}
luckperms.command.generic.parent.subtract={0} ärver behörigheter från {1} under en varaktighet av {2} i kontext {3}, {4} mindre än tidigare
luckperms.command.generic.parent.clear={0} s behörigheter rensades i kontext {1}
luckperms.command.generic.parent.clear-track={0} s behörigheter på spår {1} rensades i kontext {2}
luckperms.command.generic.parent.already-inherits={0} ärver redan från {1} i kontext {2}
luckperms.command.generic.parent.doesnt-inherit={0} ärver inte från {1} i kontext {2}
luckperms.command.generic.parent.already-temp-inherits={0} already temporarily inherits from {1} in context {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} does not temporarily inherit from {1} in context {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}s Prefix
luckperms.command.generic.chat-meta.info.title-suffix={0}s Suffix
luckperms.command.generic.chat-meta.info.none-prefix={0} har inga prefixar
luckperms.command.generic.chat-meta.info.none-suffix={0} har inga suffixer
luckperms.command.generic.chat-meta.info.click-to-remove=Klicka för att ta bort {0} från {1}
luckperms.command.generic.chat-meta.already-has={0} har redan {1} {2} satt till prioritet {3} i kontext {4}
luckperms.command.generic.chat-meta.already-has-temp={0} already has {1} {2} set temporarily at a priority of {3} in context {4}
luckperms.command.generic.chat-meta.doesnt-have={0} doesn''t have {1} {2} set at a priority of {3} in context {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} doesn''t have {1} {2} set temporarily at a priority of {3} in context {4}
luckperms.command.generic.chat-meta.add={0} had {1} {2} set at a priority of {3} in context {4}
luckperms.command.generic.chat-meta.add-temp={0} had {1} {2} set at a priority of {3} for a duration of {4} in context {5}
luckperms.command.generic.chat-meta.remove={0} had {1} {2} at priority {3} removed in context {4}
luckperms.command.generic.chat-meta.remove-bulk={0} had all {1} at priority {2} removed in context {3}
luckperms.command.generic.chat-meta.remove-temp={0} had temporary {1} {2} at priority {3} removed in context {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} had all temporary {1} at priority {2} removed in context {3}
luckperms.command.generic.meta.info.title={0}s Meta
luckperms.command.generic.meta.info.none={0} har ingen metadata
luckperms.command.generic.meta.info.click-to-remove=Klicka för att ta bort denna meta-nod från {0}
luckperms.command.generic.meta.already-has={0} har redan metanyckeln {1} inställd till {2} i kontext {3}
luckperms.command.generic.meta.already-has-temp={0} har redan metanyckeln {1} tillfälligt inställd till {2} i kontext {3}
luckperms.command.generic.meta.doesnt-have={0} har inte metanyckeln {1} inställd i kontext {2}
luckperms.command.generic.meta.doesnt-have-temp={0} har inte metanyckeln {1} tillfälligt inställd i kontext {2}
luckperms.command.generic.meta.set=Inställning av metanyckeln {0} till {1} för {2} i kontext {3}
luckperms.command.generic.meta.set-temp=Inställning av metanyckeln {0} till {1} för {2} under en tid av {3} i kontext {4}
luckperms.command.generic.meta.unset=Avinställning av metanyckeln {0} för {1} i kontext {2}
luckperms.command.generic.meta.unset-temp=Avinställning av tillfällig metanyckel {0} för {1} i kontext {2}
luckperms.command.generic.meta.clear=Metan för {0} med matchande typ {1} rensades i kontext {2}
luckperms.command.generic.contextual-data.title=Kontextuell data
luckperms.command.generic.contextual-data.mode.key=läge
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=aktiv spelare
luckperms.command.generic.contextual-data.contexts-key=Kontexter
luckperms.command.generic.contextual-data.prefix-key=Prefix
luckperms.command.generic.contextual-data.suffix-key=Suffix
luckperms.command.generic.contextual-data.primary-group-key=Primär grupp
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Inget
luckperms.command.user.info.title=Användarinfo
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=typ
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Du kan inte ta bort en användare från deras primära grupp
luckperms.command.user.primarygroup.not-member={0} var inte redan medlem av {1}, lägger till dem nu
luckperms.command.user.primarygroup.already-has={0} har redan {1} satt som sin primära grupp
luckperms.command.user.primarygroup.warn-option=Varning\: Den primära gruppberäkningsmetod som används av denna server ({0}) kanske inte återspeglar detta ändring
luckperms.command.user.primarygroup.set={0}s primära grupp har satts till {1}
luckperms.command.user.track.error-not-contain-group={0} är inte redan i några grupper på {1}
luckperms.command.user.track.unsure-which-track=Osäker på vilket spår som ska användas, vänligen ange spåret som ett argument
luckperms.command.user.track.missing-group-advice=Antingen skapa gruppen, eller ta bort den från spåret och försök igen
luckperms.command.user.promote.added-to-first={0} finns inte i några grupper på {1}, så lades till i den första gruppen, {2}, i kontext {3}
luckperms.command.user.promote.not-on-track={0} är inte med i några grupper på {1}, så blev inte befordrad
luckperms.command.user.promote.success=Befordrar {0} längs spår {1} från {2} till {3} i kontext {4}
luckperms.command.user.promote.end-of-track=Slutet av spår {0} nåddes, det gick inte att befordra {1}
luckperms.command.user.promote.next-group-deleted=Nästa grupp på spåret, {0}, finns inte längre
luckperms.command.user.promote.unable-to-promote=Det går inte att befordra användaren
luckperms.command.user.demote.success=Degraderar {0} längs spår {1} från {2} till {3} i kontext {4}
luckperms.command.user.demote.end-of-track=Slutet av spår {0} nåddes, så {1} togs bort från {2}
luckperms.command.user.demote.end-of-track-not-removed=Slutet av spår {0} nåddes, men {1} togs inte bort från den första gruppen
luckperms.command.user.demote.previous-group-deleted=Den föregående gruppen på spåret, {0}, finns inte längre
luckperms.command.user.demote.unable-to-demote=Kunde inte degradera användaren
luckperms.command.group.list.title=Grupper
luckperms.command.group.delete.not-default=Du kan inte ta bort standardgruppen
luckperms.command.group.info.title=Gruppinformation
luckperms.command.group.info.display-name-key=Visningsnamn
luckperms.command.group.info.weight-key=Vikt
luckperms.command.group.setweight.set=Sätt vikt till {0} för grupp {1}
luckperms.command.group.setdisplayname.doesnt-have={0} har inget visningsnamn satt
luckperms.command.group.setdisplayname.already-has={0} har redan visningsnamnet {1}
luckperms.command.group.setdisplayname.already-in-use=Visningsnamnet {0} används redan av {1}
luckperms.command.group.setdisplayname.set=Sätt visningsnamn till {0} för grupp {1} i kontext {2}
luckperms.command.group.setdisplayname.removed=Tog bort visningsnamn för grupp {0} i kontext {1}
luckperms.command.track.list.title=Spår
luckperms.command.track.path.empty=Inget
luckperms.command.track.info.showing-track=Visar spår
luckperms.command.track.info.path-property=Väg
luckperms.command.track.clear={0}s gruppspår rensades
luckperms.command.track.append.success=Grupp {0} lades till i spår {1}
luckperms.command.track.insert.success=Grupp {0} sattes in i spår {1} på position {2}
luckperms.command.track.insert.error-number=Förväntades ett nummer men mottog istället\: {0}
luckperms.command.track.insert.error-invalid-pos=Kunde inte sätta in vid position {0}
luckperms.command.track.insert.error-invalid-pos-reason=ogiltig position
luckperms.command.track.remove.success=Grupp {0} togs bort från spår {1}
luckperms.command.track.error-empty={0} kan inte användas då den är tom eller endast innehåller en grupp
luckperms.command.track.error-multiple-groups={0} är medlem i flera grupper på detta spår
luckperms.command.track.error-ambiguous=Det går inte att fastställa deras plats
luckperms.command.track.already-contains={0} innehåller redan {1}
luckperms.command.track.doesnt-contain={0} innehåller inte {1}
luckperms.command.log.load-error=Loggen kunde inte laddas
luckperms.command.log.invalid-page=Ogiltigt sidnummer
luckperms.command.log.invalid-page-range=Var vänlig och ange ett värde mellan {0} och {1}
luckperms.command.log.empty=Inga loggposter att visa
luckperms.command.log.notify.error-console=Kunde inte växla aviseringar för konsolen
luckperms.command.log.notify.enabled-term=Aktiverad
luckperms.command.log.notify.disabled-term=Avaktiverad
luckperms.command.log.notify.changed-state={0} loggningsutdata
luckperms.command.log.notify.already-on=Du tar redan emot aviseringar
luckperms.command.log.notify.already-off=Du får för närvarande inte aviseringar
luckperms.command.log.notify.invalid-state=Okänt tillstånd. Förväntar sig {0} eller {1}
luckperms.command.log.show.search=Visar senaste åtgärder för fråga {0}
luckperms.command.log.show.recent=Visar de senaste händelserna
luckperms.command.log.show.by=Visar de senaste händelserna efter {0}
luckperms.command.log.show.history=Visar historik för {0} {1}
luckperms.command.export.error-term=Fel
luckperms.command.export.already-running=En annan exportprocess körs redan
luckperms.command.export.file.already-exists=Filen {0} finns redan
luckperms.command.export.file.not-writable=Filen {0} är inte skrivbar
luckperms.command.export.file.success=Exporten till {0} lyckades
luckperms.command.export.file-unexpected-error-writing=Ett oväntat fel inträffade när filen skrevs
luckperms.command.export.web.export-code=Exportera kod
luckperms.command.export.web.import-command-description=Använd följande kommando för att importera
luckperms.command.import.term=Importera
luckperms.command.import.error-term=Fel
luckperms.command.import.already-running=En annan importprocess körs redan
luckperms.command.import.file.doesnt-exist=Filen {0} finns inte
luckperms.command.import.file.not-readable=Filen {0} kan inte läsas
luckperms.command.import.file.unexpected-error-reading=Ett oväntat fel inträffade vid läsning från importfilen
luckperms.command.import.file.correct-format=Är det rätt format?
luckperms.command.import.web.unable-to-read=Det gick inte att läsa data med den angivna koden
luckperms.command.import.progress.percent={0}% klar
luckperms.command.import.progress.operations={0}/{1} åtgärder slutförda
luckperms.command.import.starting=Startar importprocessen
luckperms.command.import.completed=AVSLUTAD
luckperms.command.import.duration=Tog {0} sekunder
luckperms.command.bulkupdate.must-use-console=Massuppdateringskommandot kan endast användas från konsolen
luckperms.command.bulkupdate.invalid-data-type=Ogiltig typ, förväntade sig {0}
luckperms.command.bulkupdate.invalid-constraint=Ogiltig begränsning {0}
luckperms.command.bulkupdate.invalid-constraint-format=Begränsningar ska vara i formatet {0}
luckperms.command.bulkupdate.invalid-comparison=Ogiltig jämförelseoperator {0}
luckperms.command.bulkupdate.invalid-comparison-format=Förväntade sig en av följande\: {0}
luckperms.command.bulkupdate.queued=Massuppdateringsoperationen har kösatts
luckperms.command.bulkupdate.confirm=Kör {0} för att köra uppdateringen
luckperms.command.bulkupdate.unknown-id=Åtgärd med id {0} existerar inte eller har löpt ut
luckperms.command.bulkupdate.starting=Kör massuppdatering
luckperms.command.bulkupdate.success=Massuppdateringen lyckades
luckperms.command.bulkupdate.success.statistics.nodes=Totalt påverkade noder
luckperms.command.bulkupdate.success.statistics.users=Totalt påverkade användare
luckperms.command.bulkupdate.success.statistics.groups=Totalt påverkade grupper
luckperms.command.bulkupdate.failure=Bulkuppdatering misslyckades, kontrollera konsolen efter fel
luckperms.command.update-task.request=En uppdateringsuppgift har begärts, vänligen vänta
luckperms.command.update-task.complete=Uppdateringen slutförd
luckperms.command.update-task.push.attempting=Försöker nu att flytta till andra servrar
luckperms.command.update-task.push.complete=Andra servrar har meddelats via {0}
luckperms.command.update-task.push.error=Fel vid flyttande av ändringar till andra servrar
luckperms.command.update-task.push.error-not-setup=Kan inte flytta ändringar till andra servrar då en meddelandetjänst inte har konfigurerats
luckperms.command.reload-config.success=Konfigurationsfilen laddades om
luckperms.command.reload-config.restart-note=Vissa inställningar kommer endast att appliceras efter att servern har startats om
luckperms.command.translations.searching=Söker efter tillgängliga översättningar, vänligen vänta...
luckperms.command.translations.searching-error=Kunde inte hämta en lista med tillgängliga översättningar
luckperms.command.translations.installed-translations=Installerade översättningar
luckperms.command.translations.available-translations=Tillgängliga översättningar
luckperms.command.translations.percent-translated={0}% översatt
luckperms.command.translations.translations-by=av
luckperms.command.translations.installing=Installerar översättningar, vänligen vänta...
luckperms.command.translations.download-error=Kunde inte ladda ner översättningen för {0}
luckperms.command.translations.installing-specific=Installerar språk {0}...
luckperms.command.translations.install-complete=Installation slutförd
luckperms.command.translations.download-prompt=Använd {0} för att ladda ner och installera uppdaterade versioner av dessa översättningar som tillhandahålls av gemenskapen
luckperms.command.translations.download-override-warning=Observera att detta kommer att skriva över alla ändringar som du har gjort för dessa översättningar
luckperms.usage.user.description=En uppsättning kommandon för att hantera användare inom LuckPerms. (En ''användare'' i LuckPerms är bara en spelare och kan referera till en UUID eller användarnamn)
luckperms.usage.group.description=En uppsättning kommandon för att hantera grupper inom LuckPerms. Grupper är bara samlingar av behörighetsattribut som kan ges till användare. Nya grupper skapas med kommandot ''creategroup''.
luckperms.usage.track.description=En uppsättning kommandon för att hantera spår inom LuckPerms. Spår är en ordnad samling av grupper som kan användas för att definiera befordringar och degraderingar.
luckperms.usage.log.description=En uppsättning kommandon för att hantera loggfunktionaliteten inom LuckPerms.
luckperms.usage.sync.description=Laddar om all data från pluginens lagring till minnet och tillämpar alla upptäckta ändringar.
luckperms.usage.info.description=Skriver ut allmän information om den aktiva plugininstansen.
luckperms.usage.editor.description=Skapar en ny webbredaktörssession
luckperms.usage.editor.argument.type=typerna att ladda in i redaktören. (''all'', ''users'' eller ''groups'')
luckperms.usage.editor.argument.filter=behörighet att filtrera användarposter efter
luckperms.usage.verbose.description=Kontrollerar pluginens övervakningssystem för detaljerad behörighetskontroll.
luckperms.usage.verbose.argument.action=om man vill aktivera/inaktivera loggning eller ladda upp den loggade utdata
luckperms.usage.verbose.argument.filter=filtret att matcha poster mot
luckperms.usage.verbose.argument.commandas=spelaren/kommandot att köra som
luckperms.usage.tree.description=Genererar ett trädvisningsvy (ordnad listhierarki) över alla behörigheter som LuckPerms känner till.
luckperms.usage.tree.argument.scope=rot av trädet. ange "." för att inkludera alla behörigheter
luckperms.usage.tree.argument.player=namnet på en online-spelare att kontrollera mot
luckperms.usage.search.description=Söker efter alla användare/grupper med en specifik behörighet
luckperms.usage.search.argument.permission=behörigheten att söka efter
luckperms.usage.search.argument.page=sidan att visa
luckperms.usage.network-sync.description=Synkroniserar ändringar med lagringen och begär att alla andra servrar i nätverket ska göra detsamma
luckperms.usage.import.description=Importerar data från en (tidigare skapad) exportfil
luckperms.usage.import.argument.file=filen att importera från
luckperms.usage.import.argument.replace=ersätt befintlig data istället för att sammanfoga den
luckperms.usage.import.argument.upload=ladda upp data från en tidigare export
luckperms.usage.export.description=Exporterar all behörighetsdata till en "export"-fil. Kan återimporteras vid ett senare tillfälle.
luckperms.usage.export.argument.file=filen att exportera till
luckperms.usage.export.argument.without-users=uteslut användare från exporten
luckperms.usage.export.argument.without-groups=uteslut grupper från exporten
luckperms.usage.export.argument.upload=Ladda upp all behörighetsdata till webbredaktören. Kan återimporteras vid ett senare tillfälle.
luckperms.usage.reload-config.description=Laddar om vissa konfigurationsalternativ
luckperms.usage.bulk-update.description=Utför bulkändringsförfrågningar på all data
luckperms.usage.bulk-update.argument.data-type=typen av data som ändras. (''all'', ''users'' eller ''groups'')
luckperms.usage.bulk-update.argument.action=åtgärden att utföra på datan. (''update'' eller ''delete'')
luckperms.usage.bulk-update.argument.action-field=fältet att agera på. endast nödvändigt för ''update''. (''permission'', ''server'' eller ''world'')
luckperms.usage.bulk-update.argument.action-value=värdet att ersätta med. endast nödvändigt för ''update''.
luckperms.usage.bulk-update.argument.constraint=begränsningarna som krävs för uppdateringen
luckperms.usage.translations.description=Hantera översättningar
luckperms.usage.translations.argument.install=underkommando för att installera översättningar
luckperms.usage.apply-edits.description=Tillämpar behörighetsändringar som gjorts från webbredaktören
luckperms.usage.apply-edits.argument.code=unik kod för datan
luckperms.usage.apply-edits.argument.target=vem datan ska tillämpas på
luckperms.usage.create-group.description=Skapar en ny grupp
luckperms.usage.create-group.argument.name=gruppens namn
luckperms.usage.create-group.argument.weight=gruppens vikt
luckperms.usage.create-group.argument.display-name=gruppens visningsnamn
luckperms.usage.delete-group.description=Tar bort en grupp
luckperms.usage.delete-group.argument.name=gruppens namn
luckperms.usage.list-groups.description=Lista alla grupper på plattformen
luckperms.usage.create-track.description=Skapa ett nytt spår
luckperms.usage.create-track.argument.name=spårets namn
luckperms.usage.delete-track.description=Ta bort ett spår
luckperms.usage.delete-track.argument.name=spårets namn
luckperms.usage.list-tracks.description=Lista alla spår på plattformen
luckperms.usage.user-info.description=Visar information om användaren
luckperms.usage.user-switchprimarygroup.description=Byter användarens primära grupp
luckperms.usage.user-switchprimarygroup.argument.group=gruppen att byta till
luckperms.usage.user-promote.description=Befordrar användaren upp på ett spår
luckperms.usage.user-promote.argument.track=spåret att befordra användaren på
luckperms.usage.user-promote.argument.context=sammanhanget att befordra användaren i
luckperms.usage.user-promote.argument.dont-add-to-first=befordra bara användaren om de redan är på spåret
luckperms.usage.user-demote.description=Avbefordrar användaren ner på ett spår
luckperms.usage.user-demote.argument.track=spåret att avbefordra användaren på
luckperms.usage.user-demote.argument.context=sammanhanget att avbefordra användaren i
luckperms.usage.user-demote.argument.dont-remove-from-first=hindra att användaren tas bort från den första gruppen
luckperms.usage.user-clone.description=Klonar användaren
luckperms.usage.user-clone.argument.user=användarens namn/UUID att klona på
luckperms.usage.group-info.description=Geri information om gruppen
luckperms.usage.group-listmembers.description=Visa användare/grupper som ärver från den här gruppen
luckperms.usage.group-listmembers.argument.page=sidan att visa
luckperms.usage.group-setweight.description=Sätt gruppens vikt
luckperms.usage.group-setweight.argument.weight=vikten att sätta
luckperms.usage.group-set-display-name.description=Sätt gruppens visningsnamn
luckperms.usage.group-set-display-name.argument.name=namnet att sätta
luckperms.usage.group-set-display-name.argument.context=sammanhanget att sätta namnet i
luckperms.usage.group-rename.description=Byt namn på gruppen
luckperms.usage.group-rename.argument.name=det nya namnet
luckperms.usage.group-clone.description=Klonar gruppen
luckperms.usage.group-clone.argument.name=namnet på gruppen att klona till
luckperms.usage.holder-editor.description=Öppnar webbredaktören för behörigheter
luckperms.usage.holder-showtracks.description=Listar spåren som objektet finns på
luckperms.usage.holder-clear.description=Ta bort alla behörigheter, föräldrar och meta
luckperms.usage.holder-clear.argument.context=sammanhanget att filtrera efter
luckperms.usage.permission.description=Redigerar behörigheter
luckperms.usage.parent.description=Redigerar föräldrar
luckperms.usage.meta.description=Redigerar meta-data värden
luckperms.usage.permission-info.description=Listar behörighetsnoderna objektet har
luckperms.usage.permission-info.argument.page=sidan att visa
luckperms.usage.permission-info.argument.sort-mode=hur man sorterar posterna
luckperms.usage.permission-set.description=Sätter en behörighet för objektet
luckperms.usage.permission-set.argument.node=behörighetsnoden att sätta
luckperms.usage.permission-set.argument.value=värdet på noden
luckperms.usage.permission-set.argument.context=sammanhanget att lägga till behörigheten i
luckperms.usage.permission-unset.description=Avsätter en behörighet för objektet
luckperms.usage.permission-unset.argument.node=behörighetsnoden att avsätta
luckperms.usage.permission-unset.argument.context=sammanhanget att ta bort behörigheten i
luckperms.usage.permission-settemp.description=Sätter en behörighet för objektet temporärt
luckperms.usage.permission-settemp.argument.node=behörighetsnoden att sätta
luckperms.usage.permission-settemp.argument.value=värdet på noden
luckperms.usage.permission-settemp.argument.duration=varaktigheten tills behörighetsnoden löper ut
luckperms.usage.permission-settemp.argument.temporary-modifier=hur den tillfälliga behörigheten ska tillämpas
luckperms.usage.permission-settemp.argument.context=sammanhanget att lägga till behörigheten i
luckperms.usage.permission-unsettemp.description=Avsätter en tillfällig behörighet för objektet
luckperms.usage.permission-unsettemp.argument.node=behörighetsnoden att avsätta
luckperms.usage.permission-unsettemp.argument.duration=varaktigheten att subtrahera
luckperms.usage.permission-unsettemp.argument.context=sammanhanget att ta bort behörigheten i
luckperms.usage.permission-check.description=Kontrollerar om objektet har en viss behörighetsnod
luckperms.usage.permission-check.argument.node=behörighetsnoden att kontrollera för
luckperms.usage.permission-clear.description=Rensar alla behörigheter
luckperms.usage.permission-clear.argument.context=sammanhanget att filtrera efter
luckperms.usage.parent-info.description=Listar grupperna som detta objekt ärver från
luckperms.usage.parent-info.argument.page=sidan att visa
luckperms.usage.parent-info.argument.sort-mode=hur man sorterar posterna
luckperms.usage.parent-set.description=Tar bort alla andra grupper som objektet redan ärver och lägger till dem i den angivna
luckperms.usage.parent-set.argument.group=gruppen att sätta till
luckperms.usage.parent-set.argument.context=sammanhanget att sätta gruppen i
luckperms.usage.parent-add.description=Sätter en annan grupp för objektet att ärva behörigheter från
luckperms.usage.parent-add.argument.group=gruppen att ärva från
luckperms.usage.parent-add.argument.context=sammanhanget att ärva gruppen i
luckperms.usage.parent-remove.description=Avlägsnar en tidigare satt arvregel
luckperms.usage.parent-remove.argument.group=gruppen att ta bort
luckperms.usage.parent-remove.argument.context=sammanhanget att ta bort gruppen i
luckperms.usage.parent-set-track.description=Tar bort alla andra grupper som objektet redan ärver från på det angivna spåret och lägger till dem i det angivna
luckperms.usage.parent-set-track.argument.track=spåret att sätta på
luckperms.usage.parent-set-track.argument.group=gruppen att sätta till, eller ett nummer relaterat till positionen för gruppen på det angivna spåret
luckperms.usage.parent-set-track.argument.context=sammanhanget att sätta gruppen i
luckperms.usage.parent-add-temp.description=Sätter tillfälligt en annan grupp för objektet att ärva behörigheter från
luckperms.usage.parent-add-temp.argument.group=gruppen att ärva från
luckperms.usage.parent-add-temp.argument.duration=varaktigheten för gruppmedlemskapet
luckperms.usage.parent-add-temp.argument.temporary-modifier=hur den tillfälliga behörigheten ska tillämpas
luckperms.usage.parent-add-temp.argument.context=sammanhanget att ärva gruppen i
luckperms.usage.parent-remove-temp.description=Avlägsnar en tidigare satt tillfällig arvregel
luckperms.usage.parent-remove-temp.argument.group=gruppen att ta bort
luckperms.usage.parent-remove-temp.argument.duration=varaktigheten att subtrahera
luckperms.usage.parent-remove-temp.argument.context=sammanhanget att ta bort gruppen i
luckperms.usage.parent-clear.description=Rensar alla föräldrar
luckperms.usage.parent-clear.argument.context=sammanhanget att filtrera efter
luckperms.usage.parent-clear-track.description=Rensar alla föräldrar på ett angivet spår
luckperms.usage.parent-clear-track.argument.track=spåret att ta bort på
luckperms.usage.parent-clear-track.argument.context=sammanhanget att filtrera efter
luckperms.usage.meta-info.description=Visar all chat meta
luckperms.usage.meta-set.description=Sätter ett meta-värde
luckperms.usage.meta-set.argument.key=nyckeln att sätta
luckperms.usage.meta-set.argument.value=värdet att sätta nyckeln till
luckperms.usage.meta-set.argument.context=sammanhanget att lägga till meta-paret i
luckperms.usage.meta-unset.description=Avsätter ett meta-värde
luckperms.usage.meta-unset.argument.key=nyckeln att avsätta
luckperms.usage.meta-unset.argument.context=sammanhanget att ta bort meta-paret i
luckperms.usage.meta-settemp.description=Sätter ett meta-värde tillfälligt
luckperms.usage.meta-settemp.argument.key=nyckeln att sätta
luckperms.usage.meta-settemp.argument.value=värdet att sätta nyckeln till
luckperms.usage.meta-settemp.argument.duration=varaktigheten tills meta-värdet löper ut
luckperms.usage.meta-settemp.argument.context=sammanhanget att lägga till meta-paret i
luckperms.usage.meta-unsettemp.description=Avsätter ett tillfälligt meta-värde
luckperms.usage.meta-unsettemp.argument.key=nyckeln att avsätta
luckperms.usage.meta-unsettemp.argument.context=sammanhanget att ta bort meta-paret i
luckperms.usage.meta-addprefix.description=Lägger till ett prefix
luckperms.usage.meta-addprefix.argument.priority=prioriteten att lägga till prefixet på
luckperms.usage.meta-addprefix.argument.prefix=prefixsträngen
luckperms.usage.meta-addprefix.argument.context=sammanhanget att lägga till prefixet i
luckperms.usage.meta-addsuffix.description=Lägger till ett suffix
luckperms.usage.meta-addsuffix.argument.priority=prioriteten att lägga till suffixet på
luckperms.usage.meta-addsuffix.argument.suffix=suffixsträngen
luckperms.usage.meta-addsuffix.argument.context=sammanhanget att lägga till suffixet i
luckperms.usage.meta-setprefix.description=Sätter ett prefix
luckperms.usage.meta-setprefix.argument.priority=prioriteten att sätta prefixet på
luckperms.usage.meta-setprefix.argument.prefix=prefixsträngen
luckperms.usage.meta-setprefix.argument.context=sammanhanget att sätta prefixet i
luckperms.usage.meta-setsuffix.description=Sätter ett suffix
luckperms.usage.meta-setsuffix.argument.priority=prioriteten att sätta suffixet på
luckperms.usage.meta-setsuffix.argument.suffix=suffixsträngen
luckperms.usage.meta-setsuffix.argument.context=sammanhanget att sätta suffixet i
luckperms.usage.meta-removeprefix.description=Avlägsnar ett prefix
luckperms.usage.meta-removeprefix.argument.priority=prioriteten att ta bort prefixet på
luckperms.usage.meta-removeprefix.argument.prefix=prefixsträngen
luckperms.usage.meta-removeprefix.argument.context=sammanhanget att ta bort prefixet i
luckperms.usage.meta-removesuffix.description=Avlägsnar ett suffix
luckperms.usage.meta-removesuffix.argument.priority=prioriteten att ta bort suffixet på
luckperms.usage.meta-removesuffix.argument.suffix=suffixsträngen
luckperms.usage.meta-removesuffix.argument.context=sammanhanget att ta bort suffixet i
luckperms.usage.meta-addtemp-prefix.description=Lägger till ett prefix tillfälligt
luckperms.usage.meta-addtemp-prefix.argument.priority=prioriteten att lägga till prefixet på
luckperms.usage.meta-addtemp-prefix.argument.prefix=prefixsträngen
luckperms.usage.meta-addtemp-prefix.argument.duration=varaktigheten tills prefixet löper ut
luckperms.usage.meta-addtemp-prefix.argument.context=sammanhanget att lägga till prefixet i
luckperms.usage.meta-addtemp-suffix.description=Lägger till ett suffix tillfälligt
luckperms.usage.meta-addtemp-suffix.argument.priority=prioriteten att lägga till suffixet på
luckperms.usage.meta-addtemp-suffix.argument.suffix=suffixsträngen
luckperms.usage.meta-addtemp-suffix.argument.duration=varaktigheten tills suffixet löper ut
luckperms.usage.meta-addtemp-suffix.argument.context=sammanhanget att lägga till suffixet i
luckperms.usage.meta-settemp-prefix.description=Sätter ett prefix tillfälligt
luckperms.usage.meta-settemp-prefix.argument.priority=prioriteten att sätta prefixet på
luckperms.usage.meta-settemp-prefix.argument.prefix=prefixsträngen
luckperms.usage.meta-settemp-prefix.argument.duration=varaktigheten tills prefixet löper ut
luckperms.usage.meta-settemp-prefix.argument.context=sammanhanget att sätta prefixet i
luckperms.usage.meta-settemp-suffix.description=Sätter ett suffix tillfälligt
luckperms.usage.meta-settemp-suffix.argument.priority=prioriteten att sätta suffixet på
luckperms.usage.meta-settemp-suffix.argument.suffix=suffixsträngen
luckperms.usage.meta-settemp-suffix.argument.duration=varaktigheten tills suffixet löper ut
luckperms.usage.meta-settemp-suffix.argument.context=sammanhanget att sätta suffixet i
luckperms.usage.meta-removetemp-prefix.description=Avlägsnar ett tillfälligt prefix
luckperms.usage.meta-removetemp-prefix.argument.priority=prioriteten att ta bort prefixet på
luckperms.usage.meta-removetemp-prefix.argument.prefix=prefixsträngen
luckperms.usage.meta-removetemp-prefix.argument.context=sammanhanget att ta bort prefixet i
luckperms.usage.meta-removetemp-suffix.description=Avlägsnar ett tillfälligt suffix
luckperms.usage.meta-removetemp-suffix.argument.priority=prioriteten att ta bort suffixet på
luckperms.usage.meta-removetemp-suffix.argument.suffix=suffixsträngen
luckperms.usage.meta-removetemp-suffix.argument.context=sammanhanget att ta bort suffixet i
luckperms.usage.meta-clear.description=Rensar all meta
luckperms.usage.meta-clear.argument.type=typen av meta att ta bort
luckperms.usage.meta-clear.argument.context=sammanhanget att filtrera efter
luckperms.usage.track-info.description=Geri information om spåret
luckperms.usage.track-editor.description=Öppnar webbredigeraren för behörigheter
luckperms.usage.track-append.description=Lägger till en grupp längst bak på spåret
luckperms.usage.track-append.argument.group=gruppen att lägga till
luckperms.usage.track-insert.description=Infogar en grupp på en given position längs spåret
luckperms.usage.track-insert.argument.group=gruppen att infoga
luckperms.usage.track-insert.argument.position=positionen att infoga gruppen på (första positionen på spåret är 1)
luckperms.usage.track-remove.description=Avlägsnar en grupp från spåret
luckperms.usage.track-remove.argument.group=gruppen att ta bort
luckperms.usage.track-clear.description=Rensar grupperna på spåret
luckperms.usage.track-rename.description=Byter namn på spåret
luckperms.usage.track-rename.argument.name=det nya namnet
luckperms.usage.track-clone.description=Klonar spåret
luckperms.usage.track-clone.argument.name=spårets namn att klona till
luckperms.usage.log-recent.description=Visar de senaste händelserna
luckperms.usage.log-recent.argument.user=användarens namn/UUID att filtrera efter
luckperms.usage.log-recent.argument.page=sidnumret att visa
luckperms.usage.log-search.description=Sök i loggen efter en post
luckperms.usage.log-search.argument.query=sökfrågan att söka efter
luckperms.usage.log-search.argument.page=sidnumret att visa
luckperms.usage.log-notify.description=Aktiverar/deaktiverar loggaviseringar
luckperms.usage.log-notify.argument.toggle=om man ska aktivera eller inaktivera
luckperms.usage.log-user-history.description=Visa en användares historik
luckperms.usage.log-user-history.argument.user=användarens namn/UUID
luckperms.usage.log-user-history.argument.page=sidnumret att visa
luckperms.usage.log-group-history.description=Visa en grupps historik
luckperms.usage.log-group-history.argument.group=gruppens namn
luckperms.usage.log-group-history.argument.page=sidnumret att visa
luckperms.usage.log-track-history.description=Visa ett spårs historik
luckperms.usage.log-track-history.argument.track=spårets namn
luckperms.usage.log-track-history.argument.page=sidnumret att visa
luckperms.usage.sponge.description=Redigera extra Sponge-data
luckperms.usage.sponge.argument.collection=samlingen att fråga
luckperms.usage.sponge.argument.subject=ämnet att modifiera
luckperms.usage.sponge-permission-info.description=Visa information om ämnets behörigheter
luckperms.usage.sponge-permission-info.argument.contexts=sammanhanget att filtrera efter
luckperms.usage.sponge-permission-set.description=Sätter en behörighet för ämnet
luckperms.usage.sponge-permission-set.argument.node=behörighetsnoden att sätta
luckperms.usage.sponge-permission-set.argument.tristate=värdet att sätta behörigheten till
luckperms.usage.sponge-permission-set.argument.contexts=sammanhanget att sätta behörigheten i
luckperms.usage.sponge-permission-clear.description=Rensar ämnets behörigheter
luckperms.usage.sponge-permission-clear.argument.contexts=sammanhanget att rensa behörigheter i
luckperms.usage.sponge-parent-info.description=Visa information om ämnets föräldrar
luckperms.usage.sponge-parent-info.argument.contexts=sammanhanget att filtrera efter
luckperms.usage.sponge-parent-add.description=Lägger till en förälder till ämnet
luckperms.usage.sponge-parent-add.argument.collection=samlingen där förälderämnet är
luckperms.usage.sponge-parent-add.argument.subject=namnet på förälderämnet
luckperms.usage.sponge-parent-add.argument.contexts=sammanhanget att lägga till föräldern i
luckperms.usage.sponge-parent-remove.description=Avlägsnar en förälder från ämnet
luckperms.usage.sponge-parent-remove.argument.collection=samlingen där förälderämnet är
luckperms.usage.sponge-parent-remove.argument.subject=namnet på förälderämnet
luckperms.usage.sponge-parent-remove.argument.contexts=sammanhanget att ta bort föräldern i
luckperms.usage.sponge-parent-clear.description=Rensar ämnets föräldrar
luckperms.usage.sponge-parent-clear.argument.contexts=sammanhanget att rensa föräldrar i
luckperms.usage.sponge-option-info.description=Visa information om ämnets alternativ
luckperms.usage.sponge-option-info.argument.contexts=sammanhanget att filtrera efter
luckperms.usage.sponge-option-set.description=Sätter ett alternativ för ämnet
luckperms.usage.sponge-option-set.argument.key=nyckeln att sätta
luckperms.usage.sponge-option-set.argument.value=värdet att sätta nyckeln till
luckperms.usage.sponge-option-set.argument.contexts=sammanhanget att sätta alternativet i
luckperms.usage.sponge-option-unset.description=Avsätter ett alternativ för ämnet
luckperms.usage.sponge-option-unset.argument.key=nyckeln att avsätta
luckperms.usage.sponge-option-unset.argument.contexts=sammanhanget att avsätta nyckeln i
luckperms.usage.sponge-option-clear.description=Rensar ämnets alternativ
luckperms.usage.sponge-option-clear.argument.contexts=sammanhanget att rensa alternativ i
