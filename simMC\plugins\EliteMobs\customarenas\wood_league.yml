isEnabled: true
arenaName: Wood League
corner1: em_adventurers_guild,257,69,333,0,0
corner2: em_adventurers_guild,181,91,257,0,0
startLocation: em_adventurers_guild,219.5,70,295.5,0,0
exitLocation: em_adventurers_guild,242.5,92,269.5,84,0
waveCount: 50
delayBetweenWaves: 5
spawnPoints:
- name=north:location=em_adventurers_guild,219.5,71,273.5
- name=south:location=em_adventurers_guild,219.5,71,316.5
- name=west:location=em_adventurers_guild,197.5,71,295.5
- name=east:location=em_adventurers_guild,240.5,71,295.5
- name=center:location=em_adventurers_guild,219.5,71,295.5
- name=northeast:location=em_adventurers_guild,233.5,71,281.5
- name=southeast:location=em_adventurers_guild,233.5,71,309.5
- name=northwest:location=em_adventurers_guild,205.5,71,281.5
- name=southwest:location=em_adventurers_guild,205.5,71,309.5
bossList:
- wave=1:spawnPoint=north:boss=wood_league_wave_1_melee.yml
- wave=1:spawnPoint=north:boss=wood_league_wave_1_melee.yml
- wave=1:spawnPoint=north:boss=wood_league_wave_1_ranged.yml
- wave=2:spawnPoint=north:boss=wood_league_wave_2_melee.yml
- wave=2:spawnPoint=north:boss=wood_league_wave_2_melee.yml
- wave=2:spawnPoint=north:boss=wood_league_wave_2_ranged.yml
- wave=2:spawnPoint=south:boss=wood_league_wave_2_melee.yml
- wave=2:spawnPoint=south:boss=wood_league_wave_2_melee.yml
- wave=2:spawnPoint=south:boss=wood_league_wave_2_ranged.yml
- wave=3:spawnPoint=north:boss=wood_league_wave_3_melee.yml
- wave=3:spawnPoint=north:boss=wood_league_wave_3_melee.yml
- wave=3:spawnPoint=north:boss=wood_league_wave_3_ranged.yml
- wave=3:spawnPoint=south:boss=wood_league_wave_3_melee.yml
- wave=3:spawnPoint=south:boss=wood_league_wave_3_melee.yml
- wave=3:spawnPoint=south:boss=wood_league_wave_3_ranged.yml
- wave=4:spawnPoint=north:boss=wood_league_wave_4_melee.yml
- wave=4:spawnPoint=north:boss=wood_league_wave_4_melee.yml
- wave=4:spawnPoint=north:boss=wood_league_wave_4_ranged.yml
- wave=4:spawnPoint=south:boss=wood_league_wave_4_melee.yml
- wave=4:spawnPoint=south:boss=wood_league_wave_4_melee.yml
- wave=4:spawnPoint=south:boss=wood_league_wave_4_ranged.yml
- wave=5:spawnPoint=center:boss=wood_league_wave_5_miniboss.yml
- wave=6:spawnPoint=north:boss=wood_league_wave_6_melee.yml
- wave=6:spawnPoint=north:boss=wood_league_wave_6_melee.yml
- wave=6:spawnPoint=north:boss=wood_league_wave_6_ranged.yml
- wave=6:spawnPoint=south:boss=wood_league_wave_6_melee.yml
- wave=6:spawnPoint=south:boss=wood_league_wave_6_melee.yml
- wave=6:spawnPoint=south:boss=wood_league_wave_6_ranged.yml
- wave=6:spawnPoint=east:boss=wood_league_wave_6_melee.yml
- wave=6:spawnPoint=east:boss=wood_league_wave_6_melee.yml
- wave=6:spawnPoint=east:boss=wood_league_wave_6_ranged.yml
- wave=6:spawnPoint=west:boss=wood_league_wave_6_melee.yml
- wave=6:spawnPoint=west:boss=wood_league_wave_6_melee.yml
- wave=6:spawnPoint=west:boss=wood_league_wave_6_ranged.yml
- wave=7:spawnPoint=north:boss=wood_league_wave_7_melee.yml
- wave=7:spawnPoint=north:boss=wood_league_wave_7_melee.yml
- wave=7:spawnPoint=north:boss=wood_league_wave_7_ranged.yml
- wave=7:spawnPoint=south:boss=wood_league_wave_7_melee.yml
- wave=7:spawnPoint=south:boss=wood_league_wave_7_melee.yml
- wave=7:spawnPoint=south:boss=wood_league_wave_7_ranged.yml
- wave=8:spawnPoint=north:boss=wood_league_wave_8_melee.yml
- wave=8:spawnPoint=north:boss=wood_league_wave_8_melee.yml
- wave=8:spawnPoint=north:boss=wood_league_wave_8_melee.yml
- wave=8:spawnPoint=south:boss=wood_league_wave_8_melee.yml
- wave=8:spawnPoint=south:boss=wood_league_wave_8_melee.yml
- wave=8:spawnPoint=south:boss=wood_league_wave_8_melee.yml
- wave=9:spawnPoint=north:boss=wood_league_wave_9_ranged.yml
- wave=9:spawnPoint=north:boss=wood_league_wave_9_ranged.yml
- wave=9:spawnPoint=north:boss=wood_league_wave_9_ranged.yml
- wave=9:spawnPoint=south:boss=wood_league_wave_9_ranged.yml
- wave=9:spawnPoint=south:boss=wood_league_wave_9_ranged.yml
- wave=9:spawnPoint=south:boss=wood_league_wave_9_ranged.yml
- wave=10:spawnPoint=center:boss=wood_league_wave_10_boss.yml
- wave=11:spawnPoint=north:boss=wood_league_wave_11_melee.yml
- wave=11:spawnPoint=north:boss=wood_league_wave_11_melee.yml
- wave=11:spawnPoint=north:boss=wood_league_wave_11_ranged.yml
- wave=11:spawnPoint=south:boss=wood_league_wave_11_melee.yml
- wave=11:spawnPoint=south:boss=wood_league_wave_11_melee.yml
- wave=11:spawnPoint=south:boss=wood_league_wave_11_ranged.yml
- wave=12:spawnPoint=north:boss=wood_league_wave_12_melee.yml
- wave=12:spawnPoint=north:boss=wood_league_wave_12_melee.yml
- wave=12:spawnPoint=north:boss=wood_league_wave_12_ranged.yml
- wave=12:spawnPoint=south:boss=wood_league_wave_12_melee.yml
- wave=12:spawnPoint=south:boss=wood_league_wave_12_melee.yml
- wave=12:spawnPoint=south:boss=wood_league_wave_12_ranged.yml
- wave=12:spawnPoint=east:boss=wood_league_wave_12_melee.yml
- wave=12:spawnPoint=east:boss=wood_league_wave_12_melee.yml
- wave=12:spawnPoint=east:boss=wood_league_wave_12_ranged.yml
- wave=13:spawnPoint=north:boss=wood_league_wave_13_melee.yml
- wave=13:spawnPoint=north:boss=wood_league_wave_13_melee.yml
- wave=13:spawnPoint=north:boss=wood_league_wave_13_ranged.yml
- wave=13:spawnPoint=south:boss=wood_league_wave_13_melee.yml
- wave=13:spawnPoint=south:boss=wood_league_wave_13_melee.yml
- wave=13:spawnPoint=south:boss=wood_league_wave_13_ranged.yml
- wave=13:spawnPoint=east:boss=wood_league_wave_13_melee.yml
- wave=13:spawnPoint=east:boss=wood_league_wave_13_melee.yml
- wave=13:spawnPoint=east:boss=wood_league_wave_13_ranged.yml
- wave=14:spawnPoint=north:boss=wood_league_wave_14_melee.yml
- wave=14:spawnPoint=north:boss=wood_league_wave_14_melee.yml
- wave=14:spawnPoint=north:boss=wood_league_wave_14_ranged.yml
- wave=14:spawnPoint=south:boss=wood_league_wave_14_melee.yml
- wave=14:spawnPoint=south:boss=wood_league_wave_14_melee.yml
- wave=14:spawnPoint=south:boss=wood_league_wave_14_ranged.yml
- wave=14:spawnPoint=east:boss=wood_league_wave_14_melee.yml
- wave=14:spawnPoint=east:boss=wood_league_wave_14_melee.yml
- wave=14:spawnPoint=east:boss=wood_league_wave_14_ranged.yml
- wave=15:spawnPoint=center:boss=wood_league_wave_15_miniboss.yml
- wave=16:spawnPoint=north:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=north:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=north:boss=wood_league_wave_16_ranged.yml
- wave=16:spawnPoint=south:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=south:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=south:boss=wood_league_wave_16_ranged.yml
- wave=16:spawnPoint=east:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=east:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=east:boss=wood_league_wave_16_ranged.yml
- wave=16:spawnPoint=west:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=west:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=west:boss=wood_league_wave_16_ranged.yml
- wave=16:spawnPoint=northeast:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=northeast:boss=wood_league_wave_16_melee.yml
- wave=16:spawnPoint=northeast:boss=wood_league_wave_16_ranged.yml
- wave=17:spawnPoint=north:boss=wood_league_wave_17_melee.yml
- wave=17:spawnPoint=north:boss=wood_league_wave_17_melee.yml
- wave=17:spawnPoint=north:boss=wood_league_wave_17_ranged.yml
- wave=17:spawnPoint=south:boss=wood_league_wave_17_melee.yml
- wave=17:spawnPoint=south:boss=wood_league_wave_17_melee.yml
- wave=17:spawnPoint=south:boss=wood_league_wave_17_ranged.yml
- wave=17:spawnPoint=east:boss=wood_league_wave_17_melee.yml
- wave=17:spawnPoint=east:boss=wood_league_wave_17_melee.yml
- wave=17:spawnPoint=east:boss=wood_league_wave_17_ranged.yml
- wave=18:spawnPoint=north:boss=wood_league_wave_18_melee.yml
- wave=18:spawnPoint=north:boss=wood_league_wave_18_melee.yml
- wave=18:spawnPoint=north:boss=wood_league_wave_18_melee.yml
- wave=18:spawnPoint=south:boss=wood_league_wave_18_melee.yml
- wave=18:spawnPoint=south:boss=wood_league_wave_18_melee.yml
- wave=18:spawnPoint=south:boss=wood_league_wave_18_melee.yml
- wave=18:spawnPoint=east:boss=wood_league_wave_18_melee.yml
- wave=18:spawnPoint=east:boss=wood_league_wave_18_melee.yml
- wave=18:spawnPoint=east:boss=wood_league_wave_18_melee.yml
- wave=19:spawnPoint=north:boss=wood_league_wave_19_ranged.yml
- wave=19:spawnPoint=north:boss=wood_league_wave_19_ranged.yml
- wave=19:spawnPoint=north:boss=wood_league_wave_19_ranged.yml
- wave=19:spawnPoint=south:boss=wood_league_wave_19_ranged.yml
- wave=19:spawnPoint=south:boss=wood_league_wave_19_ranged.yml
- wave=19:spawnPoint=south:boss=wood_league_wave_19_ranged.yml
- wave=19:spawnPoint=east:boss=wood_league_wave_19_ranged.yml
- wave=19:spawnPoint=east:boss=wood_league_wave_19_ranged.yml
- wave=19:spawnPoint=east:boss=wood_league_wave_19_ranged.yml
- wave=20:spawnPoint=center:boss=wood_league_wave_20_boss.yml
- wave=21:spawnPoint=north:boss=wood_league_wave_21_melee.yml
- wave=21:spawnPoint=north:boss=wood_league_wave_21_melee.yml
- wave=21:spawnPoint=north:boss=wood_league_wave_21_ranged.yml
- wave=21:spawnPoint=south:boss=wood_league_wave_21_melee.yml
- wave=21:spawnPoint=south:boss=wood_league_wave_21_melee.yml
- wave=21:spawnPoint=south:boss=wood_league_wave_21_ranged.yml
- wave=21:spawnPoint=east:boss=wood_league_wave_21_melee.yml
- wave=21:spawnPoint=east:boss=wood_league_wave_21_melee.yml
- wave=21:spawnPoint=east:boss=wood_league_wave_21_ranged.yml
- wave=22:spawnPoint=north:boss=wood_league_wave_22_melee.yml
- wave=22:spawnPoint=north:boss=wood_league_wave_22_melee.yml
- wave=22:spawnPoint=north:boss=wood_league_wave_22_ranged.yml
- wave=22:spawnPoint=south:boss=wood_league_wave_22_melee.yml
- wave=22:spawnPoint=south:boss=wood_league_wave_22_melee.yml
- wave=22:spawnPoint=south:boss=wood_league_wave_22_ranged.yml
- wave=22:spawnPoint=east:boss=wood_league_wave_22_melee.yml
- wave=22:spawnPoint=east:boss=wood_league_wave_22_melee.yml
- wave=22:spawnPoint=east:boss=wood_league_wave_22_ranged.yml
- wave=22:spawnPoint=west:boss=wood_league_wave_22_melee.yml
- wave=22:spawnPoint=west:boss=wood_league_wave_22_melee.yml
- wave=22:spawnPoint=west:boss=wood_league_wave_22_ranged.yml
- wave=23:spawnPoint=north:boss=wood_league_wave_23_melee.yml
- wave=23:spawnPoint=north:boss=wood_league_wave_23_melee.yml
- wave=23:spawnPoint=north:boss=wood_league_wave_23_ranged.yml
- wave=23:spawnPoint=south:boss=wood_league_wave_23_melee.yml
- wave=23:spawnPoint=south:boss=wood_league_wave_23_melee.yml
- wave=23:spawnPoint=south:boss=wood_league_wave_23_ranged.yml
- wave=23:spawnPoint=east:boss=wood_league_wave_23_melee.yml
- wave=23:spawnPoint=east:boss=wood_league_wave_23_melee.yml
- wave=23:spawnPoint=east:boss=wood_league_wave_23_ranged.yml
- wave=23:spawnPoint=west:boss=wood_league_wave_23_melee.yml
- wave=23:spawnPoint=west:boss=wood_league_wave_23_melee.yml
- wave=23:spawnPoint=west:boss=wood_league_wave_23_ranged.yml
- wave=24:spawnPoint=north:boss=wood_league_wave_24_melee.yml
- wave=24:spawnPoint=north:boss=wood_league_wave_24_melee.yml
- wave=24:spawnPoint=north:boss=wood_league_wave_24_ranged.yml
- wave=24:spawnPoint=south:boss=wood_league_wave_24_melee.yml
- wave=24:spawnPoint=south:boss=wood_league_wave_24_melee.yml
- wave=24:spawnPoint=south:boss=wood_league_wave_24_ranged.yml
- wave=24:spawnPoint=east:boss=wood_league_wave_24_melee.yml
- wave=24:spawnPoint=east:boss=wood_league_wave_24_melee.yml
- wave=24:spawnPoint=east:boss=wood_league_wave_24_ranged.yml
- wave=24:spawnPoint=west:boss=wood_league_wave_24_melee.yml
- wave=24:spawnPoint=west:boss=wood_league_wave_24_melee.yml
- wave=24:spawnPoint=west:boss=wood_league_wave_24_ranged.yml
- wave=25:spawnPoint=center:boss=wood_league_wave_25_miniboss.yml
- wave=26:spawnPoint=north:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=north:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=north:boss=wood_league_wave_26_ranged.yml
- wave=26:spawnPoint=south:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=south:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=south:boss=wood_league_wave_26_ranged.yml
- wave=26:spawnPoint=east:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=east:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=east:boss=wood_league_wave_26_ranged.yml
- wave=26:spawnPoint=west:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=west:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=west:boss=wood_league_wave_26_ranged.yml
- wave=26:spawnPoint=northeast:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=northeast:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=northeast:boss=wood_league_wave_26_ranged.yml
- wave=26:spawnPoint=southwest:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=southwest:boss=wood_league_wave_26_melee.yml
- wave=26:spawnPoint=southwest:boss=wood_league_wave_26_ranged.yml
- wave=27:spawnPoint=north:boss=wood_league_wave_27_melee.yml
- wave=27:spawnPoint=north:boss=wood_league_wave_27_melee.yml
- wave=27:spawnPoint=north:boss=wood_league_wave_27_ranged.yml
- wave=27:spawnPoint=south:boss=wood_league_wave_27_melee.yml
- wave=27:spawnPoint=south:boss=wood_league_wave_27_melee.yml
- wave=27:spawnPoint=south:boss=wood_league_wave_27_ranged.yml
- wave=27:spawnPoint=east:boss=wood_league_wave_27_melee.yml
- wave=27:spawnPoint=east:boss=wood_league_wave_27_melee.yml
- wave=27:spawnPoint=east:boss=wood_league_wave_27_ranged.yml
- wave=27:spawnPoint=west:boss=wood_league_wave_27_melee.yml
- wave=27:spawnPoint=west:boss=wood_league_wave_27_melee.yml
- wave=27:spawnPoint=west:boss=wood_league_wave_27_ranged.yml
- wave=28:spawnPoint=north:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=north:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=north:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=south:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=south:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=south:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=east:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=east:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=east:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=west:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=west:boss=wood_league_wave_28_melee.yml
- wave=28:spawnPoint=west:boss=wood_league_wave_28_melee.yml
- wave=29:spawnPoint=north:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=north:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=north:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=south:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=south:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=south:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=east:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=east:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=east:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=west:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=west:boss=wood_league_wave_29_ranged.yml
- wave=29:spawnPoint=west:boss=wood_league_wave_29_ranged.yml
- wave=30:spawnPoint=center:boss=wood_league_wave_30_boss.yml
- wave=31:spawnPoint=north:boss=wood_league_wave_31_ranged.yml
- wave=31:spawnPoint=north:boss=wood_league_wave_31_melee.yml
- wave=31:spawnPoint=north:boss=wood_league_wave_31_melee.yml
- wave=31:spawnPoint=south:boss=wood_league_wave_31_ranged.yml
- wave=31:spawnPoint=south:boss=wood_league_wave_31_melee.yml
- wave=31:spawnPoint=south:boss=wood_league_wave_31_melee.yml
- wave=31:spawnPoint=east:boss=wood_league_wave_31_ranged.yml
- wave=31:spawnPoint=east:boss=wood_league_wave_31_melee.yml
- wave=31:spawnPoint=east:boss=wood_league_wave_31_melee.yml
- wave=31:spawnPoint=west:boss=wood_league_wave_31_ranged.yml
- wave=31:spawnPoint=west:boss=wood_league_wave_31_melee.yml
- wave=31:spawnPoint=west:boss=wood_league_wave_31_melee.yml
- wave=32:spawnPoint=north:boss=wood_league_wave_32_ranged.yml
- wave=32:spawnPoint=north:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=north:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=south:boss=wood_league_wave_32_ranged.yml
- wave=32:spawnPoint=south:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=south:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=east:boss=wood_league_wave_32_ranged.yml
- wave=32:spawnPoint=east:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=east:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=west:boss=wood_league_wave_32_ranged.yml
- wave=32:spawnPoint=west:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=west:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=northeast:boss=wood_league_wave_32_ranged.yml
- wave=32:spawnPoint=northeast:boss=wood_league_wave_32_melee.yml
- wave=32:spawnPoint=northeast:boss=wood_league_wave_32_melee.yml
- wave=33:spawnPoint=north:boss=wood_league_wave_33_ranged.yml
- wave=33:spawnPoint=north:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=north:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=south:boss=wood_league_wave_33_ranged.yml
- wave=33:spawnPoint=south:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=south:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=east:boss=wood_league_wave_33_ranged.yml
- wave=33:spawnPoint=east:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=east:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=west:boss=wood_league_wave_33_ranged.yml
- wave=33:spawnPoint=west:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=west:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=northeast:boss=wood_league_wave_33_ranged.yml
- wave=33:spawnPoint=northeast:boss=wood_league_wave_33_melee.yml
- wave=33:spawnPoint=northeast:boss=wood_league_wave_33_melee.yml
- wave=34:spawnPoint=north:boss=wood_league_wave_34_ranged.yml
- wave=34:spawnPoint=north:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=north:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=south:boss=wood_league_wave_34_ranged.yml
- wave=34:spawnPoint=south:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=south:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=east:boss=wood_league_wave_34_ranged.yml
- wave=34:spawnPoint=east:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=east:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=west:boss=wood_league_wave_34_ranged.yml
- wave=34:spawnPoint=west:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=west:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=northeast:boss=wood_league_wave_34_ranged.yml
- wave=34:spawnPoint=northeast:boss=wood_league_wave_34_melee.yml
- wave=34:spawnPoint=northeast:boss=wood_league_wave_34_melee.yml
- wave=35:spawnPoint=center:boss=wood_league_wave_35_miniboss.yml
- wave=36:spawnPoint=north:boss=wood_league_wave_36_ranged.yml
- wave=36:spawnPoint=north:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=north:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=south:boss=wood_league_wave_36_ranged.yml
- wave=36:spawnPoint=south:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=south:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=east:boss=wood_league_wave_36_ranged.yml
- wave=36:spawnPoint=east:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=east:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=west:boss=wood_league_wave_36_ranged.yml
- wave=36:spawnPoint=west:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=west:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=northeast:boss=wood_league_wave_36_ranged.yml
- wave=36:spawnPoint=northeast:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=northeast:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=southwest:boss=wood_league_wave_36_ranged.yml
- wave=36:spawnPoint=southwest:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=southwest:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=southeast:boss=wood_league_wave_36_ranged.yml
- wave=36:spawnPoint=southeast:boss=wood_league_wave_36_melee.yml
- wave=36:spawnPoint=southeast:boss=wood_league_wave_36_melee.yml
- wave=37:spawnPoint=north:boss=wood_league_wave_37_ranged.yml
- wave=37:spawnPoint=north:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=north:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=south:boss=wood_league_wave_37_ranged.yml
- wave=37:spawnPoint=south:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=south:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=east:boss=wood_league_wave_37_ranged.yml
- wave=37:spawnPoint=east:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=east:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=west:boss=wood_league_wave_37_ranged.yml
- wave=37:spawnPoint=west:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=west:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=northeast:boss=wood_league_wave_37_ranged.yml
- wave=37:spawnPoint=northeast:boss=wood_league_wave_37_melee.yml
- wave=37:spawnPoint=northeast:boss=wood_league_wave_37_melee.yml
- wave=38:spawnPoint=north:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=north:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=north:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=south:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=south:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=south:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=east:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=east:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=east:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=west:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=west:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=west:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=northeast:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=northeast:boss=wood_league_wave_38_melee.yml
- wave=38:spawnPoint=northeast:boss=wood_league_wave_38_melee.yml
- wave=39:spawnPoint=north:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=north:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=north:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=south:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=south:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=south:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=east:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=east:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=east:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=west:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=west:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=west:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=northeast:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=northeast:boss=wood_league_wave_39_ranged.yml
- wave=39:spawnPoint=northeast:boss=wood_league_wave_39_ranged.yml
- wave=40:spawnPoint=center:boss=wood_league_wave_40_boss.yml
- wave=41:spawnPoint=north:boss=wood_league_wave_41_ranged.yml
- wave=41:spawnPoint=north:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=north:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=south:boss=wood_league_wave_41_ranged.yml
- wave=41:spawnPoint=south:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=south:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=east:boss=wood_league_wave_41_ranged.yml
- wave=41:spawnPoint=east:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=east:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=west:boss=wood_league_wave_41_ranged.yml
- wave=41:spawnPoint=west:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=west:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=northeast:boss=wood_league_wave_41_ranged.yml
- wave=41:spawnPoint=northeast:boss=wood_league_wave_41_melee.yml
- wave=41:spawnPoint=northeast:boss=wood_league_wave_41_melee.yml
- wave=42:spawnPoint=north:boss=wood_league_wave_42_ranged.yml
- wave=42:spawnPoint=north:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=north:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=north:boss=wood_league_wave_42_healer.yml
- wave=42:spawnPoint=south:boss=wood_league_wave_42_ranged.yml
- wave=42:spawnPoint=south:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=south:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=south:boss=wood_league_wave_42_healer.yml
- wave=42:spawnPoint=east:boss=wood_league_wave_42_ranged.yml
- wave=42:spawnPoint=east:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=east:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=east:boss=wood_league_wave_42_healer.yml
- wave=42:spawnPoint=west:boss=wood_league_wave_42_ranged.yml
- wave=42:spawnPoint=west:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=west:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=west:boss=wood_league_wave_42_healer.yml
- wave=42:spawnPoint=northeast:boss=wood_league_wave_42_ranged.yml
- wave=42:spawnPoint=northeast:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=northeast:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=northeast:boss=wood_league_wave_42_healer.yml
- wave=42:spawnPoint=southwest:boss=wood_league_wave_42_ranged.yml
- wave=42:spawnPoint=southwest:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=southwest:boss=wood_league_wave_42_melee.yml
- wave=42:spawnPoint=southwest:boss=wood_league_wave_42_healer.yml
- wave=43:spawnPoint=north:boss=wood_league_wave_43_ranged.yml
- wave=43:spawnPoint=north:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=north:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=north:boss=wood_league_wave_43_healer.yml
- wave=43:spawnPoint=south:boss=wood_league_wave_43_ranged.yml
- wave=43:spawnPoint=south:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=south:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=south:boss=wood_league_wave_43_healer.yml
- wave=43:spawnPoint=east:boss=wood_league_wave_43_ranged.yml
- wave=43:spawnPoint=east:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=east:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=east:boss=wood_league_wave_43_healer.yml
- wave=43:spawnPoint=west:boss=wood_league_wave_43_ranged.yml
- wave=43:spawnPoint=west:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=west:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=west:boss=wood_league_wave_43_healer.yml
- wave=43:spawnPoint=northeast:boss=wood_league_wave_43_ranged.yml
- wave=43:spawnPoint=northeast:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=northeast:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=northeast:boss=wood_league_wave_43_healer.yml
- wave=43:spawnPoint=southwest:boss=wood_league_wave_43_ranged.yml
- wave=43:spawnPoint=southwest:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=southwest:boss=wood_league_wave_43_melee.yml
- wave=43:spawnPoint=southwest:boss=wood_league_wave_43_healer.yml
- wave=44:spawnPoint=north:boss=wood_league_wave_44_ranged.yml
- wave=44:spawnPoint=north:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=north:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=north:boss=wood_league_wave_44_healer.yml
- wave=44:spawnPoint=south:boss=wood_league_wave_44_ranged.yml
- wave=44:spawnPoint=south:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=south:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=south:boss=wood_league_wave_44_healer.yml
- wave=44:spawnPoint=east:boss=wood_league_wave_44_ranged.yml
- wave=44:spawnPoint=east:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=east:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=east:boss=wood_league_wave_44_healer.yml
- wave=44:spawnPoint=west:boss=wood_league_wave_44_ranged.yml
- wave=44:spawnPoint=west:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=west:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=west:boss=wood_league_wave_44_healer.yml
- wave=44:spawnPoint=northeast:boss=wood_league_wave_44_ranged.yml
- wave=44:spawnPoint=northeast:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=northeast:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=northeast:boss=wood_league_wave_44_healer.yml
- wave=44:spawnPoint=southwest:boss=wood_league_wave_44_ranged.yml
- wave=44:spawnPoint=southwest:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=southwest:boss=wood_league_wave_44_melee.yml
- wave=44:spawnPoint=southwest:boss=wood_league_wave_44_healer.yml
- wave=45:spawnPoint=center:boss=wood_league_wave_45_miniboss.yml
- wave=46:spawnPoint=north:boss=wood_league_wave_46_ranged.yml
- wave=46:spawnPoint=north:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=north:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=north:boss=wood_league_wave_46_healer.yml
- wave=46:spawnPoint=south:boss=wood_league_wave_46_ranged.yml
- wave=46:spawnPoint=south:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=south:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=south:boss=wood_league_wave_46_healer.yml
- wave=46:spawnPoint=east:boss=wood_league_wave_46_ranged.yml
- wave=46:spawnPoint=east:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=east:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=east:boss=wood_league_wave_46_healer.yml
- wave=46:spawnPoint=west:boss=wood_league_wave_46_ranged.yml
- wave=46:spawnPoint=west:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=west:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=west:boss=wood_league_wave_46_healer.yml
- wave=46:spawnPoint=northeast:boss=wood_league_wave_46_ranged.yml
- wave=46:spawnPoint=northeast:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=northeast:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=northeast:boss=wood_league_wave_46_healer.yml
- wave=46:spawnPoint=southwest:boss=wood_league_wave_46_ranged.yml
- wave=46:spawnPoint=southwest:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=southwest:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=southwest:boss=wood_league_wave_46_healer.yml
- wave=46:spawnPoint=northwest:boss=wood_league_wave_46_ranged.yml
- wave=46:spawnPoint=northwest:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=northwest:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=northwest:boss=wood_league_wave_46_healer.yml
- wave=46:spawnPoint=southeast:boss=wood_league_wave_46_ranged.yml
- wave=46:spawnPoint=southeast:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=southeast:boss=wood_league_wave_46_melee.yml
- wave=46:spawnPoint=southeast:boss=wood_league_wave_46_healer.yml
- wave=47:spawnPoint=north:boss=wood_league_wave_47_ranged.yml
- wave=47:spawnPoint=north:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=north:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=north:boss=wood_league_wave_47_healer.yml
- wave=47:spawnPoint=south:boss=wood_league_wave_47_ranged.yml
- wave=47:spawnPoint=south:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=south:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=south:boss=wood_league_wave_47_healer.yml
- wave=47:spawnPoint=east:boss=wood_league_wave_47_ranged.yml
- wave=47:spawnPoint=east:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=east:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=east:boss=wood_league_wave_47_healer.yml
- wave=47:spawnPoint=west:boss=wood_league_wave_47_ranged.yml
- wave=47:spawnPoint=west:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=west:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=west:boss=wood_league_wave_47_healer.yml
- wave=47:spawnPoint=northeast:boss=wood_league_wave_47_ranged.yml
- wave=47:spawnPoint=northeast:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=northeast:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=northeast:boss=wood_league_wave_47_healer.yml
- wave=47:spawnPoint=southwest:boss=wood_league_wave_47_ranged.yml
- wave=47:spawnPoint=southwest:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=southwest:boss=wood_league_wave_47_melee.yml
- wave=47:spawnPoint=southwest:boss=wood_league_wave_47_healer.yml
- wave=48:spawnPoint=north:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=north:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=north:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=north:boss=wood_league_wave_48_healer.yml
- wave=48:spawnPoint=south:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=south:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=south:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=south:boss=wood_league_wave_48_healer.yml
- wave=48:spawnPoint=east:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=east:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=east:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=east:boss=wood_league_wave_48_healer.yml
- wave=48:spawnPoint=west:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=west:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=west:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=west:boss=wood_league_wave_48_healer.yml
- wave=48:spawnPoint=northeast:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=northeast:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=northeast:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=northeast:boss=wood_league_wave_48_healer.yml
- wave=48:spawnPoint=southwest:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=southwest:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=southwest:boss=wood_league_wave_48_melee.yml
- wave=48:spawnPoint=southwest:boss=wood_league_wave_48_healer.yml
- wave=49:spawnPoint=north:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=north:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=north:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=north:boss=wood_league_wave_49_healer.yml
- wave=49:spawnPoint=south:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=south:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=south:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=south:boss=wood_league_wave_49_healer.yml
- wave=49:spawnPoint=east:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=east:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=east:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=east:boss=wood_league_wave_49_healer.yml
- wave=49:spawnPoint=west:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=west:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=west:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=west:boss=wood_league_wave_49_healer.yml
- wave=49:spawnPoint=northeast:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=northeast:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=northeast:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=northeast:boss=wood_league_wave_49_healer.yml
- wave=49:spawnPoint=southwest:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=southwest:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=southwest:boss=wood_league_wave_49_ranged.yml
- wave=49:spawnPoint=southwest:boss=wood_league_wave_49_healer.yml
- wave=50:spawnPoint=center:boss=wood_league_wave_50_boss.yml
rawArenaReward:
- filename=enchanted_book_protection_environmental.yml:wave=10:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=10:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=10:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=10:chance=0.01
- filename=enchanted_book_damage_all.yml:wave=10:chance=0.01
- filename=enchanted_book_arrow_damage.yml:wave=10:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=20:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=20:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=20:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=20:chance=0.01
- filename=enchanted_book_damage_all.yml:wave=20:chance=0.01
- filename=enchanted_book_arrow_damage.yml:wave=20:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=30:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=30:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=30:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=30:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=30:chance=0.01
- filename=enchanted_book_arrow_damage.yml:wave=30:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=40:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=40:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=40:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=40:chance=0.01
- filename=enchanted_book_damage_all.yml:wave=40:chance=0.01
- filename=enchanted_book_arrow_damage.yml:wave=40:chance=0.01
- filename=enchanted_book_protection_environmental.yml:wave=50:chance=0.1
- filename=enchanted_book_protection_environmental.yml:wave=50:chance=0.1
- filename=enchanted_book_protection_environmental.yml:wave=50:chance=0.1
- filename=enchanted_book_protection_environmental.yml:wave=50:chance=0.1
- filename=enchanted_book_damage_all.yml:wave=50:chance=0.1
- filename=enchanted_book_arrow_damage.yml:wave=50:chance=0.1
- filename=elite_scrap_tiny.yml:wave=5:chance=1:amount=5
- filename=elite_scrap_tiny.yml:wave=10:chance=1:amount=5
- filename=elite_scrap_small.yml:wave=15:chance=1:amount=5
- filename=elite_scrap_small.yml:wave=20:chance=1:amount=5
- filename=elite_scrap_small.yml:wave=25:chance=1:amount=5
- filename=elite_scrap_large.yml:wave=30:chance=1:amount=5
- filename=elite_scrap_large.yml:wave=35:chance=1:amount=5
- filename=elite_scrap_large.yml:wave=40:chance=1:amount=5
- filename=elite_scrap_large.yml:wave=45:chance=1:amount=5
- filename=elite_scrap_huge.yml:wave=50:chance=1:amount=5
- currencyAmount=2:wave=1
- currencyAmount=5:wave=2
- currencyAmount=10:wave=3
- currencyAmount=15:wave=4
- currencyAmount=20:wave=5
- currencyAmount=25:wave=6
- currencyAmount=30:wave=7
- currencyAmount=35:wave=8
- currencyAmount=40:wave=9
- currencyAmount=45:wave=10
- currencyAmount=50:wave=11
- currencyAmount=55:wave=12
- currencyAmount=60:wave=13
- currencyAmount=65:wave=14
- currencyAmount=70:wave=15
- currencyAmount=75:wave=16
- currencyAmount=80:wave=17
- currencyAmount=85:wave=18
- currencyAmount=90:wave=19
- currencyAmount=95:wave=20
- currencyAmount=100:wave=21
- currencyAmount=105:wave=22
- currencyAmount=110:wave=23
- currencyAmount=115:wave=24
- currencyAmount=120:wave=25
- currencyAmount=125:wave=26
- currencyAmount=130:wave=27
- currencyAmount=135:wave=28
- currencyAmount=140:wave=29
- currencyAmount=145:wave=30
- currencyAmount=150:wave=31
- currencyAmount=155:wave=32
- currencyAmount=160:wave=33
- currencyAmount=165:wave=34
- currencyAmount=170:wave=35
- currencyAmount=175:wave=36
- currencyAmount=180:wave=37
- currencyAmount=185:wave=38
- currencyAmount=190:wave=39
- currencyAmount=195:wave=40
- currencyAmount=200:wave=41
- currencyAmount=205:wave=42
- currencyAmount=210:wave=43
- currencyAmount=215:wave=44
- currencyAmount=220:wave=45
- currencyAmount=225:wave=46
- currencyAmount=230:wave=47
- currencyAmount=235:wave=48
- currencyAmount=240:wave=49
- currencyAmount=250:wave=50
- level=10:filename=summon_merchant_scroll.yml:wave=10
- filename=invictus_sword.yml:itemlevel=50:wave=50:chance=0.2
- filename=invictus_helmet.yml:itemlevel=50:wave=50:chance=0.2
- filename=invictus_chestplate.yml:itemlevel=50:wave=50:chance=0.2
- filename=invictus_leggings.yml:itemlevel=50:wave=50:chance=0.2
- filename=invictus_boots.yml:itemlevel=50:wave=50:chance=0.2
- filename=invictus_pickaxe.yml:itemlevel=50:wave=50:chance=0.1
- filename=invictus_shovel.yml:itemlevel=50:wave=50:chance=0.1
- filename=challengers_sword.yml:itemlevel=40:wave=40:chance=0.2
- filename=challengers_helmet.yml:itemlevel=40:wave=40:chance=0.2
- filename=challengers_chestplate.yml:itemlevel=40:wave=40:chance=0.2
- filename=challengers_leggings.yml:itemlevel=40:wave=40:chance=0.2
- filename=challengers_boots.yml:itemlevel=40:wave=40:chance=0.2
- filename=veterans_sword.yml:itemlevel=30:wave=30:chance=0.2
- filename=veterans_helmet.yml:itemlevel=30:wave=30:chance=0.2
- filename=veterans_chestplate.yml:itemlevel=30:wave=30:chance=0.2
- filename=veterans_leggings.yml:itemlevel=30:wave=30:chance=0.2
- filename=veterans_boots.yml:itemlevel=30:wave=30:chance=0.2
- filename=grunts_sword.yml:itemlevel=20:wave=20:chance=0.2
- filename=grunts_helmet.yml:itemlevel=20:wave=20:chance=0.2
- filename=grunts_chestplate.yml:itemlevel=20:wave=20:chance=0.2
- filename=grunts_leggings.yml:itemlevel=20:wave=20:chance=0.2
- filename=grunts_boots.yml:itemlevel=20:wave=20:chance=0.2
- filename=novices_sword.yml:itemlevel=10:wave=10:chance=0.2
- filename=novices_helmet.yml:itemlevel=10:wave=10:chance=0.2
- filename=novices_chestplate.yml:itemlevel=10:wave=10:chance=0.2
- filename=novices_leggings.yml:itemlevel=10:wave=10:chance=0.2
- filename=novices_boots.yml:itemlevel=10:wave=10:chance=0.2
maximumPlayerCount: 5
arenaMessages:
- wave=1:message=&8[Gladius] &fToday's main event! A legendary fighter came all the
  way to the arena to test his might against the toughest monsters from all the land!
  But before that, here's a rookie fighting some zombies.
- wave=2:message=&8[Gladius] &fWould you look at that, they at least know how to wave
  their arms! That was just the warmup, let's start putting on some pressure!
- wave=3:message=&8[Gladius] &fSo you think you know how to fight? Well this wave
  will put the &cheat &fon! Ha ha ha ha ha!
- wave=4:message=&8[Gladius] &fAlright I guess they can take the heat! Ladies and
  gentlemen, strap on, we might be in for a show!
- wave=5:message=&8[Gladius] &fI found this &cweird creature &foutside, and I thought
  putting it in the arena would be funny!
- wave=6:message=&8[Gladius] &fSeems like you can handle small groups of enemies just
  fine, &clet's try scaling that up!
- wave=7:message=&8[Gladius] &fSo you think you can truly challenge the arena? We
  shall see...
- wave=8:message=&8[Gladius] &fNot bad, but what if every enemy is a &cmelee &fenemy?
- wave=9:message=&8[Gladius] &fNice, nice, but what if they're all &cranged &finstead?
- wave=10:message=&8[Gladius] &fCareful now, a &cbad doggo &fseems to have wandered
  into the arena! Where did it even come from?
- wave=11:message=&8[Gladius] &fAlright folks we got animal control to deal with the
  bad doggos, we're back in business!
- wave=12:message=&8[Gladius] &fIt seems like the audience is clamoring for &cmore&f!
  Well, let's give it to them!
- wave=13:message=&8[Gladius] &fSeems like the custodians got bored and gave the arena
  creatures &2poison&f! Let's see where this goes...
- wave=14:message=&8[Gladius] &fWell that was... boring! Whoever gave those guys poison
  is fired! Come up with something better next time!
- wave=15:message=&8[Gladius] &fAnd here we have this year's prize-winning &4hog&f!
  It got first place for ugliness and meanness! Everyone give a round of applause
  for &4Mr. Oinkers&f!
- wave=16:message=&8[Gladius] &fMr. Oinkers is now bacon! Here comes &cMr. Oinker's
  fans&f, flooding the arena to get their revenge!
- wave=17:message=&8[Gladius] &fIt would seem as though our arena challenger thinks
  they have a shot! But can they deal with even more &cpowerful &ffoes?
- wave=18:message=&8[Gladius] &fThe &cfencing club &fand the &carchery club &fare
  having a contest! Let's see which one does better!
- wave=19:message=&8[Gladius] &fThe fencing club didn't so so hot, let's see what
  the &carchery club&f has for us!
- wave=20:message=&8[Gladius] &fSeems like the archery club didn't do so hot either!
  It seems like our challenger has begun attracting the attention of some of our top
  gladiators! Introducing &2Zoltan, the Pride of the Zombies&f!
- wave=21:message=&8[Gladius] &fUnbelievable! Seems like this rookie defeated Zoltan!
  Do we have a potential new champion in our hands?
- wave=22:message=&8[Gladius] &fSpectators are starting to place their bets! How far
  can today's contestant go?
- wave=23:message=&8[Gladius] &fDon't look away now, this wave's enemies all have
  &cwithering &fattacks!
- wave=24:message=&8[Gladius] &fSeems like some pretty big arena names are starting
  to arrive to stop this challenger! Stay tuned!
- wave=25:message=&8[Gladius] &fHere comes &bAgdluak&f, the archer from the Great
  North! Beware of its &bfrost&f!
- wave=26:message=&8[Gladius] &fThat was a pretty cool fight! I am &cswarming &fwith
  excitement to see what's next!
- wave=27:message=&8[Gladius] &fI've been told the arena grows harder! &cZombies immune
  to arrows, skeletons that freeze their opponents &f- what will they think of next!?
- wave=28:message=&8[Gladius] &fIt's time to show off those &cfencing &fskills! Remember,
  it's all about the legwork!
- wave=29:message=&8[Gladius] &fAnd now, let's have some &ctarget practice&f! The
  challenger is the target!
- wave=30:message=&8[Gladius] &fA true arena champion approaches! The &2Southern Viper
  &fonce again seeks to &cpoison &ftheir enemies to death with his &cthousand poisons&f!
- wave=31:message=&8[Gladius] &fThe Southern Viper has fallen! The crowd goes wild!
  What a day, ladies and gentlemen, what a day!
- wave=32:message=&8[Gladius] &fI have been told that the arena custodians are done
  messing around and are staring to let loose! Can our challenger survive this?
- wave=33:message=&8[Gladius] &fSeems like some enemies got their hands on some of
  Southern Viper's &cpoisons&f! You do not want to get hit by those, ladies and gentlemen!
- wave=34:message=&8[Gladius] &fBobby, make sure you put 1,000 Elite Coins on these
  contestants dying for me - no, no, 1,000 Elite Coins - wait was this thing on? I
  mean, onwards to the next wave!
- wave=35:message=&8[Gladius] &fLadies and gentlemen, what a rare treat! It would
  seems as the very &aWood League Jester &fhas stepped up to fight! What delightful
  &2tricks &fdoes he have for us today?
- wave=36:message=&8[Gladius] &fWhat a lovely show ladies and gentlemen, give it up
  for &aThe Jester&f!
- wave=37:message=&8[Gladius] &fBobby, how is my bet doing? What do you mean 10,000
  Elite Coins, I told you to put 1,000 on there! Uh, pump them, &cpump the powers
  up&f!
- wave=38:message=&8[Gladius] &fBobby you're killing me here! Uh, dump some &cswordsmen
  &fon there, I need to deal with something real quick...
- wave=39:message=&8[Gladius] &fWhat do you mean no refunds? I didn't even want to
  - ahem, I mean, a legion of &carchers &fhave come from far to show off their skills!
- wave=40:message=&8[Gladius] &fLadies an gentlemen, for, uh, random reasons, I have
  invited the strongest fighter I can summon! Meet &4Crucios, the Bearer of Curses&f!
- wave=41:message=&8[Gladius] &fCrucios has been defeated!? What has the world come
  to?
- wave=42:message=&8[Gladius] &fA wealthy for now patron has decided to throw in free
  healthcare to arena enemies! Will they do better with &2healing&f?
- wave=43:message=&8[Gladius] &f&cWither&f, &cpoison, &cfire&f throw in everything
  you have! Do you even know how much is on the line!?
- wave=44:message=&8[Gladius] &fThis is clearly not working, let me think of something...
- wave=45:message=&8[Gladius] &fHere is one of the top gladiators of the Wood League
  Arena! &cThousand Blades &fapproaches once again with his &cinnumerable projectiles&f!
  He better win!
- wave=46:message=&8[Gladius] &fThousand Blades has fallen! It just seems like you
  can't get competent gladiators these days! That's fine, you'll see! You'll all see!
  &cRelease the swarm!
- wave=47:message=&8[Gladius] &fIf swarms don't work, throw in &cmore powers&f! Throw
  in all of the powers!
- wave=48:message=&8[Gladius] &fWe are starting to run out of options here! Are these
  all of the &cswordsmen &fwe have left?
- wave=49:message=&8[Gladius] &fAlright send all we have left, we have no other options!
  Move forth, &carchers&f!
- wave=50:message=&8[Gladius] &fI guess this is it for me... I have no option but
  to declare - wait, who is that in the distance? Is that &4Uther, the Champion of
  the Wood League Arena&f? Ladies and gentlemen, match set and over, our contestant
  has no chance of winning here! They shall be &csmitten &fby our champion!
- wave=51:message=&8[Gladius the Destitute] &fUnbelievable, Uther has been defeated!
  We have a new arena champion! I would be incredibly excited if I wasn't so terribly
  in debt now!
cylindricalArena: true
