luckperms.logs.actionlog-prefix=日志
luckperms.logs.verbose-prefix=权限检查
luckperms.logs.export-prefix=导出
luckperms.commandsystem.available-commands=使用 {0} 来查看可用的命令
luckperms.commandsystem.command-not-recognised=未知的命令
luckperms.commandsystem.no-permission=你没有使用此命令的权限\!
luckperms.commandsystem.no-permission-subcommands=你没有权限使用任何子命令
luckperms.commandsystem.already-executing-command=正在执行另一个命令，请等待完成...
luckperms.commandsystem.usage.sub-commands-header=子命令
luckperms.commandsystem.usage.usage-header=命令使用方法
luckperms.commandsystem.usage.arguments-header=参数
luckperms.first-time.no-permissions-setup=似乎还没有设置权限\!
luckperms.first-time.use-console-to-give-access=在游戏中使用 LuckPerms 命令之前, 你需要在控制台中给自己使用 LuckPerms 的权限
luckperms.first-time.console-command-prompt=打开控制台并执行下面的命令
luckperms.first-time.next-step=完成这些工作后, 你就可以开始定义权限分配和权限组啦
luckperms.first-time.wiki-prompt=不知道从哪里开始？点击这里查看\: {0}
luckperms.login.try-again=请稍后再试
luckperms.login.loading-database-error=在加载权限数据时发生了一个数据库错误
luckperms.login.server-admin-check-console-errors=如果你是服务器管理员, 请检查控制台是否有错误
luckperms.login.server-admin-check-console-info=请检查服务器控制台了解更多信息
luckperms.login.data-not-loaded-at-pre=在预登录阶段未能加载用户权限数据
luckperms.login.unable-to-continue=无法继续
luckperms.login.craftbukkit-offline-mode-error=这可能是由于 CraftBukkit 和 online-mode 设置之间的冲突
luckperms.login.unexpected-error=在设置权限数据时发生了一个意外的错误
luckperms.opsystem.disabled=此服务器禁用了原版OP系统
luckperms.opsystem.sponge-warning=请注意\: 当安装了权限插件时, 服务器OP身份对Sponge权限检查没有影响, 你必须直接编辑用户数据
luckperms.duration.unit.years.plural={0} 年
luckperms.duration.unit.years.singular={0} 年
luckperms.duration.unit.years.short={0}年
luckperms.duration.unit.months.plural={0} 月
luckperms.duration.unit.months.singular={0} 月
luckperms.duration.unit.months.short={0}月
luckperms.duration.unit.weeks.plural={0} 周
luckperms.duration.unit.weeks.singular={0} 周
luckperms.duration.unit.weeks.short={0}周
luckperms.duration.unit.days.plural={0} 天
luckperms.duration.unit.days.singular={0} 天
luckperms.duration.unit.days.short={0}天
luckperms.duration.unit.hours.plural={0} 小时
luckperms.duration.unit.hours.singular={0} 小时
luckperms.duration.unit.hours.short={0}时
luckperms.duration.unit.minutes.plural={0} 分钟
luckperms.duration.unit.minutes.singular={0} 分钟
luckperms.duration.unit.minutes.short={0}分
luckperms.duration.unit.seconds.plural={0} 秒
luckperms.duration.unit.seconds.singular={0} 秒
luckperms.duration.unit.seconds.short={0}秒
luckperms.duration.since={0} 前
luckperms.command.misc.invalid-code=无效代码
luckperms.command.misc.response-code-key=响应代码
luckperms.command.misc.error-message-key=消息
luckperms.command.misc.bytebin-unable-to-communicate=无法与 bytebin 通信
luckperms.command.misc.webapp-unable-to-communicate=无法与网页编辑器通信
luckperms.command.misc.check-console-for-errors=检查控制台是否有错误
luckperms.command.misc.file-must-be-in-data=文件 {0} 必须直接放在数据目录中
luckperms.command.misc.wait-to-finish=请等待它完成后再试一次
luckperms.command.misc.invalid-priority=无效的优先级 {0}
luckperms.command.misc.expected-number=需要输入数字
luckperms.command.misc.date-parse-error=无法解析日期 {0}
luckperms.command.misc.date-in-past-error=不能设置已经过去的日期\!
luckperms.command.misc.page=第 {0} 页, 共 {1} 页
luckperms.command.misc.page-entries={0} 项
luckperms.command.misc.none=无
luckperms.command.misc.loading.error.unexpected=发生了一个意外的错误
luckperms.command.misc.loading.error.user=未加载用户
luckperms.command.misc.loading.error.user-specific=无法加载目标用户 {0}
luckperms.command.misc.loading.error.user-not-found=无法找到 {0} 这个用户
luckperms.command.misc.loading.error.user-save-error=在保存 {0} 的用户数据时出现了错误
luckperms.command.misc.loading.error.user-not-online=用户 {0} 未在线
luckperms.command.misc.loading.error.user-invalid={0} 不是一个有效的用户名称或UUID
luckperms.command.misc.loading.error.user-not-uuid=目标用户 {0} 不是一个有效的UUID
luckperms.command.misc.loading.error.group=未加载权限组
luckperms.command.misc.loading.error.all-groups=无法加载所有权限组
luckperms.command.misc.loading.error.group-not-found=无法找到一个名为 {0} 的权限组
luckperms.command.misc.loading.error.group-save-error=在保存权限组 {0} 的数据时出现了错误
luckperms.command.misc.loading.error.group-invalid={0} 不是一个有效的权限组名称
luckperms.command.misc.loading.error.track=未加载权限组路线
luckperms.command.misc.loading.error.all-tracks=无法加载所有权限组路线
luckperms.command.misc.loading.error.track-not-found=无法找到一个名为 {0} 的权限组路线
luckperms.command.misc.loading.error.track-save-error=在保存权限组路线 {0} 的数据时出现了错误
luckperms.command.misc.loading.error.track-invalid={0} 不是一个有效的权限组路线名称
luckperms.command.editor.no-match=无法打开网页编辑器, 没有匹配到所需类型的对象
luckperms.command.editor.start=正在准备一个新的编辑会话, 请稍候...
luckperms.command.editor.url=点击下面的链接打开网页编辑器
luckperms.command.editor.unable-to-communicate=无法与网页编辑器通信
luckperms.command.editor.apply-edits.success=网页编辑器的数据已成功应用到 {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} 和 {2} {3}
luckperms.command.editor.apply-edits.success.additions=新增
luckperms.command.editor.apply-edits.success.additions-singular=新增
luckperms.command.editor.apply-edits.success.deletions=删减
luckperms.command.editor.apply-edits.success.deletions-singular=删减
luckperms.command.editor.apply-edits.no-changes=没有从网页编辑器中应用任何更改，因为返回的数据不包含任何修改
luckperms.command.editor.apply-edits.unknown-type=无法将编辑应用于指定的对象类型
luckperms.command.editor.apply-edits.unable-to-read=无法使用给定的代码读取数据
luckperms.command.search.searching.permission=搜索带有 {0} 的用户和权限组
luckperms.command.search.searching.inherit=搜索继承自 {0} 的用户和权限组
luckperms.command.search.result=发现来自 {1} 个用户和 {2} 个权限组的 {0} 个条目
luckperms.command.search.result.default-notice=注意\: 在搜索默认组的成员时, 没有其他权限的离线玩家将不会被显示出来\!
luckperms.command.search.showing-users=显示用户条目
luckperms.command.search.showing-groups=显示权限组条目
luckperms.command.tree.start=正在生成权限树, 请稍候...
luckperms.command.tree.empty=无法生成权限树, 没有找到结果
luckperms.command.tree.url=权限树 URL
luckperms.command.verbose.invalid-filter={0} 不是一个有效的详细日志过滤器
luckperms.command.verbose.enabled=详细日志 {0} 过滤器\: {1}
luckperms.command.verbose.command-exec=强制 {0} 执行命令 {1} 并报告所有的检查结果...
luckperms.command.verbose.off=详细日志 {0}
luckperms.command.verbose.command-exec-complete=命令执行完毕
luckperms.command.verbose.command.no-checks=命令执行完毕, 但没有进行权限检查
luckperms.command.verbose.command.possibly-async=这可能是因为该插件在后台运行命令 (异步)
luckperms.command.verbose.command.try-again-manually=你仍然可以手动使用 verbose 来检测
luckperms.command.verbose.enabled-recording=详细日志 {0} 过滤器\: {1}
luckperms.command.verbose.uploading=详细日志 {0}, 正在上传记录...
luckperms.command.verbose.url=详细的日志记录 URL
luckperms.command.verbose.enabled-term=启用
luckperms.command.verbose.disabled-term=禁用
luckperms.command.verbose.query-any=任意
luckperms.command.info.running-plugin=正在运行
luckperms.command.info.platform-key=平台
luckperms.command.info.server-brand-key=服务器类型
luckperms.command.info.server-version-key=服务器版本
luckperms.command.info.storage-key=存储
luckperms.command.info.storage-type-key=类型
luckperms.command.info.storage.meta.split-types-key=类型
luckperms.command.info.storage.meta.ping-key=延迟
luckperms.command.info.storage.meta.connected-key=已连接
luckperms.command.info.storage.meta.file-size-key=文件大小
luckperms.command.info.extensions-key=扩展
luckperms.command.info.messaging-key=消息服务
luckperms.command.info.instance-key=实例
luckperms.command.info.static-contexts-key=静态情境
luckperms.command.info.online-players-key=在线玩家
luckperms.command.info.online-players-unique={0} 唯一
luckperms.command.info.uptime-key=运行时间
luckperms.command.info.local-data-key=本地数据
luckperms.command.info.local-data={0} 个用户, {1} 个权限组, {2} 个权限组路线
luckperms.command.generic.create.success={0} 已成功创建
luckperms.command.generic.create.error=创建 {0} 时出现了一个错误
luckperms.command.generic.create.error-already-exists={0} 已存在
luckperms.command.generic.delete.success={0} 已成功删除
luckperms.command.generic.delete.error=删除 {0} 时出现了一个错误
luckperms.command.generic.delete.error-doesnt-exist={0} 并不存在
luckperms.command.generic.rename.success={0} 成功重名为 {1}
luckperms.command.generic.clone.success={0} 已成功克隆到 {1}
luckperms.command.generic.info.parent.title=父权限组
luckperms.command.generic.info.parent.temporary-title=临时父权限组
luckperms.command.generic.info.expires-in=过期时间
luckperms.command.generic.info.inherited-from=继承自
luckperms.command.generic.info.inherited-from-self=自己
luckperms.command.generic.show-tracks.title={0} 的权限组路线
luckperms.command.generic.show-tracks.empty={0} 不在任何权限组路线上
luckperms.command.generic.clear.node-removed={0} 个权限节点被移除
luckperms.command.generic.clear.node-removed-singular=权限节点 {0} 已移除
luckperms.command.generic.clear={0} 在情境 {1} 中的节点已被清除
luckperms.command.generic.permission.info.title={0} 的权限
luckperms.command.generic.permission.info.empty={0} 没有设置任何权限
luckperms.command.generic.permission.info.click-to-remove=点击从 {0} 中移除此权限节点
luckperms.command.generic.permission.check.info.title={0} 的权限信息
luckperms.command.generic.permission.check.info.directly={0} 已在情境 {3} 中将 {1} 设置为 {2}
luckperms.command.generic.permission.check.info.inherited={0} 在情境 {4} 中继承的 {3} 将 {1} 设置为 {2}
luckperms.command.generic.permission.check.info.not-directly={0} 没有设置 {1}
luckperms.command.generic.permission.check.info.not-inherited={0} 没有继承 {1}
luckperms.command.generic.permission.check.result.title=对 {0} 的权限检查
luckperms.command.generic.permission.check.result.result-key=结果
luckperms.command.generic.permission.check.result.processor-key=处理器
luckperms.command.generic.permission.check.result.cause-key=原因
luckperms.command.generic.permission.check.result.context-key=情境
luckperms.command.generic.permission.set=在情境 {3} 中将 {2} 的权限 {0} 设置为 {1}
luckperms.command.generic.permission.already-has={0} 在情境 {2} 中已经设置了 {1}
luckperms.command.generic.permission.set-temp=在情境 {4} 中将 {2} 的权限 {0} 设置为 {1}, 有效期\: {3}
luckperms.command.generic.permission.already-has-temp={0} 在情境 {2} 中已经临时设置了 {1}
luckperms.command.generic.permission.unset=在情境 {2} 中为 {1} 取消设置 {0}
luckperms.command.generic.permission.doesnt-have={0} 没有在情境 {2} 中设置 {1}
luckperms.command.generic.permission.unset-temp=在情境 {2} 中为 {1} 取消设置临时权限 {0}
luckperms.command.generic.permission.subtract=在情境 {4} 中将 {2} 的权限 {0} 设置为 {1}, 有效期\: {3}, 比之前少了 {5}
luckperms.command.generic.permission.doesnt-have-temp={0} 没有在情境 {2} 中临时设置 {1}
luckperms.command.generic.permission.clear={0} 在情境 {1} 中的权限已被清除
luckperms.command.generic.parent.info.title={0} 的父权限组
luckperms.command.generic.parent.info.empty={0} 没有定义任何父权限组
luckperms.command.generic.parent.info.click-to-remove=单击以从 {0} 中移除此父权限组
luckperms.command.generic.parent.add={0} 现在在情境 {2} 中继承 {1} 的权限
luckperms.command.generic.parent.add-temp={0} 现在在情境 {3} 中继承 {1} 的权限, 有效期\: {2}
luckperms.command.generic.parent.set={0} 在情境 {2} 中清除了现有的父权限组, 现在只继承 {1}
luckperms.command.generic.parent.set-track={0} 在情境 {3} 中清除了包含在权限组路线 {1} 中的父权限组, 现在只继承 {2}
luckperms.command.generic.parent.remove={0} 在情境 {2} 中不再继承 {1} 的权限
luckperms.command.generic.parent.remove-temp={0} 在情境 {2} 中不再临时继承 {1} 的权限
luckperms.command.generic.parent.subtract={0} 在情境 {3} 中继承 {1} 的权限, 有效期\: {2}, 比之前少了 {4}
luckperms.command.generic.parent.clear={0} 在情境 {1} 中的父权限组已被清除
luckperms.command.generic.parent.clear-track={0} 在情境 {2} 中清除了包含在权限组路线 {1} 中的父权限组
luckperms.command.generic.parent.already-inherits={0} 在情境 {2} 中已经继承 {1} 的权限
luckperms.command.generic.parent.doesnt-inherit={0} 没有在上下文 {2} 中继承 {1} 的权限
luckperms.command.generic.parent.already-temp-inherits={0} 在情境 {2} 中已经临时继承 {1} 的权限
luckperms.command.generic.parent.doesnt-temp-inherit={0} 没有在情境 {2} 中临时继承 {1} 的权限
luckperms.command.generic.chat-meta.info.title-prefix={0} 的前缀
luckperms.command.generic.chat-meta.info.title-suffix={0} 的后缀
luckperms.command.generic.chat-meta.info.none-prefix={0} 没有前缀
luckperms.command.generic.chat-meta.info.none-suffix={0} 没有后缀
luckperms.command.generic.chat-meta.info.click-to-remove=单击以从 {1} 中移除 {0}
luckperms.command.generic.chat-meta.already-has={0} 在情境 {4} 中已经设置了 {1} {2}, 优先级为 {3}
luckperms.command.generic.chat-meta.already-has-temp={0} 在情境 {4} 中已经临时设置了 {1} {2}, 优先级为 {3}
luckperms.command.generic.chat-meta.doesnt-have={0} 没有在情境 {4} 中设置优先级为 {3} 的 {1} {2}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} 没有在情境 {4} 中临时设置优先级为 {3} 的 {1} {2}
luckperms.command.generic.chat-meta.add={0} 在情境 {4} 中设置了 {1} {2}, 优先级为 {3}
luckperms.command.generic.chat-meta.add-temp={0} 在情境 {5} 中设置了 {1} {2}, 优先级为 {3}, 有效期\: {4}
luckperms.command.generic.chat-meta.remove={0} 在情境 {4} 中移除了优先级为 {3} 的 {1} {2}
luckperms.command.generic.chat-meta.remove-bulk={0} 在情境 {3} 中移除了所有优先级为 {2} 的 {1}
luckperms.command.generic.chat-meta.remove-temp={0} 在情境 {4} 中移除了优先级为 {3} 的临时 {1} {2}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} 在情境 {3} 中移除了所有优先级为 {2} 的临时 {1}
luckperms.command.generic.meta.info.title={0} 的元数据
luckperms.command.generic.meta.info.none={0} 没有元数据
luckperms.command.generic.meta.info.click-to-remove=单击以从 {0} 中移除此元数据节点
luckperms.command.generic.meta.already-has={0} 在情境 {3} 中已经将元数据键 {1} 设置为 {2}
luckperms.command.generic.meta.already-has-temp={0} 在情境 {3} 中已经将元数据键 {1} 临时设置为 {2}
luckperms.command.generic.meta.doesnt-have={0} 没有在情境 {2} 中设置元数据键 {1}
luckperms.command.generic.meta.doesnt-have-temp={0} 没有在情境 {2} 中临时设置元数据键 {1}
luckperms.command.generic.meta.set=在情境 {3} 中将 {2} 的元数据键 {0} 设置为 {1}
luckperms.command.generic.meta.set-temp=在情境 {4} 中将 {2} 的元数据键 {0} 设置为 {1}, 有效期\: {3}
luckperms.command.generic.meta.unset=在情境 {2} 中为 {1} 取消设置元数据键 {0}
luckperms.command.generic.meta.unset-temp=在情境 {2} 中为 {1} 取消设置临时元数据键 {0}
luckperms.command.generic.meta.clear={0} 在情境 {2} 中匹配类型 {1} 的元数据已被清除
luckperms.command.generic.contextual-data.title=情境数据
luckperms.command.generic.contextual-data.mode.key=模式
luckperms.command.generic.contextual-data.mode.server=服务器
luckperms.command.generic.contextual-data.mode.active-player=活跃玩家
luckperms.command.generic.contextual-data.contexts-key=情境
luckperms.command.generic.contextual-data.prefix-key=前缀
luckperms.command.generic.contextual-data.suffix-key=后缀
luckperms.command.generic.contextual-data.primary-group-key=主组
luckperms.command.generic.contextual-data.meta-key=元数据
luckperms.command.generic.contextual-data.null-result=无
luckperms.command.user.info.title=用户信息
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=类型
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=离线
luckperms.command.user.info.status-key=状态
luckperms.command.user.info.status.online=在线
luckperms.command.user.info.status.offline=离线
luckperms.command.user.removegroup.error-primary=你不能将用户从他们的主组中移除
luckperms.command.user.primarygroup.not-member={0} 还不是 {1} 的成员, 现在正在加入
luckperms.command.user.primarygroup.already-has={0} 已经将 {1} 设置为其的主权限组
luckperms.command.user.primarygroup.warn-option=警告\: 此服务器 ({0}) 使用的主权限组计算方法可能未反映此更改
luckperms.command.user.primarygroup.set={0} 的主组设置为 {1}
luckperms.command.user.track.error-not-contain-group={0} 尚未在 {1} 上的任何权限组中
luckperms.command.user.track.unsure-which-track=不确定使用哪个权限组路线, 请将其指定为参数
luckperms.command.user.track.missing-group-advice=创建权限组或将其从权限组路线中移除并重试
luckperms.command.user.promote.added-to-first={0} 在情境 {3} 中不在 {1} 上的任何权限组中, 因此其被添加到第一个组 {2}
luckperms.command.user.promote.not-on-track={0} 不在 {1} 上的任何权限组中, 因此未能提升
luckperms.command.user.promote.success=在情境 {4} 中将 {0} 沿权限组路线 {1} 从 {2} 提升到 {3}
luckperms.command.user.promote.end-of-track=已到达权限组路线 {0} 的末尾, 无法继续提升 {1}
luckperms.command.user.promote.next-group-deleted=权限组路线上的下一个权限组 {0} 不再存在
luckperms.command.user.promote.unable-to-promote=无法提升用户
luckperms.command.user.demote.success=在情境 {4} 中将 {0} 沿权限组路线 {1} 从 {2} 降级到 {3}
luckperms.command.user.demote.end-of-track=已到达权限组路线 {0} 的起点, 因此 {1} 已从 {2} 中移除
luckperms.command.user.demote.end-of-track-not-removed=已到达权限组路线 {0} 的起点, 但未从第一个组中移除 {1}
luckperms.command.user.demote.previous-group-deleted=权限组路线上的前一个权限组 {0} 不再存在
luckperms.command.user.demote.unable-to-demote=无法降级用户
luckperms.command.group.list.title=权限组
luckperms.command.group.delete.not-default=你不能删除默认组
luckperms.command.group.info.title=权限组信息
luckperms.command.group.info.display-name-key=显示名称
luckperms.command.group.info.weight-key=权重
luckperms.command.group.setweight.set=将权限组 {1} 的权重设置为 {0}
luckperms.command.group.setdisplayname.doesnt-have={0} 没有设置显示名称
luckperms.command.group.setdisplayname.already-has={0} 已经设置了显示名称为 {1}
luckperms.command.group.setdisplayname.already-in-use=显示名称 {0} 已被 {1} 使用
luckperms.command.group.setdisplayname.set=在情境 {2} 中将组 {1} 的显示名称设置为 {0}
luckperms.command.group.setdisplayname.removed=在情境 {1} 中移除了组 {0} 的显示名称
luckperms.command.track.list.title=权限组路线
luckperms.command.track.path.empty=无
luckperms.command.track.info.showing-track=显示权限组路线
luckperms.command.track.info.path-property=路线
luckperms.command.track.clear=权限组路线 {0} 上的路线已清除
luckperms.command.track.append.success=权限组 {0} 被附加到权限组路线 {1} 中
luckperms.command.track.insert.success=权限组 {0} 被插入到权限组路线 {1} 的位置 {2}
luckperms.command.track.insert.error-number=需要数字, 但收到\: {0}
luckperms.command.track.insert.error-invalid-pos=无法插入位置 {0}
luckperms.command.track.insert.error-invalid-pos-reason=无效位置
luckperms.command.track.remove.success=权限组 {0} 已从权限组路线 {1} 中移除
luckperms.command.track.error-empty={0} 不能使用, 因为它是空的或只包含一个组
luckperms.command.track.error-multiple-groups={0} 是此权限组路线上多个组的成员
luckperms.command.track.error-ambiguous=无法确定其位置
luckperms.command.track.already-contains={0} 已经包含 {1}
luckperms.command.track.doesnt-contain={0} 没有包含 {1}
luckperms.command.log.load-error=无法加载日志
luckperms.command.log.invalid-page=页码无效
luckperms.command.log.invalid-page-range=请输入一个介于 {0} 和 {1} 之间的值
luckperms.command.log.empty=没有可显示的日志条目
luckperms.command.log.notify.error-console=无法切换控制台的通知
luckperms.command.log.notify.enabled-term=启用
luckperms.command.log.notify.disabled-term=禁用
luckperms.command.log.notify.changed-state={0} 日志输出
luckperms.command.log.notify.already-on=你已经在接收通知
luckperms.command.log.notify.already-off=你目前并未接收通知
luckperms.command.log.notify.invalid-state=状态未知. 需要 {0} 或 {1}
luckperms.command.log.show.search=显示包含查询内容 {0} 的最近的操作
luckperms.command.log.show.recent=显示最近的操作
luckperms.command.log.show.by=显示 {0} 最近的操作
luckperms.command.log.show.history=显示 {0} {1} 的历史记录
luckperms.command.export.error-term=错误
luckperms.command.export.already-running=另一个导出过程正在运行
luckperms.command.export.file.already-exists=文件 {0} 已存在
luckperms.command.export.file.not-writable=文件 {0} 无法被写入
luckperms.command.export.file.success=已成功导出到 {0}
luckperms.command.export.file-unexpected-error-writing=在写入文件时发生了意外的错误
luckperms.command.export.web.export-code=导出代码
luckperms.command.export.web.import-command-description=使用以下命令导入
luckperms.command.import.term=导入
luckperms.command.import.error-term=错误
luckperms.command.import.already-running=另一个导入过程已经在运行
luckperms.command.import.file.doesnt-exist=文件 {0} 不存在
luckperms.command.import.file.not-readable=文件 {0} 不可读
luckperms.command.import.file.unexpected-error-reading=在读取导入文件时发生了意外的错误
luckperms.command.import.file.correct-format=这是正确的格式吗？
luckperms.command.import.web.unable-to-read=无法使用给定的代码读取数据
luckperms.command.import.progress.percent=已完成{0}%
luckperms.command.import.progress.operations={0}/{1} 操作完成
luckperms.command.import.starting=正在开始导入过程
luckperms.command.import.completed=已完成
luckperms.command.import.duration=花费了 {0} 秒
luckperms.command.bulkupdate.must-use-console=批量更新命令只能在控制台使用
luckperms.command.bulkupdate.invalid-data-type=无效的类型, 需要 {0}
luckperms.command.bulkupdate.invalid-constraint=无效的约束 {0}
luckperms.command.bulkupdate.invalid-constraint-format=约束应遵循格式 {0}
luckperms.command.bulkupdate.invalid-comparison=无效的比较运算符 {0}
luckperms.command.bulkupdate.invalid-comparison-format=需要为以下之一\: {0}
luckperms.command.bulkupdate.queued=批量更新操作排队中
luckperms.command.bulkupdate.confirm=运行 {0} 来执行更新
luckperms.command.bulkupdate.unknown-id=ID为 {0} 的操作不存在或已过期
luckperms.command.bulkupdate.starting=正在进行批量更新
luckperms.command.bulkupdate.success=批量更新已成功完成
luckperms.command.bulkupdate.success.statistics.nodes=受影响的节点总数
luckperms.command.bulkupdate.success.statistics.users=受影响的用户总数
luckperms.command.bulkupdate.success.statistics.groups=受影响的权限组总数
luckperms.command.bulkupdate.failure=批量更新失败, 请检查控制台获得错误信息
luckperms.command.update-task.request=已请求更新任务, 请稍候
luckperms.command.update-task.complete=更新任务已完成
luckperms.command.update-task.push.attempting=正在尝试推送至其他服务器
luckperms.command.update-task.push.complete=已成功通过 {0} 通知其它服务器
luckperms.command.update-task.push.error=在向其他服务器推送更改时发生了错误
luckperms.command.update-task.push.error-not-setup=无法将更改推送到其他服务器, 因为消息服务尚未配置
luckperms.command.reload-config.success=已重新加载配置文件
luckperms.command.reload-config.restart-note=某些选项仅在服务器重新启动后才应用
luckperms.command.translations.searching=正在搜索可用的翻译, 请稍候...
luckperms.command.translations.searching-error=无法获得可用翻译的列表
luckperms.command.translations.installed-translations=已安装的翻译
luckperms.command.translations.available-translations=可用的翻译
luckperms.command.translations.percent-translated=已翻译{0}%
luckperms.command.translations.translations-by=由
luckperms.command.translations.installing=正在安装翻译, 请稍候...
luckperms.command.translations.download-error=无法下载 {0} 的翻译
luckperms.command.translations.installing-specific=正在安装语言 {0}...
luckperms.command.translations.install-complete=安装已完成
luckperms.command.translations.download-prompt=使用 {0} 下载并安装由社区提供的翻译的最新版本
luckperms.command.translations.download-override-warning=请注意, 此操作将会覆盖您对这些语言做出的任何更改
luckperms.usage.user.description=用于在 LuckPerms 中管理用户的命令. (LuckPerms 中的 "user" 代表玩家, 可以引用 UUID 或用户名称)
luckperms.usage.group.description=用于在 LuckPerms 中管理权限组的命令. 权限组只是可以授予用户的权限分配的集合, 使用 ''''creategroup'''' 命令创建新组
luckperms.usage.track.description=用于在 LuckPerms 中管理权限组路线的命令. 权限组路线是一组有序的权限组, 可用于定义晋升和降级
luckperms.usage.log.description=用于在 LuckPerms 中管理日志记录功能的命令.
luckperms.usage.sync.description=将插件存储中的所有数据重新加载到内存中, 并应用检测到的任何更改.
luckperms.usage.info.description=打印关于当前插件实例的一般信息.
luckperms.usage.editor.description=创建一个新的网页编辑器会话
luckperms.usage.editor.argument.type=要加载到编辑器中的类型. (''''all'''', ''''users'''' 或 ''''groups'''')
luckperms.usage.editor.argument.filter=过滤用户条目的权限
luckperms.usage.verbose.description=控制插件的详细日志系统
luckperms.usage.verbose.argument.action=启用/禁用日志记录, 或上传记录的输出
luckperms.usage.verbose.argument.filter=匹配条目的过滤器
luckperms.usage.verbose.argument.commandas=要运行命令的玩家和要运行的命令
luckperms.usage.tree.description=生成 LuckPerms 已知的所有权限的树视图 (有序列表层次结构)
luckperms.usage.tree.argument.scope=树的根. 指定 "." 以包含所有权限
luckperms.usage.tree.argument.player=要检查的在线玩家的名称
luckperms.usage.search.description=搜索具有特定权限的用户和权限组
luckperms.usage.search.argument.permission=要搜索的权限
luckperms.usage.search.argument.page=要查看的页
luckperms.usage.network-sync.description=与存储同步更改并请求网络上的所有其他服务器也这样做
luckperms.usage.import.description=从先前导出的文件导入数据
luckperms.usage.import.argument.file=要导入的文件
luckperms.usage.import.argument.replace=替换现有数据而不是合并
luckperms.usage.import.argument.upload=上传先前导出的数据
luckperms.usage.export.description=将所有权限数据导出到指定文件. 以后可以重新导入.
luckperms.usage.export.argument.file=要导出的文件
luckperms.usage.export.argument.without-users=将用户从导出中排除
luckperms.usage.export.argument.without-groups=将权限组从导出中排除
luckperms.usage.export.argument.upload=将所有权限数据上传到网络. 稍后可以重新导入(最多保留14天).
luckperms.usage.reload-config.description=重新加载一些配置选项
luckperms.usage.bulk-update.description=在所有数据上执行批量变更查询
luckperms.usage.bulk-update.argument.data-type=要更改的数据类型. ("all", "users" 或 "groups")
luckperms.usage.bulk-update.argument.action=要对数据执行的操作. ("update" 或 "delete")
luckperms.usage.bulk-update.argument.action-field=要更新的字段. 仅"update"操作需要. ("permission", "server" 或 "world")
luckperms.usage.bulk-update.argument.action-value=要替换成的值. 仅"update"操作需要.
luckperms.usage.bulk-update.argument.constraint=更新所需的限制条件
luckperms.usage.translations.description=管理翻译
luckperms.usage.translations.argument.install=安装翻译的子命令
luckperms.usage.apply-edits.description=应用从网页编辑器所做的权限更改
luckperms.usage.apply-edits.argument.code=数据的唯一代码
luckperms.usage.apply-edits.argument.target=将数据应用到谁
luckperms.usage.create-group.description=创建一个新的权限组
luckperms.usage.create-group.argument.name=权限组的名称
luckperms.usage.create-group.argument.weight=权限组的权重
luckperms.usage.create-group.argument.display-name=权限组的显示名称
luckperms.usage.delete-group.description=删除一个权限组
luckperms.usage.delete-group.argument.name=权限组的名称
luckperms.usage.list-groups.description=列出平台上的所有权限组
luckperms.usage.create-track.description=创建一个新的权限组路线
luckperms.usage.create-track.argument.name=权限组路线的名称
luckperms.usage.delete-track.description=删除一个权限组路线
luckperms.usage.delete-track.argument.name=权限组路线的名称
luckperms.usage.list-tracks.description=列出平台上的所有权限组路线
luckperms.usage.user-info.description=显示用户信息
luckperms.usage.user-switchprimarygroup.description=切换用户的主组
luckperms.usage.user-switchprimarygroup.argument.group=要切换到的权限组
luckperms.usage.user-promote.description=在指定的权限组路线上提升用户
luckperms.usage.user-promote.argument.track=要提升用户的权限组路线
luckperms.usage.user-promote.argument.context=要提升用户的情境
luckperms.usage.user-promote.argument.dont-add-to-first=只有当用户已经在权限组路线上时才进行提升
luckperms.usage.user-demote.description=在指定的权限组路线上降级用户
luckperms.usage.user-demote.argument.track=要降级用户的权限组路线
luckperms.usage.user-demote.argument.context=要降级用户的情境
luckperms.usage.user-demote.argument.dont-remove-from-first=防止将用户从第一个组中移除
luckperms.usage.user-clone.description=克隆用户
luckperms.usage.user-clone.argument.user=要克隆到的用户名称或UUID
luckperms.usage.group-info.description=提供关于权限组的信息
luckperms.usage.group-listmembers.description=显示继承此组的用户和权限组
luckperms.usage.group-listmembers.argument.page=要查看的页
luckperms.usage.group-setweight.description=设置权限组的权重
luckperms.usage.group-setweight.argument.weight=要设置的权重
luckperms.usage.group-set-display-name.description=设置权限组的显示名称
luckperms.usage.group-set-display-name.argument.name=要设置的名称
luckperms.usage.group-set-display-name.argument.context=要设置名称的情境
luckperms.usage.group-rename.description=重命名权限组
luckperms.usage.group-rename.argument.name=新的名称
luckperms.usage.group-clone.description=克隆权限组
luckperms.usage.group-clone.argument.name=要克隆到的权限组名称
luckperms.usage.holder-editor.description=打开网页权限编辑器
luckperms.usage.holder-showtracks.description=列出对象所在的权限组路线
luckperms.usage.holder-clear.description=移除所有权限、父权限组和元数据
luckperms.usage.holder-clear.argument.context=要过滤的情境：
luckperms.usage.permission.description=编辑权限
luckperms.usage.parent.description=编辑继承
luckperms.usage.meta.description=编辑元数据值
luckperms.usage.permission-info.description=列出对象拥有的权限节点
luckperms.usage.permission-info.argument.page=要查看的页
luckperms.usage.permission-info.argument.sort-mode=如何排序条目
luckperms.usage.permission-set.description=设置对象的权限
luckperms.usage.permission-set.argument.node=要设置的权限节点
luckperms.usage.permission-set.argument.value=节点的值
luckperms.usage.permission-set.argument.context=要添加权限的情境
luckperms.usage.permission-unset.description=取消设置对象的权限
luckperms.usage.permission-unset.argument.node=要取消设置的权限节点
luckperms.usage.permission-unset.argument.context=要取消设置权限的情境
luckperms.usage.permission-settemp.description=临时设置对象的权限
luckperms.usage.permission-settemp.argument.node=要设置的权限节点
luckperms.usage.permission-settemp.argument.value=节点的值
luckperms.usage.permission-settemp.argument.duration=权限节点的有效期
luckperms.usage.permission-settemp.argument.temporary-modifier=要如何应用临时权限
luckperms.usage.permission-settemp.argument.context=要添加权限的情境
luckperms.usage.permission-unsettemp.description=取消设置对象的临时权限
luckperms.usage.permission-unsettemp.argument.node=要取消设置的权限节点
luckperms.usage.permission-unsettemp.argument.duration=要减去的有效期
luckperms.usage.permission-unsettemp.argument.context=要取消设置权限的情境
luckperms.usage.permission-check.description=检查对象是否拥有特定的权限节点
luckperms.usage.permission-check.argument.node=要检查的权限节点
luckperms.usage.permission-clear.description=清除所有权限
luckperms.usage.permission-clear.argument.context=要过滤的情境：
luckperms.usage.parent-info.description=列出该对象继承的权限组
luckperms.usage.parent-info.argument.page=要查看的页
luckperms.usage.parent-info.argument.sort-mode=如何排序条目
luckperms.usage.parent-set.description=移除对象已经继承的所有权限组并将其添加到给定的权限组中
luckperms.usage.parent-set.argument.group=要设置的权限组
luckperms.usage.parent-set.argument.context=要设置权限组的情境
luckperms.usage.parent-add.description=为对象添加另一个要继承的权限组以从其继承权限
luckperms.usage.parent-add.argument.group=要继承的权限组
luckperms.usage.parent-add.argument.context=要继承权限组的情境
luckperms.usage.parent-remove.description=移除先前继承的某个组
luckperms.usage.parent-remove.argument.group=要移除的权限组
luckperms.usage.parent-remove.argument.context=要移除权限组的情境
luckperms.usage.parent-set-track.description=移除对象已经继承的所有包含在给定权限组路线上的权限组并将其添加到给定的权限组中
luckperms.usage.parent-set-track.argument.track=用于设置的权限组路线
luckperms.usage.parent-set-track.argument.group=要设置的权限组, 或该权限组在给定权限组路线上的位置
luckperms.usage.parent-set-track.argument.context=要设置权限组的情境
luckperms.usage.parent-add-temp.description=为对象添加另一个要临时继承的权限组以从其继承权限
luckperms.usage.parent-add-temp.argument.group=要继承的权限组
luckperms.usage.parent-add-temp.argument.duration=临时权限组的有效期
luckperms.usage.parent-add-temp.argument.temporary-modifier=要如何应用临时权限
luckperms.usage.parent-add-temp.argument.context=要继承权限组的情境
luckperms.usage.parent-remove-temp.description=移除先前临时继承的某个组
luckperms.usage.parent-remove-temp.argument.group=要移除的权限组
luckperms.usage.parent-remove-temp.argument.duration=要减去的有效期
luckperms.usage.parent-remove-temp.argument.context=要移除权限组的情境
luckperms.usage.parent-clear.description=清除所有父权限组
luckperms.usage.parent-clear.argument.context=要过滤的情境：
luckperms.usage.parent-clear-track.description=清除包含在给定权限组路线上的所有父权限组
luckperms.usage.parent-clear-track.argument.track=用于清除的权限组路线
luckperms.usage.parent-clear-track.argument.context=要过滤的情境：
luckperms.usage.meta-info.description=显示所有聊天元数据
luckperms.usage.meta-set.description=设置元数据值
luckperms.usage.meta-set.argument.key=要设置的键
luckperms.usage.meta-set.argument.value=要设置的值
luckperms.usage.meta-set.argument.context=要添加元数据的情境
luckperms.usage.meta-unset.description=取消设置元数据值
luckperms.usage.meta-unset.argument.key=要取消设置的键
luckperms.usage.meta-unset.argument.context=要移除元数据的情境
luckperms.usage.meta-settemp.description=临时设置元数据值
luckperms.usage.meta-settemp.argument.key=要设置的键
luckperms.usage.meta-settemp.argument.value=要设置的值
luckperms.usage.meta-settemp.argument.duration=元数据值的有效期
luckperms.usage.meta-settemp.argument.context=要添加元数据的情境
luckperms.usage.meta-unsettemp.description=取消设置临时元数据值
luckperms.usage.meta-unsettemp.argument.key=要取消设置的键
luckperms.usage.meta-unsettemp.argument.context=要移除元数据的情境
luckperms.usage.meta-addprefix.description=添加一个前缀
luckperms.usage.meta-addprefix.argument.priority=要添加的前缀的优先级
luckperms.usage.meta-addprefix.argument.prefix=要添加的前缀
luckperms.usage.meta-addprefix.argument.context=要添加前缀的情境
luckperms.usage.meta-addsuffix.description=添加一个后缀
luckperms.usage.meta-addsuffix.argument.priority=要添加的后缀的优先级
luckperms.usage.meta-addsuffix.argument.suffix=要添加的后缀
luckperms.usage.meta-addsuffix.argument.context=要添加后缀的情境
luckperms.usage.meta-setprefix.description=设置一个前缀
luckperms.usage.meta-setprefix.argument.priority=要设置的前缀的优先级
luckperms.usage.meta-setprefix.argument.prefix=要设置的前缀
luckperms.usage.meta-setprefix.argument.context=要设置前缀的情境
luckperms.usage.meta-setsuffix.description=设置一个后缀
luckperms.usage.meta-setsuffix.argument.priority=要设置的后缀的优先级
luckperms.usage.meta-setsuffix.argument.suffix=要设置的后缀
luckperms.usage.meta-setsuffix.argument.context=要设置后缀的情境
luckperms.usage.meta-removeprefix.description=移除一个前缀
luckperms.usage.meta-removeprefix.argument.priority=要移除的前缀的优先级
luckperms.usage.meta-removeprefix.argument.prefix=要移除的前缀
luckperms.usage.meta-removeprefix.argument.context=要移除前缀的情境
luckperms.usage.meta-removesuffix.description=移除一个后缀
luckperms.usage.meta-removesuffix.argument.priority=要移除的后缀的优先级
luckperms.usage.meta-removesuffix.argument.suffix=要移除的后缀
luckperms.usage.meta-removesuffix.argument.context=要移除后缀的情境
luckperms.usage.meta-addtemp-prefix.description=临时添加一个前缀
luckperms.usage.meta-addtemp-prefix.argument.priority=要添加的前缀的优先级
luckperms.usage.meta-addtemp-prefix.argument.prefix=要添加的前缀
luckperms.usage.meta-addtemp-prefix.argument.duration=要添加的前缀的有效期
luckperms.usage.meta-addtemp-prefix.argument.context=要添加前缀的情境
luckperms.usage.meta-addtemp-suffix.description=临时添加一个后缀
luckperms.usage.meta-addtemp-suffix.argument.priority=要添加的后缀的优先级
luckperms.usage.meta-addtemp-suffix.argument.suffix=要添加的后缀
luckperms.usage.meta-addtemp-suffix.argument.duration=要添加的后缀的有效期
luckperms.usage.meta-addtemp-suffix.argument.context=要添加后缀的情境
luckperms.usage.meta-settemp-prefix.description=临时设置一个前缀
luckperms.usage.meta-settemp-prefix.argument.priority=要设置的前缀的优先级
luckperms.usage.meta-settemp-prefix.argument.prefix=要设置的前缀
luckperms.usage.meta-settemp-prefix.argument.duration=要设置的前缀的有效期
luckperms.usage.meta-settemp-prefix.argument.context=要设置前缀的情境
luckperms.usage.meta-settemp-suffix.description=临时设置一个后缀
luckperms.usage.meta-settemp-suffix.argument.priority=要设置的后缀的优先级
luckperms.usage.meta-settemp-suffix.argument.suffix=要设置的后缀
luckperms.usage.meta-settemp-suffix.argument.duration=要设置的后缀的有效期
luckperms.usage.meta-settemp-suffix.argument.context=要设置后缀的情境
luckperms.usage.meta-removetemp-prefix.description=移除一个临时前缀
luckperms.usage.meta-removetemp-prefix.argument.priority=要移除的前缀的优先级
luckperms.usage.meta-removetemp-prefix.argument.prefix=要移除的前缀
luckperms.usage.meta-removetemp-prefix.argument.context=要移除前缀的情境
luckperms.usage.meta-removetemp-suffix.description=移除一个临时后缀
luckperms.usage.meta-removetemp-suffix.argument.priority=要移除的后缀的优先级
luckperms.usage.meta-removetemp-suffix.argument.suffix=要移除的后缀
luckperms.usage.meta-removetemp-suffix.argument.context=要移除后缀的情境
luckperms.usage.meta-clear.description=清除所有元数据
luckperms.usage.meta-clear.argument.type=要移除的元数据的类型
luckperms.usage.meta-clear.argument.context=要过滤的情境：
luckperms.usage.track-info.description=提供关于权限组路线的信息
luckperms.usage.track-editor.description=打开网页权限编辑器
luckperms.usage.track-append.description=将一个权限组附加到权限组路线的末尾
luckperms.usage.track-append.argument.group=要添加的权限组
luckperms.usage.track-insert.description=在权限组路线的指定位置插入权限组
luckperms.usage.track-insert.argument.group=要插入的权限组
luckperms.usage.track-insert.argument.position=要插入权限组的位置 (权限组路线上的第一个位置是1)
luckperms.usage.track-remove.description=从权限组路线中移除一个权限组
luckperms.usage.track-remove.argument.group=要移除的权限组
luckperms.usage.track-clear.description=清除权限组路线上的权限组
luckperms.usage.track-rename.description=重命名权限组路线
luckperms.usage.track-rename.argument.name=新的名称
luckperms.usage.track-clone.description=克隆权限组路线
luckperms.usage.track-clone.argument.name=要克隆到的权限组路线名称
luckperms.usage.log-recent.description=查看最近的操作
luckperms.usage.log-recent.argument.user=要过滤的用户名称或UUID
luckperms.usage.log-recent.argument.page=要查看的页码
luckperms.usage.log-search.description=在日志中搜索条目
luckperms.usage.log-search.argument.query=要搜索的内容
luckperms.usage.log-search.argument.page=要查看的页码
luckperms.usage.log-notify.description=开关日志通知
luckperms.usage.log-notify.argument.toggle=开启或关闭
luckperms.usage.log-user-history.description=查看用户的历史记录
luckperms.usage.log-user-history.argument.user=用户名称或UUID
luckperms.usage.log-user-history.argument.page=要查看的页码
luckperms.usage.log-group-history.description=查看权限组历史记录
luckperms.usage.log-group-history.argument.group=权限组的名称
luckperms.usage.log-group-history.argument.page=要查看的页码
luckperms.usage.log-track-history.description=查看权限组路线历史记录
luckperms.usage.log-track-history.argument.track=权限组路线名称
luckperms.usage.log-track-history.argument.page=要查看的页码
luckperms.usage.sponge.description=编辑额外的 Sponge 数据
luckperms.usage.sponge.argument.collection=要查询的集合
luckperms.usage.sponge.argument.subject=要修改的主体
luckperms.usage.sponge-permission-info.description=显示主体的权限信息
luckperms.usage.sponge-permission-info.argument.contexts=要过滤的情境：
luckperms.usage.sponge-permission-set.description=为主体设置权限
luckperms.usage.sponge-permission-set.argument.node=要设置的权限节点
luckperms.usage.sponge-permission-set.argument.tristate=权限节点的值
luckperms.usage.sponge-permission-set.argument.contexts=要设置权限的情境
luckperms.usage.sponge-permission-clear.description=清除主体的权限
luckperms.usage.sponge-permission-clear.argument.contexts=要清除权限的情境
luckperms.usage.sponge-parent-info.description=显示对象的父权限组的信息
luckperms.usage.sponge-parent-info.argument.contexts=要过滤的情境：
luckperms.usage.sponge-parent-add.description=为主体添加父权限组
luckperms.usage.sponge-parent-add.argument.collection=父权限组所在的主体集合
luckperms.usage.sponge-parent-add.argument.subject=父权限组的名称
luckperms.usage.sponge-parent-add.argument.contexts=要添加父权限组的情境
luckperms.usage.sponge-parent-remove.description=移除对象的父权限组
luckperms.usage.sponge-parent-remove.argument.collection=父权限组所在的主体集合
luckperms.usage.sponge-parent-remove.argument.subject=父权限组的名称
luckperms.usage.sponge-parent-remove.argument.contexts=要移除父权限组的情境
luckperms.usage.sponge-parent-clear.description=清除主体的父权限组
luckperms.usage.sponge-parent-clear.argument.contexts=要清除父权限组的情境
luckperms.usage.sponge-option-info.description=显示主体的选项的信息
luckperms.usage.sponge-option-info.argument.contexts=要过滤的情境：
luckperms.usage.sponge-option-set.description=为主体设置一个选项
luckperms.usage.sponge-option-set.argument.key=要设置的键
luckperms.usage.sponge-option-set.argument.value=要设置的值
luckperms.usage.sponge-option-set.argument.contexts=要设置选项的情境
luckperms.usage.sponge-option-unset.description=取消设置主体的选项
luckperms.usage.sponge-option-unset.argument.key=要取消设置的键
luckperms.usage.sponge-option-unset.argument.contexts=要取消设置键的情境
luckperms.usage.sponge-option-clear.description=清除主体的选项
luckperms.usage.sponge-option-clear.argument.contexts=要清除选项的情境
