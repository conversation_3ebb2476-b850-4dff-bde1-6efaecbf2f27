@echo off
REM Minecraft Server Docker Stop Script for Windows
REM This script stops the Minecraft server and MySQL database containers

echo 🛑 Stopping Minecraft Server...

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose is not installed.
    pause
    exit /b 1
)

REM Stop the containers gracefully
echo 🐳 Stopping Docker containers...
docker-compose down

echo ✅ Minecraft server stopped successfully!
echo.
echo 📋 To start again:
echo    - Run 'start.cmd' to start the server
echo.
echo 🗑️  To remove all data (CAUTION!):
echo    - Run 'docker-compose down -v' to remove volumes
echo    - Run 'docker system prune' to clean up unused containers/images

pause
