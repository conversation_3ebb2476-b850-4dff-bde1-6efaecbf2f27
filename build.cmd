@echo off
REM Minecraft Server Docker Build Script for Windows
REM This script builds the Docker containers for the Minecraft server

echo 🚀 Building Minecraft Server Docker Containers...

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose is not installed. Please install docker-compose and try again.
    pause
    exit /b 1
)

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist "database\init" mkdir "database\init"
if not exist "database\config" mkdir "database\config"
if not exist "config\plugins" mkdir "config\plugins"
if not exist "logs" mkdir "logs"

REM Build the containers
echo 🔨 Building Docker containers...
docker-compose build --no-cache

echo ✅ Build completed successfully!
echo.
echo 📋 Next steps:
echo    1. Run 'start.cmd' to start the server
echo    2. Access phpMyAdmin at http://localhost:8080
echo    3. Connect to Minecraft server at localhost:25565
echo.
echo 🔧 Useful commands:
echo    - View logs: docker-compose logs -f minecraft
echo    - Stop server: docker-compose down
echo    - Restart server: docker-compose restart minecraft
echo    - Access server console: docker exec -it minecraft-server bash

pause
