-- Create the main minecraft database if it doesn't exist
CREATE DATABASE IF NOT EXISTS `minecraft-database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the minecraft database
USE `minecraft-database`;

-- Create LoginSecurity tables
CREATE TABLE IF NOT EXISTS `ls_players` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) NOT NULL UNIQUE,
    `username` VARCHAR(16) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `ip` VARCHAR(45),
    `last_login` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `registration_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `login_attempts` INT DEFAULT 0,
    `is_logged_in` BOOLEAN DEFAULT FALSE,
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_username` (`username`),
    INDEX `idx_last_login` (`last_login`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create UserLogin tables
CREATE TABLE IF NOT EXISTS `player_data` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) NOT NULL UNIQUE,
    `username` VARCHAR(16) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `salt` VARCHAR(255),
    `ip_address` VARCHAR(45),
    `last_login` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `registration_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `login_attempts` INT DEFAULT 0,
    `is_registered` BOOLEAN DEFAULT TRUE,
    `session_timeout` TIMESTAMP NULL,
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_username` (`username`),
    INDEX `idx_last_login` (`last_login`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create LuckPerms tables
CREATE TABLE IF NOT EXISTS `luckperms_players` (
    `uuid` VARCHAR(36) NOT NULL,
    `username` VARCHAR(16) NOT NULL,
    `primary_group` VARCHAR(36) NOT NULL,
    PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `luckperms_user_permissions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) NOT NULL,
    `permission` VARCHAR(200) NOT NULL,
    `value` BOOLEAN NOT NULL,
    `server` VARCHAR(36) NOT NULL DEFAULT 'global',
    `world` VARCHAR(64) NOT NULL DEFAULT 'global',
    `expiry` BIGINT NOT NULL DEFAULT 0,
    `contexts` VARCHAR(200) NOT NULL DEFAULT '{}',
    INDEX `idx_uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `luckperms_groups` (
    `name` VARCHAR(36) NOT NULL,
    PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `luckperms_group_permissions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(36) NOT NULL,
    `permission` VARCHAR(200) NOT NULL,
    `value` BOOLEAN NOT NULL,
    `server` VARCHAR(36) NOT NULL DEFAULT 'global',
    `world` VARCHAR(64) NOT NULL DEFAULT 'global',
    `expiry` BIGINT NOT NULL DEFAULT 0,
    `contexts` VARCHAR(200) NOT NULL DEFAULT '{}',
    INDEX `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create MyCommand player data table
CREATE TABLE IF NOT EXISTS `mycommand_playerdata` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) NOT NULL,
    `username` VARCHAR(16) NOT NULL,
    `data_key` VARCHAR(100) NOT NULL,
    `data_value` TEXT,
    `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `unique_player_key` (`uuid`, `data_key`),
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create PlayerParticles table
CREATE TABLE IF NOT EXISTS `playerparticles_players` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) NOT NULL UNIQUE,
    `particles` TEXT,
    `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create AdvancedTeleport tables
CREATE TABLE IF NOT EXISTS `advancedtp_homes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) NOT NULL,
    `name` VARCHAR(50) NOT NULL,
    `world` VARCHAR(100) NOT NULL,
    `x` DOUBLE NOT NULL,
    `y` DOUBLE NOT NULL,
    `z` DOUBLE NOT NULL,
    `yaw` FLOAT NOT NULL DEFAULT 0,
    `pitch` FLOAT NOT NULL DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY `unique_player_home` (`uuid`, `name`),
    INDEX `idx_uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `advancedtp_warps` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(50) NOT NULL UNIQUE,
    `world` VARCHAR(100) NOT NULL,
    `x` DOUBLE NOT NULL,
    `y` DOUBLE NOT NULL,
    `z` DOUBLE NOT NULL,
    `yaw` FLOAT NOT NULL DEFAULT 0,
    `pitch` FLOAT NOT NULL DEFAULT 0,
    `created_by` VARCHAR(36),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create EliteMobs tables
CREATE TABLE IF NOT EXISTS `elitemobs_players` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) NOT NULL UNIQUE,
    `username` VARCHAR(16) NOT NULL,
    `currency` DECIMAL(15,2) DEFAULT 0.00,
    `guild_rank` VARCHAR(50) DEFAULT 'Peasant',
    `active_quests` TEXT,
    `completed_quests` TEXT,
    `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create TAB plugin tables
CREATE TABLE IF NOT EXISTS `tab_players` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) NOT NULL UNIQUE,
    `username` VARCHAR(16) NOT NULL,
    `tablist_name` VARCHAR(100),
    `prefix` VARCHAR(100),
    `suffix` VARCHAR(100),
    `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
