#!/bin/bash

# Minecraft Server Backup Script
# This script creates backups of world data and database

set -e  # Exit on any error

BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
echo "🗄️  Creating backup in: $BACKUP_DIR"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Check if containers are running
if ! docker-compose ps | grep -q "Up"; then
    echo "⚠️  Containers are not running. Starting them first..."
    docker-compose up -d
    sleep 30
fi

# Backup world data
echo "🌍 Backing up world data..."
docker cp minecraft-server:/opt/minecraft/world "$BACKUP_DIR/"
docker cp minecraft-server:/opt/minecraft/world_nether "$BACKUP_DIR/"
docker cp minecraft-server:/opt/minecraft/world_the_end "$BACKUP_DIR/"

# Backup plugin data
echo "🔌 Backing up plugin data..."
docker cp minecraft-server:/opt/minecraft/plugins "$BACKUP_DIR/"

# Backup database
echo "🗃️  Backing up database..."
docker exec minecraft-mysql mysqldump -u root -pHh@#2021 --single-transaction --routines --triggers minecraft-database > "$BACKUP_DIR/database.sql"

# Create backup info file
echo "📋 Creating backup info..."
cat > "$BACKUP_DIR/backup_info.txt" << EOF
Backup created: $(date)
Minecraft Server Version: 1.21.4 Paper
Database: minecraft-database
User: hamza

Contents:
- world/ (Overworld)
- world_nether/ (Nether)
- world_the_end/ (End)
- plugins/ (Plugin data and configurations)
- database.sql (MySQL database dump)

To restore:
1. Stop the server: docker-compose down
2. Copy world folders back to container
3. Import database: docker exec -i minecraft-mysql mysql -u root -pHh@#2021 minecraft-database < database.sql
4. Start server: docker-compose up -d
EOF

# Compress backup
echo "📦 Compressing backup..."
tar -czf "${BACKUP_DIR}.tar.gz" -C "$(dirname "$BACKUP_DIR")" "$(basename "$BACKUP_DIR")"
rm -rf "$BACKUP_DIR"

echo "✅ Backup completed: ${BACKUP_DIR}.tar.gz"
echo "📊 Backup size: $(du -h "${BACKUP_DIR}.tar.gz" | cut -f1)"

# Clean up old backups (keep last 7 days)
echo "🧹 Cleaning up old backups..."
find ./backups -name "*.tar.gz" -mtime +7 -delete 2>/dev/null || true

echo "🎉 Backup process completed successfully!"
