isEnabled: true
entityType: ZOGLIN
name: $eventBossLevel &cFailed Pigxperiment
level: dynamic
healthMultiplier: 3.5
damageMultiplier: 1.1
uniqueLootList: 
- filename=failed_pigxperiment_drop_sword.yml:amount=1:chance=0.22
powers:
- ground_pound.yml
movementSpeedAttribute: 0.36
followDistance: 80
locationMessage: "&cFailed Pigxperiment: $distance blocks away!"
deathMessage: "&aThe Failed Pigxperiment was slain by $players!"
escapeMessage: "&4The Failed Pigxperiment has escaped!"
announcementPriority: 2