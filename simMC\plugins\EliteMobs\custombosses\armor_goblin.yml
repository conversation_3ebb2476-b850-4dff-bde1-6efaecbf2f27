isEnabled: true
entityType: ZOMBIE
name: $eventBossLevel &3Armor Goblin
level: dynamic
isPersistent: true
healthMultiplier: 4.0
damageMultiplier: 2.0
isBaby: true
deathMessages:
- '&e&l---------------------------------------------'
- '&eThe Armor Goblin has been pillaged!'
- '&c&l    1st Damager: $damager1name &cwith $damager1damage damage!'
- '&6&l    2nd Damager: $damager2name &6with $damager2damage damage!'
- '&e&l    3rd Damager: $damager3name &ewith $damager3damage damage!'
- '&aSlayers: $players'
- '&e&l---------------------------------------------'
uniqueLootList:
- goblin_helmet.yml:0.2
- goblin_chestplate.yml:0.2
- goblin_leggings.yml:0.2
- goblin_boots.yml:0.2
powers:
- gold_explosion.yml
- gold_shotgun.yml
- spirit_walk.yml
trails:
- GOLD_NUGGET
locationMessage: '&cArmor Goblin: $distance blocks away!'
spawnMessage: '&cAn Armor Goblin has been sighted!'
deathMessage: '&aAn Armor Goblin has been slain by $players!'
escapeMessage: '&4An Armor Goblin has escaped!'
customModel: em_goblin_armor
announcementPriority: 2
followDistance: 100
helmet: DIAMOND_HELMET
chestplate: DIAMOND_CHESTPLATE
leggings: DIAMOND_LEGGINGS
boots: DIAMOND_BOOTS
mainHand: SHIELD
offHand: SHIELD
onSpawnBlockStates: []
onRemoveBlockStates: []
bossType: NORMAL
