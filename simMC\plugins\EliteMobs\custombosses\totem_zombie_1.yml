isEnabled: true
entityType: ZOMBIE
name: $eventBossLevel Head Zombie
level: dynamic
isPersistent: true
healthMultiplier: 2.0
damageMultiplier: 2.0
deathMessages:
- '&e&l---------------------------------------------'
- '&4The Zombie Totem been slain!'
- '&c&l    1st Damager: $damager1name &cwith $damager1damage damage!'
- '&6&l    2nd Damager: $damager2name &6with $damager2damage damage!'
- '&e&l    3rd Damager: $damager3name &ewith $damager3damage damage!'
- '&aSlayers: $players'
- '&e&l---------------------------------------------'
powers:
- skeleton_tracking_arrow.yml
- attack_fireball.yml
- hyper_loot.yml
locationMessage: '&cZombie Totem: $distance blocks away!'
mountedEntity: totem_zombie_2.yml
spawnMessage: '&cA Dr. Craftenmine abomination has been sighted!'
deathMessage: '&aDr. Craftenmine''s abomination been terminated by $players!'
escapeMessage: '&4Dr. Craftenmine''s creation has escaped!'
announcementPriority: 2
followDistance: 100
helmet: NETHERITE_HELMET
chestplate: NETHERITE_CHESTPLATE
leggings: NETHERITE_LEGGINGS
boots: NETHERITE_BOOTS
mainHand: NETHERITE_SWORD
onSpawnBlockStates: []
onRemoveBlockStates: []
cullReinforcements: false
bossType: NORMAL
