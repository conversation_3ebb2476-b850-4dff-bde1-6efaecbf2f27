# This is the main configuration file for Spigot.
# As you can see, there's tons to configure. Some options may impact gameplay, so use
# with caution, and make sure you know what each option does before configuring.
# For a reference for any variable inside this file, check out the Spigot wiki at
# http://www.spigotmc.org/wiki/spigot-configuration/
#
# If you need help with the configuration or have any questions related to Spigot,
# join us at the Discord or drop by our forums and leave a post.
#
# Discord: https://www.spigotmc.org/go/discord
# Forums: http://www.spigotmc.org/

advancements:
  disable-saving: false
  disabled:
  - minecraft:story/disabled
commands:
  tab-complete: 0
  send-namespaced: true
  log: true
  replace-commands:
  - setblock
  - summon
  - testforblock
  - tellraw
  spam-exclusions:
  - /skill
  silent-commandblock-console: false
world-settings:
  default:
    below-zero-generation-in-existing-chunks: true
    merge-radius:
      exp: -1.0
      item: 0.5
    hunger:
      jump-walk-exhaustion: 0.05
      jump-sprint-exhaustion: 0.2
      combat-exhaustion: 0.1
      regen-exhaustion: 6.0
      swim-multiplier: 0.01
      sprint-multiplier: 0.1
      other-multiplier: 0.0
    ticks-per:
      hopper-transfer: 8
      hopper-check: 1
    hopper-amount: 1
    hopper-can-load-chunks: false
    simulation-distance: default
    view-distance: default
    wither-spawn-sound-radius: 0
    hanging-tick-frequency: 100
    end-portal-sound-radius: 0
    dragon-death-sound-radius: 0
    zombie-aggressive-towards-villager: true
    enable-zombie-pigmen-portal-spawns: true
    thunder-chance: 100000
    unload-frozen-chunks: false
    arrow-despawn-rate: 1200
    trident-despawn-rate: 1200
    entity-tracking-range:
      players: 128
      animals: 96
      monsters: 200
      misc: 96
      display: 128
      other: 64
    entity-activation-range:
      animals: 32
      monsters: 200
      raiders: 64
      misc: 16
      water: 16
      villagers: 32
      flying-monsters: 32
      wake-up-inactive:
        animals-max-per-tick: 4
        animals-every: 1200
        animals-for: 100
        monsters-max-per-tick: 50
        monsters-every: 400
        monsters-for: 100
        villagers-max-per-tick: 4
        villagers-every: 600
        villagers-for: 100
        flying-monsters-max-per-tick: 8
        flying-monsters-every: 200
        flying-monsters-for: 100
      villagers-work-immunity-after: 100
      villagers-work-immunity-for: 20
      villagers-active-for-panic: true
      tick-inactive-villagers: true
      ignore-spectators: false
    seed-village: 10387312
    seed-desert: 14357617
    seed-igloo: 14357618
    seed-jungle: 14357619
    seed-swamp: 14357620
    seed-monument: 10387313
    seed-shipwreck: 165745295
    seed-ocean: 14357621
    seed-outpost: 165745296
    seed-endcity: 10387313
    seed-slime: 987234911
    seed-nether: 30084232
    seed-mansion: 10387319
    seed-fossil: 14357921
    seed-portal: 34222645
    seed-ancientcity: 20083232
    seed-trailruins: 83469867
    seed-trialchambers: 94251327
    seed-buriedtreasure: 10387320
    seed-mineshaft: default
    seed-stronghold: default
    growth:
      cactus-modifier: 100
      cane-modifier: 100
      melon-modifier: 100
      mushroom-modifier: 100
      pumpkin-modifier: 100
      sapling-modifier: 100
      beetroot-modifier: 100
      carrot-modifier: 100
      potato-modifier: 100
      torchflower-modifier: 100
      wheat-modifier: 100
      netherwart-modifier: 100
      vine-modifier: 100
      cocoa-modifier: 100
      bamboo-modifier: 100
      sweetberry-modifier: 100
      kelp-modifier: 100
      twistingvines-modifier: 100
      weepingvines-modifier: 100
      cavevines-modifier: 100
      glowberry-modifier: 100
      pitcherplant-modifier: 100
    nerf-spawner-mobs: false
    max-tick-time:
      tile: 50
      entity: 50
    max-tnt-per-tick: 100
    mob-spawn-range: 20
    item-despawn-rate: 6000
    verbose: false
  worldeditregentempworld:
    verbose: false
messages:
  whitelist: You are not whitelisted on this server!
  unknown-command: Unknown command. Type "/help" for help.
  server-full: The server is full!
  outdated-client: Outdated client! Please use {0}
  outdated-server: Outdated server! I'm still on {0}
  restart: Server is restarting
players:
  disable-saving: false
config-version: 12
stats:
  disable-saving: false
  forced-stats: {}
settings:
  debug: false
  bungeecord: false
  sample-count: 12
  save-user-cache-on-stop-only: false
  netty-threads: 4
  user-cache-size: 1000
  player-shuffle: 0
  moved-wrongly-threshold: 0.0625
  moved-too-quickly-multiplier: 10.0
  log-villager-deaths: true
  log-named-deaths: true
  timeout-time: 60
  restart-on-crash: true
  restart-script: ./start.sh
  attribute:
    maxAbsorption:
      max: 2048.0
    movementSpeed:
      max: 1024.0
    attackDamage:
      max: 2048.0
    maxHealth:
      max: 100000000
