---- Minecraft Chunk IO Error Report ----
// Computers were a mistake

Time: 2025-02-11 20:52:04
Description: Chunk found in invalid location

java.lang.IllegalStateException: Retrieved chunk position [0, -19] does not match requested [0, -18]
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.createMisplacedChunkReport(ChunkIOErrorReporter.java:14)
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.reportMisplacedChunk(ChunkIOErrorReporter.java:23)
	at net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:357)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:72)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: IO-Worker-1
Stacktrace:
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.createMisplacedChunkReport(ChunkIOErrorReporter.java:14)
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.reportMisplacedChunk(ChunkIOErrorReporter.java:23)
	at net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:357)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101)

-- Misplaced Chunk --
Details:
	Stored Position: [0, -19]
Stacktrace:
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.createMisplacedChunkReport(ChunkIOErrorReporter.java:14)
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.reportMisplacedChunk(ChunkIOErrorReporter.java:23)
	at net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:357)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:72)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)

-- Chunk Info --
Details:
	Level: world
	Dimension: minecraft:overworld
	Storage: chunk
	Position: [0, -18]
Stacktrace:
	at net.minecraft.server.MinecraftServer.lambda$storeChunkIoError$41(MinecraftServer.java:2765)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

-- System Details --
Details:
	Minecraft Version: 1.21.4
	Minecraft Version ID: 1.21.4
	Operating System: Windows 10 (amd64) version 10.0
	Java Version: 21.0.6, Eclipse Adoptium
	Java VM Version: OpenJDK 64-Bit Server VM (mixed mode, sharing), Eclipse Adoptium
	Memory: 14813170112 bytes (14126 MiB) / 16106127360 bytes (15360 MiB) up to 16106127360 bytes (15360 MiB)
	CPUs: 20
	Processor Vendor: GenuineIntel
	Processor Name: 12th Gen Intel(R) Core(TM) i7-12700K
	Identifier: Intel64 Family 6 Model 151 Stepping 2
	Microarchitecture: Alder Lake
	Frequency (GHz): 3.61
	Number of physical packages: 1
	Number of physical CPUs: 12
	Number of logical CPUs: 20
	Graphics card #0 name: Intel(R) UHD Graphics 770
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6129
	Memory slot #0 capacity (MiB): 16384.00
	Memory slot #0 clockSpeed (GHz): 3.20
	Memory slot #0 type: DDR4
	Memory slot #1 capacity (MiB): 16384.00
	Memory slot #1 clockSpeed (GHz): 3.20
	Memory slot #1 type: DDR4
	Virtual memory max (MiB): 34583.84
	Virtual memory used (MiB): 30285.65
	Swap memory total (MiB): 2048.00
	Swap memory used (MiB): 22.54
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 806166.50, total: 953204.19
	Space in storage for workdir (MiB): available: 806166.50, total: 953204.19
	JVM Flags: 2 total; -Xmx15G -Xms15G
	CraftBukkit Information: 
   BrandInfo: Paper (papermc:paper) version 1.21.4-147-main@3bd69f2 (2025-02-10T22:59:40Z)
   Running: Paper version 1.21.4-147-3bd69f2 (MC: 1.21.4) (Implementing API version 1.21.4-R0.1-SNAPSHOT) false
   Plugins: { EliteMobs v9.3.1 com.magmaguy.elitemobs.EliteMobs [MagmaGuy],}
   Warnings: DEFAULT
   Reload Count: 0
   Threads: { RUNNABLE DestroyJavaVM: [], WAITING Paper Common Worker #4: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:107), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], RUNNABLE WindowsStreamPump: [org.jline.nativ.Kernel32.WaitForSingleObject(Native Method), org.jline.terminal.impl.jni.win.NativeWinSysTerminal.processConsoleInput(NativeWinSysTerminal.java:210), org.jline.terminal.impl.AbstractWindowsTerminal.pump(AbstractWindowsTerminal.java:469), org.jline.terminal.impl.AbstractWindowsTerminal$$Lambda/0x0000023c1013dd38.run(Unknown Source), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING User Authenticator #0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING mysql-cj-abandoned-connection-cleanup: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), java.base@21.0.6/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:84), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Timer hack thread: [java.base@21.0.6/java.lang.Thread.sleep0(Native Method), java.base@21.0.6/java.lang.Thread.sleep(Thread.java:509), net.minecraft.Util$8.run(Util.java:905)], WAITING Paper Common Worker #0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:107), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], RUNNABLE Paper Common Worker #1: [java.base@21.0.6/java.lang.Thread.dumpThreads(Native Method), java.base@21.0.6/java.lang.Thread.getAllStackTraces(Thread.java:2522), org.bukkit.craftbukkit.CraftCrashReport.get(CraftCrashReport.java:35), org.bukkit.craftbukkit.CraftCrashReport.get(CraftCrashReport.java:17), net.minecraft.SystemReport.setDetail(SystemReport.java:71), net.minecraft.CrashReport.<init>(CrashReport.java:38), net.minecraft.CrashReport.forThrowable(CrashReport.java:216), net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.createMisplacedChunkReport(ChunkIOErrorReporter.java:14), net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.reportMisplacedChunk(ChunkIOErrorReporter.java:23), net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:357), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:72), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], TIMED_WAITING spark-java-sampler-0-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING spark-java-sampler-0-3: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Finalizer: [java.base@21.0.6/java.lang.Object.wait0(Native Method), java.base@21.0.6/java.lang.Object.wait(Object.java:366), java.base@21.0.6/java.lang.Object.wait(Object.java:339), java.base@21.0.6/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158), java.base@21.0.6/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89), java.base@21.0.6/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)], TIMED_WAITING pool-8-thread-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Reference Handler: [java.base@21.0.6/java.lang.ref.Reference.waitForReferencePendingList(Native Method), java.base@21.0.6/java.lang.ref.Reference.processPendingReferences(Reference.java:246), java.base@21.0.6/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)], TIMED_WAITING Worker-Main-4: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], RUNNABLE Notification Thread: [], TIMED_WAITING Paper Watchdog Thread: [java.base@21.0.6/java.lang.Thread.sleep0(Native Method), java.base@21.0.6/java.lang.Thread.sleep(Thread.java:509), org.spigotmc.WatchdogThread.run(WatchdogThread.java:155)], TIMED_WAITING Yggdrasil Key Fetcher: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING spark-java-sampler-0-5: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Async Task Handler Thread - 1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Craft Async Scheduler Management Thread: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Attach Listener: [], RUNNABLE Server thread: [java.base@21.0.6/sun.nio.ch.IOUtil.write1(Native Method), java.base@21.0.6/sun.nio.ch.WEPollSelectorImpl.wakeup(WEPollSelectorImpl.java:242), io.netty.channel.nio.SelectedSelectionKeySetSelector.wakeup(SelectedSelectionKeySetSelector.java:73), io.netty.channel.nio.NioEventLoop.wakeup(NioEventLoop.java:853), io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:857), io.netty.util.concurrent.SingleThreadEventExecutor.execute0(SingleThreadEventExecutor.java:827), io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:817), net.minecraft.network.Connection.sendPacket(Connection.java:479), net.minecraft.network.Connection.send(Connection.java:440), net.minecraft.server.network.ServerCommonPacketListenerImpl.send(ServerCommonPacketListenerImpl.java:298), net.minecraft.server.network.ServerCommonPacketListenerImpl.send(ServerCommonPacketListenerImpl.java:280), net.minecraft.server.level.ServerEntity.removePairing(ServerEntity.java:307), net.minecraft.server.level.ChunkMap$TrackedEntity.broadcastRemoved(ChunkMap.java:1200), net.minecraft.server.level.ChunkMap.removeEntity(ChunkMap.java:934), net.minecraft.server.level.ServerChunkCache.removeEntity(ServerChunkCache.java:710), net.minecraft.server.level.ServerLevel$EntityCallbacks.onTrackingEnd(ServerLevel.java:2688), net.minecraft.server.level.ServerLevel$EntityCallbacks.onTrackingEnd(ServerLevel.java:2588), ca.spottedleaf.moonrise.patches.chunk_system.level.entity.EntityLookup.entityStatusChange(EntityLookup.java:300), ca.spottedleaf.moonrise.patches.chunk_system.level.entity.EntityLookup$EntityCallback.onRemove(EntityLookup.java:984), net.minecraft.world.entity.Entity.setRemoved(Entity.java:4923), net.minecraft.world.entity.Entity.remove(Entity.java:698), net.minecraft.world.entity.LivingEntity.remove(LivingEntity.java:758), net.minecraft.world.entity.Entity.discard(Entity.java:630), net.minecraft.world.entity.Mob.checkDespawn(Mob.java:828), net.minecraft.server.level.ServerLevel.lambda$tick$4(ServerLevel.java:804), net.minecraft.server.level.ServerLevel$$Lambda/0x0000023c116d2f48.accept(Unknown Source), net.minecraft.world.level.entity.EntityTickList.forEach(EntityTickList.java:39), net.minecraft.server.level.ServerLevel.tick(ServerLevel.java:799), net.minecraft.server.MinecraftServer.tickChildren(MinecraftServer.java:1724), net.minecraft.server.MinecraftServer.tickServer(MinecraftServer.java:1529), net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1251), net.minecraft.server.MinecraftServer.lambda$spin$2(MinecraftServer.java:310), net.minecraft.server.MinecraftServer$$Lambda/0x0000023c10de2318.run(Unknown Source), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Server console handler: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), org.jline.utils.NonBlockingPumpReader.read(NonBlockingPumpReader.java:78), org.jline.utils.NonBlockingReader.read(NonBlockingReader.java:56), org.jline.keymap.BindingReader.readCharacter(BindingReader.java:160), org.jline.keymap.BindingReader.readBinding(BindingReader.java:110), org.jline.keymap.BindingReader.readBinding(BindingReader.java:61), org.jline.reader.impl.LineReaderImpl.doReadBinding(LineReaderImpl.java:923), org.jline.reader.impl.LineReaderImpl.readBinding(LineReaderImpl.java:956), org.jline.reader.impl.LineReaderImpl.readLine(LineReaderImpl.java:651), org.jline.reader.impl.LineReaderImpl.readLine(LineReaderImpl.java:468), net.minecrell.terminalconsole.SimpleTerminalConsole.readCommands(SimpleTerminalConsole.java:158), net.minecrell.terminalconsole.SimpleTerminalConsole.start(SimpleTerminalConsole.java:141), net.minecraft.server.dedicated.DedicatedServer$1.run(DedicatedServer.java:109)], WAITING Worker-Main-5: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING spark-java-sampler-0-4: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 4: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING spark-java-sampler-0-2: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 5: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-2: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Worker-Main-3: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING JNA Cleaner: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), java.base@21.0.6/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), com.sun.jna.internal.Cleaner$CleanerThread.run(Cleaner.java:154)], TIMED_WAITING ForkJoinPool.commonPool-worker-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Worker-Main-6: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING spark-monitoring-thread: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Paper Common Worker #3: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:70), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], TIMED_WAITING Common-Cleaner: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), java.base@21.0.6/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), java.base@21.0.6/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583), java.base@21.0.6/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)], WAITING Paper I/O Worker #0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:107), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], RUNNABLE Netty Server IO #0: [java.base@21.0.6/sun.nio.ch.WEPoll.wait(Native Method), java.base@21.0.6/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114), java.base@21.0.6/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.6/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147), io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68), io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879), io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Netty Server IO #2: [java.base@21.0.6/sun.nio.ch.WEPoll.wait(Native Method), java.base@21.0.6/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114), java.base@21.0.6/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.6/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142), io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:62), io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:883), io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING bStats-Metrics: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], RUNNABLE Signal Dispatcher: [], WAITING Paper Common Worker #2: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:107), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], WAITING Log4j2-AsyncAppenderEventDispatcher-1-Async: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.dispatchAll(AsyncAppenderEventDispatcher.java:81), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.run(AsyncAppenderEventDispatcher.java:73)], WAITING Paper Async Command Builder Thread Pool - 1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Async Task Handler Thread - 0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Netty Server IO #1: [java.base@21.0.6/sun.nio.ch.WEPoll.wait(Native Method), java.base@21.0.6/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114), java.base@21.0.6/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.6/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147), io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68), io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879), io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Async Command Builder Thread Pool - 0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING spark-java-sampler-0-0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)],}
   
   Force Loaded Chunks: { world: {}, world_nether: {}, world_the_end: {},}