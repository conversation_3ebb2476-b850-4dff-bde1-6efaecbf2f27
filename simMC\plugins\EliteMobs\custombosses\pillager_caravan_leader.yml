isEnabled: true
entityType: PILLAGER
name: $eventBossLevel &cPillager Caravan Leader
level: dynamic
isPersistent: true
healthMultiplier: 2.0
damageMultiplier: 2.0
uniqueLootList:
- summon_merchant_scroll.yml:0.5
- summon_merchant_scroll.yml:0.5
- summon_merchant_scroll.yml:0.5
- summon_merchant_scroll.yml:0.5
- summon_merchant_scroll.yml:0.5
powers:
- bonus_loot.yml
- arrow_fireworks.yml
- arrow_rain.yml
- summonType: ON_COMBAT_ENTER
  filename: pillager_caravan_guard.yml
  inheritLevel: true
  spawnNearby: true
- summonType: ON_COMBAT_ENTER
  filename: pillager_caravan_guard.yml
  inheritLevel: true
  spawnNearby: true
locationMessage: '&cPillager Caravan: $distance blocks away!'
mountedEntity: pillager_caravan_beast.yml
spawnMessage: '&cA pillager caravan has been sighted!'
announcementPriority: 2
followDistance: 100
onSpawnBlockStates: []
onRemoveBlockStates: []
bossType: NORMAL
