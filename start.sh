#!/bin/bash

# Minecraft Server Docker Start Script
# This script starts the Minecraft server and MySQL database containers

set -e  # Exit on any error

echo "🚀 Starting Minecraft Server..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

# Check if containers are built
if ! docker images | grep -q "simmc"; then
    echo "🔨 Containers not found. Building them first..."
    ./build.sh
fi

# Start the containers
echo "🐳 Starting Docker containers..."
docker-compose up -d

# Wait for MySQL to be ready
echo "⏳ Waiting for MySQL to be ready..."
timeout=60
counter=0
while ! docker exec minecraft-mysql mysqladmin ping -h localhost -u root -pHh@#2021 --silent; do
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo "❌ MySQL failed to start within $timeout seconds"
        exit 1
    fi
done

echo "✅ MySQL is ready!"

# Wait for Minecraft server to be ready
echo "⏳ Waiting for Minecraft server to be ready..."
timeout=120
counter=0
while ! docker exec minecraft-server nc -z localhost 25565; do
    sleep 5
    counter=$((counter + 5))
    if [ $counter -ge $timeout ]; then
        echo "⚠️  Minecraft server is taking longer than expected to start"
        echo "   Check logs with: docker-compose logs minecraft"
        break
    fi
done

if docker exec minecraft-server nc -z localhost 25565; then
    echo "✅ Minecraft server is ready!"
else
    echo "⚠️  Minecraft server may still be starting..."
fi

echo ""
echo "🎮 Minecraft Server Status:"
echo "   Server: localhost:25565"
echo "   phpMyAdmin: http://localhost:8080"
echo "   Database: localhost:3306"
echo ""
echo "📋 Useful commands:"
echo "   - View server logs: docker-compose logs -f minecraft"
echo "   - View all logs: docker-compose logs -f"
echo "   - Stop server: docker-compose down"
echo "   - Restart server: docker-compose restart minecraft"
echo "   - Server console: docker exec -it minecraft-server bash"
echo "   - MySQL console: docker exec -it minecraft-mysql mysql -u hamza -p minecraft-database"
