#!/bin/bash

# Minecraft Server Restore Script
# This script restores world data and database from backup

set -e  # Exit on any error

if [ $# -eq 0 ]; then
    echo "❌ Usage: $0 <backup_file.tar.gz>"
    echo "Available backups:"
    ls -la ./backups/*.tar.gz 2>/dev/null || echo "No backups found"
    exit 1
fi

BACKUP_FILE="$1"
TEMP_DIR="./temp_restore"

if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Backup file not found: $BACKUP_FILE"
    exit 1
fi

echo "🔄 Restoring from backup: $BACKUP_FILE"
echo "⚠️  WARNING: This will overwrite current world and database data!"
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Restore cancelled"
    exit 1
fi

# Stop the server
echo "🛑 Stopping Minecraft server..."
docker-compose down

# Extract backup
echo "📦 Extracting backup..."
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"
tar -xzf "$BACKUP_FILE" -C "$TEMP_DIR" --strip-components=1

# Start only MySQL for database restore
echo "🗃️  Starting MySQL for database restore..."
docker-compose up -d mysql

# Wait for MySQL to be ready
echo "⏳ Waiting for MySQL to be ready..."
timeout=60
counter=0
while ! docker exec minecraft-mysql mysqladmin ping -h localhost -u root -pHh@#2021 --silent; do
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo "❌ MySQL failed to start within $timeout seconds"
        exit 1
    fi
done

# Restore database if backup exists
if [ -f "$TEMP_DIR/database.sql" ]; then
    echo "🗃️  Restoring database..."
    docker exec -i minecraft-mysql mysql -u root -pHh@#2021 minecraft-database < "$TEMP_DIR/database.sql"
    echo "✅ Database restored successfully"
else
    echo "⚠️  No database backup found, skipping database restore"
fi

# Start the full server
echo "🚀 Starting Minecraft server..."
docker-compose up -d

# Wait for server to be ready
echo "⏳ Waiting for Minecraft server to be ready..."
timeout=60
counter=0
while ! docker exec minecraft-server test -f /opt/minecraft/server.jar; do
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo "❌ Server failed to start within $timeout seconds"
        break
    fi
done

# Restore world data
if [ -d "$TEMP_DIR/world" ]; then
    echo "🌍 Restoring world data..."
    docker cp "$TEMP_DIR/world" minecraft-server:/opt/minecraft/
    echo "✅ Overworld restored"
fi

if [ -d "$TEMP_DIR/world_nether" ]; then
    echo "🔥 Restoring Nether..."
    docker cp "$TEMP_DIR/world_nether" minecraft-server:/opt/minecraft/
    echo "✅ Nether restored"
fi

if [ -d "$TEMP_DIR/world_the_end" ]; then
    echo "🌌 Restoring End..."
    docker cp "$TEMP_DIR/world_the_end" minecraft-server:/opt/minecraft/
    echo "✅ End restored"
fi

# Restore plugin data
if [ -d "$TEMP_DIR/plugins" ]; then
    echo "🔌 Restoring plugin data..."
    docker cp "$TEMP_DIR/plugins/." minecraft-server:/opt/minecraft/plugins/
    echo "✅ Plugin data restored"
fi

# Set proper permissions
echo "🔧 Setting proper permissions..."
docker exec minecraft-server chown -R minecraft:minecraft /opt/minecraft/world*
docker exec minecraft-server chown -R minecraft:minecraft /opt/minecraft/plugins

# Restart server to apply changes
echo "🔄 Restarting server to apply changes..."
docker-compose restart minecraft

# Clean up
rm -rf "$TEMP_DIR"

echo "🎉 Restore completed successfully!"
echo "📋 Server should be available at localhost:25565"
echo "📊 Check logs with: docker-compose logs -f minecraft"
