bossType: MINIBOSS
damageMultiplier: 1.2
dropsEliteMobsLoot: false
dropsRandomLoot: false
dropsVanillaLoot: false
eliteScript:
  StrikeBolt:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 0,0,20
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt2:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 0,0,-20
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt20:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 0,0,10
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt22:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 0,0,-10
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt23:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 10,0,0
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt24:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: -10,0,0
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt25:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: -5,0,-5
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt26:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 5,0,5
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt27:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: -5,0,5
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt28:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 5,0,-5
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt3:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 20,0,0
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt4:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: -20,0,0
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt5:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: -15,0,-15
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt6:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 15,0,15
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt7:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: -15,0,15
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  StrikeBolt8:
    Actions:
    - Target:
        coverage: 0.4
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: ELECTRIC_SPARK
      repeatEvery: 10
      times: 13
    - Target:
        coverage: 0.05
        targetType: ZONE_FULL
        track: true
      action: STRIKE_LIGHTNING
      wait: 125
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SET_ON_FIRE
      duration: 155
      repeatEvery: 5
      times: 4
      wait: 128
    - Target:
        targetType: ZONE_FULL
      action: DAMAGE
      multiplier: 0.5
      repeatEvery: 5
      times: 4
      wait: 128
    Zone:
      Target:
        offset: 15,0,-15
        targetType: SELF_SPAWN
      height: 2
      radius: 6
      shape: CYLINDER
  Trigger:
    Actions:
    - action: RUN_SCRIPT
      onlyRunOneScript: true
      scripts:
      - StrikeBolt
      - StrikeBolt2
      - StrikeBolt3
      - StrikeBolt4
      - StrikeBolt5
      - StrikeBolt6
      - StrikeBolt7
      - StrikeBolt8
      - StrikeBolt20
      - StrikeBolt22
      - StrikeBolt23
      - StrikeBolt24
      - StrikeBolt25
      - StrikeBolt26
      - StrikeBolt27
      - StrikeBolt28
    - action: RUN_SCRIPT
      onlyRunOneScript: true
      scripts:
      - StrikeBolt
      - StrikeBolt2
      - StrikeBolt3
      - StrikeBolt4
      - StrikeBolt5
      - StrikeBolt6
      - StrikeBolt7
      - StrikeBolt8
      - StrikeBolt20
      - StrikeBolt22
      - StrikeBolt23
      - StrikeBolt24
      - StrikeBolt25
      - StrikeBolt26
      - StrikeBolt27
      - StrikeBolt28
    - action: RUN_SCRIPT
      onlyRunOneScript: true
      scripts:
      - StrikeBolt
      - StrikeBolt2
      - StrikeBolt3
      - StrikeBolt4
      - StrikeBolt5
      - StrikeBolt6
      - StrikeBolt7
      - StrikeBolt8
      - StrikeBolt20
      - StrikeBolt22
      - StrikeBolt23
      - StrikeBolt24
      - StrikeBolt25
      - StrikeBolt26
      - StrikeBolt27
      - StrikeBolt28
    - action: RUN_SCRIPT
      onlyRunOneScript: true
      scripts:
      - StrikeBolt
      - StrikeBolt2
      - StrikeBolt3
      - StrikeBolt4
      - StrikeBolt5
      - StrikeBolt6
      - StrikeBolt7
      - StrikeBolt8
      - StrikeBolt20
      - StrikeBolt22
      - StrikeBolt23
      - StrikeBolt24
      - StrikeBolt25
      - StrikeBolt26
      - StrikeBolt27
      - StrikeBolt28
      wait: 120
    - action: RUN_SCRIPT
      onlyRunOneScript: true
      scripts:
      - StrikeBolt
      - StrikeBolt2
      - StrikeBolt3
      - StrikeBolt4
      - StrikeBolt5
      - StrikeBolt6
      - StrikeBolt7
      - StrikeBolt8
      - StrikeBolt20
      - StrikeBolt22
      - StrikeBolt23
      - StrikeBolt24
      - StrikeBolt25
      - StrikeBolt26
      - StrikeBolt27
      - StrikeBolt28
      wait: 240
    Cooldowns:
      global: 60
      local: 140
    Events:
    - EliteMobDamagedByPlayerEvent
entityType: RABBIT
followDistance: 60
frozen: false
healthMultiplier: 3.0
instanced: true
isEnabled: true
isRegionalBoss: true
leashRadius: 60
movementSpeedAttribute: 0.56
name: $minibossLevel &eEnergized Bunny
normalizedCombat: true
powers:
- attack_arrow.yml
- invulnerability_fire.yml
spawnLocations:
- em_id_enchantment_challenge_2,1.5,65,0.5,0,0
trails:
- ELECTRIC_SPARK
uniqueLootList:
- chance: 0.15
  difficultyID: 0
  filename: ec_02_axe_normal.yml
- chance: 0.25
  difficultyID: 0
  filename: ec_02_chestplate_normal.yml
- chance: 0.25
  difficultyID: 0
  filename: ec_02_leggings_normal.yml
- chance: 0.05
  difficultyID: 0
  filename: enchanted_book_lightning.yml
