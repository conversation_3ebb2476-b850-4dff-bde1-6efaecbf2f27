####################################################################################################
#                                     -<( AdvancedTeleport )>-                                     #
#                               Made by Niestrat99 and Thatsmusic99                                #
#                                                                                                  #
# ------------------------------------------------------------------------------------------------ #
# A rapidly growing teleportation plugin looking to break the boundaries of traditional teleport   #
# plugins.                                                                                         #
#                                                                                                  #
# SpigotMC - https://www.spigotmc.org/resources/advanced-teleport.64139/                           #
# Wiki - https://tm-holly.gitbook.io/advancedteleport/                                             #
# Discord - https://discord.gg/mgWbbN4                                                             #
####################################################################################################

# Another comment at the very top for all you lads :)

##############
#  Features  #
##############

# Whether basic teleportation features should be enabled or not.
# This includes /tpa, /tpahere, /tpblock, /tpunblock and /back.
# This does not disable the command for other plugins - if you want other plugins to use the provided commands, use Bukkit's commands.yml file.
# Please refer to https://bukkit.gamepedia.com/Commands.yml for this!
use-basic-teleport-features: true

# Whether warps should be enabled in the plugin.
use-warps: true

# Whether the plugin should modify spawn/spawn properties.
use-spawn: true

# Whether the plugin should allow random teleportation.
use-randomtp: true

# Whether homes should be enabled in the plugin.
use-homes: true

# The commands that AT should not register upon starting up.
# In other words, this gives up the command for other plugins to use.
# NOTE: If you are using Essentials with AT and want AT to give up its commands to Essentials, Essentials does NOT go down without a fight. Jesus Christ. You'll need to restart the server for anything to change.
# To use this section, use the following format:
# disabled-commands:
# - back
disabled-commands: []

#########################
#  Teleport Requesting  #
#########################

# How long tpa and tpahere requests last before expiring.
request-lifetime: 60

# Whether or not the plugin should enable the use of multiple requests.
# When enabled, user 1 may get TPA requests from user 2 and 3, but user 1 is prompted to select a specific request.
# When this is disabled and user 1 receives requests from user 2 and then 3, they will only have user 3's request to respond to.
allow-multiple-requests: true

# Let the player know when their request has timed out or been displaced by another user's request.
# Displacement only occurs when allow-multiple-requests is disabled.
notify-on-expire: true

####################
#  Warm-Up Timers  #
####################

# The number of seconds it takes for the teleportation to take place following confirmation.
# (i.e. "You will teleport in 3 seconds!")
# This acts as the default option for the per-command warm-ups.
warm-up-timer-duration: 3

# Whether or not teleportation should be cancelled if the player rotates or moves.
cancel-warm-up-on-rotation: true

# Whether or not teleportation should be cancelled upon movement only.
cancel-warm-up-on-movement: true

# Whether the plugin should check for change in exact X, Y and Z vs. block X, Y, Z.
# By default, the player will have to cross into a new block (e.g. 60x 60y 60z -> 61x 60y 60z) to cancel the teleportation.
check-exact-coordinates: false

# Command-specific warm-ups.
per-command-warm-ups:
  # Warm-up timer for /tpa.
  tpa: default
  # Warm-up timer for /tpahere
  tpahere: default
  # Warm-up timer for /tpr, or /rtp.
  tpr: default
  # Warm-up timer for /warp
  warp: default
  # Warm-up timer for /spawn
  spawn: default
  # Warm-up timer for /home
  home: default
  # Warm-up timer for /back
  back: default

# Use this section to create custom warm-ups per-group.
# Use the following format:
# custom-warm-ups:
#   vip-warm-up: 3
# Giving a group, such as VIP, the permission at.member.timer.vip-warm-up will have a warm-up of 3.
# The key (vip-warm-up) and group name (VIP) do not have to be different, this is just an example.
# You can also add at.member.timer.3, but this is more efficient if you find permissions lag.To make it per-command, use at.member.timer.<command>.vip-warm-up. To make it per-world, use at.member.timer.<world>.vip-warm-up.
# To combine the two, you can use at.member.timer.<command>.<world>.vip-warm-up.
custom-warm-ups: {}

# Gives the teleporting player a blindness effect whilst waiting to teleport.
blindness-on-warmup: false

###############
#  Cooldowns  #
###############

# How long before the user can use a command again.
# This stops users spamming commands repeatedly.
# This is also the default cooldown period for all commands.
cooldown-duration: 5

# Adds the warm-up duration to the cooldown duration.
# For example, if the cooldown duration was 5 seconds but the warm-up was 3, the cooldown becomes 8 seconds long.
add-cooldown-duration-to-warm-up: true

# Whether or not the cooldown of one command will stop a user from using all commands.
# For example, if a player used /tpa with a cooldown of 10 seconds but then used /tpahere with a cooldown of 5, the 10-second cooldown would still apply.
# On the other hand, if a player used /tpahere, the cooldown of 5 seconds would apply to /tpa and other commands.
apply-cooldown-to-all-commands: false

# When to apply the cooldown
# Options include:
# - request - Cooldown starts as soon as any teleport command is made and still applies even if no teleport takes place (i.e. cancelled by movement or not accepted).
# - accept - Cooldown starts only when the teleport request is accepted (with /tpyes) and still applies even if no teleport takes place (i.e. cancelled by movement).
# - teleport - Cooldown starts only when the teleport actually happens.
# Note:
# 'request' and 'accept' behave the same for /rtp, /back, /spawn, /warp, and /home
# cooldown for /tpall always starts when the command is ran, regardless if any player accepts or teleports
apply-cooldown-after: request

# Command-specific cooldowns.
per-command-cooldowns:
  # Cooldown for /tpa.
  tpa: default
  # Cooldown for /tpahere
  tpahere: default
  # Cooldown for /tpr, or /rtp.
  tpr: default
  # Cooldown for /warp
  warp: default
  # Cooldown for /spawn
  spawn: default
  # Cooldown for /home
  home: default
  # Cooldown for /back
  back: default

# Use this section to create custom cooldowns per-group.
# Use the following format:
# custom-cooldowns:
#   vip-cooldown: 3
# Giving a group, such as VIP, the permission at.member.cooldown.vip-cooldown will have a cooldown of 3.
# The key (vip-cooldown) and group name (VIP) do not have to be different, this is just an example.
# You can also add at.member.cooldown.3, but this is more efficient if you find permissions lag.To make it per-command, use at.member.cooldown.<command>.vip-cooldown. To make it per-world, use at.member.cooldown.<world>.vip-cooldown.
# To combine the two, you can use at.member.cooldown.<command>.<world>.vip-cooldown.
custom-cooldowns: {}

#########################
#  Teleportation Costs  #
#########################

# The amount it costs to teleport somewhere.
# If you want to use Vault Economy, use 100.0 to charge $100.
# If you have multiple plugins hooking into Vault, enter the plugin name in front separated by a colon, e.g. Essentials:100.50
# Do note some plugins require Vault support to be toggled on manually.
# If you want to use Minecraft EXP points, use 10EXP for 10 EXP Points.
# If you want to use Minecraft EXP levels, use 5LVL for 5 levels.
# If you want to use items, use the format MATERIAL:AMOUNT or MATERIAL:AMOUNT:BYTE.
# For example, on 1.13+, ORANGE_WOOL:3 for 3 orange wool, but on versions before 1.13, WOOL:3:1.
# If you're on a legacy version and unsure on what byte to use, see https://minecraftitemids.com/types
# To use multiple methods of charging, use a ; - e.g. '100.0;10LVL' for $100 and 10 EXP levels.
# To disable, just put an empty string, i.e. ''
cost-amount: 100.0

# Command-specific costs.
per-command-cost:
  # Cost for /tpa.
  tpa: default
  # Cost for /tpahere.
  tpahere: default
  # Cost for /tpr, or /rtp.
  tpr: default
  # Cost for /warp
  warp: default
  # Cost for /spawn
  spawn: default
  # Cost for /home
  home: default
  # Cost for /back
  back: default

# Use this section to create custom costs per-group.
# Use the following format:
# custom-costs:
#   vip-cost: Essentials:100
# Giving a group, such as VIP, the permission at.member.cost.vip-cost will have a cost of $100.
# To make it per-command, add the permission at.member.cost.tpa.vip-cost (for tpa) instead.
custom-costs: {}

###############
#  Particles  #
###############

# Whether particles should be used in the plugin.
# Some standalone implementation is used, but otherwise, PlayerParticles is used.
use-particles: true

# The default waiting particles during the warm-up period.
default-waiting-particles: ''

# Command-specific waiting particles.
waiting-particles:
  tpa: default
  tpahere: default
  tpr: default
  warp: default
  spawn: default
  home: default
  back: default

# The default particles used as soon as the player teleports. 
# At this time, only spark is supported. However, other recommendations are welcome with that.
default-teleporting-particles: spark

# Command-specific teleporting particles.
teleporting-particles:
  tpa: default
  tpahere: default
  tpr: default
  warp: default
  spawn: default
  home: default
  back: default

#################
#  SQL Storage  #
#################

# Whether the plugin should use SQL storage or not.
# By default, AT uses SQLite storage, which stores data in a .db file locally.
use-mysql: false

# The MySQL host to connect to.
mysql-host: 127.0.0.1

# The port to connect to.
mysql-port: 3306

# The database to connect to.
mysql-database: database

# The username to use when connecting.
mysql-username: username

# The password to use when connecting.
mysql-password: password

# The prefix of all AT tables. 
# If you're on Bungee, you may want to add your server's name to the end.
mysql-table-prefix: advancedtp

# Whether or not to connect to the MySQL server using SSL.
use-ssl: false

# Whether or not the plugin should reconnect to the MySQL server when a connection is closed.
auto-reconnect: true

# Whether or not to enable public key retrieval. 
# Please do not enable it without being explicitly told by one of the developers.
allow-public-key-retrieval: false

##########################
#  Distance Limitations  #
##########################

# Enables the distance limiter to stop players teleporting over a large distance.
# This is only applied when people are teleporting in the same world.
enable-distance-limitations: false

# The maximum distance that a player can teleport.
# This is the default distance applied to all commands when specified.
maximum-teleport-distance: 1000

# Whether or not all teleports - not just AT's - should be checked for distance.
monitor-all-teleports-distance: false

# Determines the distance limit for each command.
per-command-distance-limitations:
  # Distance limit for /tpa
  tpa: default
  # Distance limit for /tpahere
  tpahere: default
  # Distance limit for /tpr
  tpr: default
  # Distance limit for /warp
  warp: default
  # Distance limit for /spawn
  spawn: default
  # Distance limit for /home
  home: default
  # Distance limit for /back
  back: default

# Use this section to create custom distance limitations per-group.
# Use the following format:
# custom-distance-limitations:
#   vip-distance: 300000
# Giving a group, such as VIP, the permission at.member.distance.vip-distance will have a maximum distance of 300000.
# The key (vip-distance) and group name (VIP) do not have to be different, this is just an example.
# You can also add at.member.distance.300000, but this is more efficient if you find permissions lag. To make it per-command, use at.member.distance.<command>.vip-distance. To make it per-world, use at.member.distance.<world>.vip-distance.
# To combine the two, you can use at.member.distance.<command>.<world>.vip-distance.
custom-distance-limitations: {}

###############################
#  Teleportation Limitations  #
###############################

# WARNING: A lot of the options below are considered advanced and use special syntax that is not often accepted in YAML.
# When using such options, wrap them in quotes: ''
# As an example, 'stop-teleportation-out:world,world_nether'

# Enables teleport limitations. This means cross-world or even world teleportation can be limited within specific worlds.
enable-teleport-limitations: false

# Whether or not all teleportation - not just AT's - should be checked to see if teleportation is allowed.
monitor-all-teleports-limitations: false

# The teleportation rules defined for each world.
# Rules include:
# - stop-teleportation-out - Stops players teleporting to another world when they are in this world.
# - stop-teleportation-within - Stops players teleporting within the world.
# - stop-teleportation-into - Stops players teleporting into this world.
# To combine multiple rules, use a ; - e.g. stop-teleportation-out;stop-teleportation-within
# For out and into rules, you can make it so that rules only initiate when in or going to a specific world using :, e.g. stop-teleportation-out:world stops players teleporting to "world" in the world they're currently in.
# To do the opposite (i.e. initiates the rule when users are not in the specified world), use !, e.g. stop-teleportation-into!world stops teleportation into a specific world if they are not in "world". If ! and : are used in the same rule, then : is given top priority.To make this rule work with multiple worlds, use a comma (,), e.g. stop-teleportation-into:world,world_nether
world-rules:
  default: stop-teleportation-within
  world: default
  world_nether: stop-teleportation-into!world

# The teleportation rules defined for each AT command.
# Rules include:
# - override - The command will override world rules and run regardless.
# - ignore - The command will refuse to run regardless of world rules.
# To combine multiple rules, use a ;.
# To make rules behave differently in different worlds, use : to initiate the rule in a specific world (e.g. override:world to make the command override "world"'s rules.)
# To initiate rules outside of a specific world, use ! (e.g. override!world to make the command override world rules everywhere but in world)
# To use multiple worlds, use a comma (,).
# By default, all commands will comply with the world rules. If no rules are specified, they will comply.
# All worlds specified will be considered the world in which the player is currently in. For worlds being teleported to, add > to the start of the world name.
# For example, ignore:world,>world_nether will not run if the player is in "world" or if the player is going into the Nether.
command-rules:
  tpa: ''
  tpahere: ''
  tpr: ''
  warp: ''
  spawn: ''
  home: ''
  back: ''

##############
#  RandomTP  #
##############

# Defines the range of X coordinates that players can teleport to.
# Using a value for example 5000 would automatically set the minimum to -5000.
# These are able to be defined for each world by name.
# Split the values with a semicolon (;).
# If a world is defined here but not in the z section, the x values will be reused for the z coords.
x:
  default: 5000;-5000
  world_the_end: 10000;-10000

# Defines the range of z coordinates that players can teleport to.
# Using a value for example 5000 would automatically set the minimum to -5000.
# These are able to be defined for each world by name.
# Split the values with a semicolon (;).
# If a world is defined here but not in the x section, the z values will be reused for the x coords.
z:
  default: 5000;-5000
  world_the_end: 10000;-10000

# Deprecated
#  # The maximum X coordinate to go up to when selecting a random location.
maximum-x: 5000

# Deprecated
#  # The maximum Z coordinate to go up to when selecting a random location.
maximum-z: 5000

# Deprecated
#  # The minimum X coordinate to go down to when selecting a random location.
minimum-x: -5000

# Deprecated
#  # The minimum Z coordinate to go down to when selecting a random location.
minimum-z: -5000

# Use the new rapid response system for RTP.
# This means valid locations are prepared before a user chooses to use /tpr or interact with a sign, meaning they are ready for use and can instantly TP a player.
# This feature allows you to use the "tpr" death option in the death management section further down.
# IMPORTANT NOTE - this feature only works on the Paper server type and any of its forks. It is not considered safe to use on Spigot or Bukkit.
use-rapid-response: true

# Whether the plugin should use the Vanilla world border as a viable option for managing /tpr boundaries.
use-vanilla-border: false

# Whether the plugin should use plugin world borders for managing /tpr boundaries.
# Currently supported plugins are WorldBorder and ChunkyBorder.
use-plugin-borders: true

# If enabled checks if the player is in either an unclaimed area or that they have build permission in the area.
# Supported plugins are Lands, WorldGuard, and GriefPrevention.
protect-claim-locations: true

# How many locations can be prepared per world when using AT's Rapid Response system.
# These are immediately prepared upon startup and when a world is loaded.
prepared-locations-limit: 3

# AT's Rapid Response system automatically loads locations for each world, but can be problematic on some worlds, mostly SkyBlock worlds.
# In response, this list acts as pro-active protection and ignores worlds generated using the following generators.
# This is provided as an option so you can have control over which worlds have locations load.
ignore-world-generators:
- us.talabrek.ultimateskyblock.world.SkyBlockChunkGenerator
- us.talabrek.ultimateskyblock.world.SkyBlockNetherChunkGenerator
- world.bentobox.bskyblock.generators.ChunkGeneratorWorld
- world.bentobox.acidisland.world.ChunkGeneratorWorld
- world.bentobox.oneblock.generators.ChunkGeneratorWorld
- com.wasteofplastic.askyblock.generators.ChunkGeneratorWorld
- com.wasteofplastic.acidisland.generators.ChunkGeneratorWorld
- b.a
- com.chaseoes.voidworld.VoidWorld.VoidWorldGenerator
- club.bastonbolado.voidgenerator.EmptyChunkGenerator
- de.xtkq.voidgen.generator.interfaces.ChunkGen

# Blocks that people must not be able to land in when using /tpr.
avoid-blocks:
- WATER
- LAVA
- STATIONARY_WATER
- STATIONARY_LAVA

# Biomes that the plugin should avoid when searching for a location.
avoid-biomes:
- OCEAN
- DEEP_OCEAN

# Whether or not /tpr should only be used in the worlds listed below.
whitelist-worlds: true

# Whether or not players should be directed to a whitelisted world when using /tpr.
# When this option is disabled and the player tries to use /tpr in a non-whitelisted world, the command simply won't work.
redirect-to-whitelisted-worlds: false

# Worlds you can use /tpr in.
# If a player uses /tpr in a world that doesn't allow it, they will be teleported in the first world on the list instead.
# To make this feature effective, turn on "whitelist-worlds" above.
allowed-worlds:
- em_world_the_end
- em_world_nether
- em_survival
- x_survival_spawner

###########
#  Homes  #
###########

# The default maximum of homes people can have.
# This can be overridden by giving people permissions such as at.member.homes.10.
# To disable this, use -1 as provided by default.
default-homes-limit: -1

# Whether or not the bed home should be added to /homes.
add-bed-to-homes: true

# Whether or not players should be denied access to some of their homes if they exceed their homes limit.
# The homes denied access to will end up being their most recently set homes.
# For example, having homes A, B, C, D and E with a limit of 3 will deny access to D and E.
deny-homes-if-over-limit: false

# If homes should be hidden from /homes should they be denied access.
# If this is false, they will be greyed out in the /homes list.
hide-homes-if-denied: false

# When enabled, setting homes with a name that already exists in your list gets overwritten.
overwrite-sethome: false

# Shows a list of homes the player has when doing /home and nothing else.
# This overwrites /home when attempting to teleport to their main home, but if you're more used to what Essentials does, set this to true.
show-homes-with-no-input: false

# If the player has a main home set, then the option above is ignored. I gotta be flexible.
prioritise-main-home: true

##########################
#  Notifications/Sounds  #
##########################

# The sound played when a player receives a teleportation (tpa) request.
# For 1.16+, check https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Sound.html for a list of sounds you can use
# For 1.15 and below, check https://www.spigotmc.org/threads/sounds-spigot-1-7-1-14-4-sound-enums.340452/ for a list of sounds down to 1.7.
# (Friendly reminder that 1.7.x is not supported though!)
# Set to "none" if you want no sound playing.
tpa-request-received: none

# The sound played when a player sends a teleportation (tpa) request.
tpa-request-sent: none

# The sound played when a player receives a teleportation (tpahere) request.
tpahere-request-received: none

# The sound played when a player sends a teleportation (tpahere) request.
tpahere-request-sent: none

##########
#  Back  #
##########

# The teleport causes that the plugin must listen to allow players to teleport back to the previous location.
# You can see a full list of these causes at https://hub.spigotmc.org/javadocs/spigot/org/bukkit/event/player/PlayerTeleportEvent.TeleportCause.html
used-teleport-causes:
- COMMAND
- PLUGIN
- SPECTATE

# The cubic radius to search for a safe block when using /back.
# If a player teleports from an unsafe location and uses /back to return to it, the plugin will search all blocks within this radius to see if it is a safe place for the player to be moved to.
# It is recommend to avoid setting this option too high as this can have a worst case execution time of O(n^3) (e.g. run 27 times, 64, 125, 216 and so on).
# To disable, either set to 0 or -1.
back-search-radius: 5

############################
#  Map Plugin Integration  #
############################

# At this time, AdvancedTeleport supports dynmap and squaremap.
# If you are using dynmap, the plugin has extra icons you can use as placeholders.

# Covers map options for homes.
homes:
  # Whether the icons for homes will be added at all.
  enabled: false
  # The default icon for homes in the map.
  default-icon: home-default
  # Whether the player viewing the map has to explicitly enable the layer to view homes on the map.
  shown-by-default: true
  # The tooltip that will appear when someone hovers over the icon in the map.
  # For Dynmap, this supports HTML formatting.
  hover-tooltip: '{name}'
  # Squaremap only - the tooltip that will appear when someone clicks on the icon.
  click-tooltip: '{name}'
  # The scale of the icon on the map.
  # With Dynmap, only 8, 16 and 32 are supported. With Squaremap, 2147483647 is your limit. But don't try it.
  icon-size: '32'
  # The layer display name that appears on the map.
  layer-name: Homes

# Covers map options for warps.
warps:
  # Whether the icons for warps will be added at all.
  enabled: true
  # The default icon for warps in the map.
  default-icon: warp-default
  # Whether the player viewing the map has to explicitly enable the layer to view warps on the map.
  shown-by-default: true
  # The tooltip that will appear when someone hovers over the icon in the map.
  # For Dynmap, this supports HTML formatting.
  hover-tooltip: '{name}'
  # Squaremap only - the tooltip that will appear when someone clicks on the icon.
  click-tooltip: '{name}'
  # The scale of the icon on the map.
  # With Dynmap, only 8, 16 and 32 are supported. With Squaremap, 2147483647 is your limit. But don't try it.
  icon-size: '32'
  # The layer display name that appears on the map.
  layer-name: Warps

# Covers map options for spawns.
spawns:
  # Whether the icons for spawns will be added at all.
  enabled: true
  # The default icon for spawns in the map.
  default-icon: spawn-default
  # Whether the player viewing the map has to explicitly enable the layer to view spawns on the map.
  shown-by-default: true
  # The tooltip that will appear when someone hovers over the icon in the map.
  # For Dynmap, this supports HTML formatting.
  hover-tooltip: '{name}'
  # Squaremap only - the tooltip that will appear when someone clicks on the icon.
  click-tooltip: '{name}'
  # The scale of the icon on the map.
  # With Dynmap, only 8, 16 and 32 are supported. With Squaremap, 2147483647 is your limit. But don't try it.
  icon-size: '32'
  # The layer display name that appears on the map.
  layer-name: Spawns

# Whether to make spawnpoints visible for everyone on the map.
add-spawns: true

# Whether to make warps visible for everyone on the map.
add-warps: true

# Whether to make all homes visible for everyone on the map.
add-homes: false

# The default icon size for AT's icons on the map.
default-icon-size: 40

######################
#  Spawn Management  #
######################

# Whether the player should be teleported to the spawnpoint when they join for the first time.
teleport-to-spawn-on-first-join: true

# The name of the spawnpoint players will be first teleported to if they joined for the first time.
# If it is blank, then it will take the main spawnpoint.
first-spawn-point: ''

# Whether the player should be teleported to the spawnpoint every time they join.
teleport-to-spawn-on-every-join: false

# Whether using /spawn, joining or respawning should send the user to the closest spawnpoint they have access to.
# If the user doesn't have permission to the specified spawnpoint, then they are not sent to it.
# Only spawns in the same dimension/world are considered. If no spawnpoint is set in the same dimension, then the normal main spawn is used.
teleport-to-nearest-spawnpoint: false

# If no main spawn has been set and the world being checked is in the Nether or End, use the Overworld spawn instead (if applicable).
use-overworld: true

# Determines how and where players teleport when they die.
# Options include:
# - spawn - Teleports the player to the spawnpoint of either the world or specified by the plugin.
# - bed - Teleports to the player's bed.
# - anchor - 1.16+ only, teleports to the player's respawn anchor. However, due to limitations with Spigot's API, it may or may not always work. (add Player#getRespawnAnchor pls)
# - warp:Warp Name - Teleports the player to a specified warp. For example, if you want to teleport to Hub, you'd type warp:Hub
# - tpr - Teleports the player to a random location. Can only be used when the rapid response system is enabled.
# - home - Teleports the player to their main or first home.
# - default - Uses the default respawn option, which is spawn unless set differently.
# If you're using EssentialsX Spawn and want AT to take over respawn mechanics, set respawn-listener-priority in EssX's config.yml file to lowest.
death-management:
  default: bed;spawn
  world: default
  special-world: warp:Special
  another-world: bed

#################
#  Permissions  #
#################

# The default permissions given to users without OP.
# By default, Advanced Teleport allows users without OP to use all member features.
# This allows for permission management without a permissions plugin, especially if a user doesn't understand how such plugins work.
# However, if you have a permissions plugin and Vault installed, you cannot make admin permissions work by default.
default-permissions:
- at.member.*
- at.member.warp.*
- at.member.warp.sign.*
- at.member.core.help
- at.member.core.info

# Allows admin permissions to be allowed as default permissions by default.
# If you want to use admin permissions, it's often recommended to use a permissions plugin such as LuckPerms.
# Do not enable this if you are unsure of the risks this option proposes.
allow-admin-permissions-as-default-perms: false

#############
#  Updates  #
#############

# Whether or not the plugin should check for updates.
check-for-updates: true

# Whether or not to notify admins when an update is available.
# Anyone with the permission at.admin.notify will receive this notification.
notify-admins-on-update: true

###################
#  Miscellaneous  #
###################

# Used for debugging purposes.
debug: false

# Whether to use Cumulus forms for Bedrock players.
# These work by having a Bedrock player type in the command itself (such as /warp, /tpa, /setwarp), then fill in the rest of the commands through a form.
# This only works when Geyser and Floodgate are used on the server. This improves accessibility for mobile or console players.
use-floodgate-forms: true

# If you are just using action bars for messages and have empty base messages, the console will not receive them.
# If you have this option set to true, then the console will receive the message that the action bar uses.
send-actionbar-to-console: true

# Keeps any entities riding teleporting players on their heads.
# Only available to newer versions of Paper and uses experimental API - don't expect this to be set to true by default for a while!
# Teleportation is also not async if this has to be used.
retain-passengers: false

# Keeps any entities being ridden by teleporting players.
# Only available to newer versions of Paper and uses experimental API - don't expect this to be set to true by default for a while!
# Teleportation is also not async if this has to be used.
retain-vehicle: false

# If it's not a minecart or boat, take it with us. Requires the above option to be set to true.
retain-living-vehicles-only: true

# Applies to 1.20+ servers - whether the player will have to click the front of the sign (or where teleportation text is) to activate a sign.
# If a player clicks on a sign with different lines on either side, the plugin prioritises the clicked side.
teleport-on-sign-side: false
