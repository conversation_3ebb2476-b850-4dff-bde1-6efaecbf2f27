---- Minecraft Chunk IO Error Report ----
// Ideally, this shouldn't be here

Time: 2025-02-11 20:52:07
Description: Chunk found in invalid location

java.lang.IllegalStateException: Retrieved chunk position [7, -49] does not match requested [7, -33]
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.createMisplacedChunkReport(ChunkIOErrorReporter.java:14)
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.reportMisplacedChunk(ChunkIOErrorReporter.java:23)
	at net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:357)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:94)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: IO-Worker-3
Stacktrace:
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.createMisplacedChunkReport(ChunkIOErrorReporter.java:14)
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.reportMisplacedChunk(ChunkIOErrorReporter.java:23)
	at net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:357)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101)

-- Misplaced Chunk --
Details:
	Stored Position: [7, -49]
Stacktrace:
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.createMisplacedChunkReport(ChunkIOErrorReporter.java:14)
	at net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.reportMisplacedChunk(ChunkIOErrorReporter.java:23)
	at net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:357)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355)
	at ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281)
	at ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:94)
	at ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)

-- Chunk Info --
Details:
	Level: world
	Dimension: minecraft:overworld
	Storage: chunk
	Position: [7, -33]
Stacktrace:
	at net.minecraft.server.MinecraftServer.lambda$storeChunkIoError$41(MinecraftServer.java:2765)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

-- System Details --
Details:
	Minecraft Version: 1.21.4
	Minecraft Version ID: 1.21.4
	Operating System: Windows 10 (amd64) version 10.0
	Java Version: 21.0.6, Eclipse Adoptium
	Java VM Version: OpenJDK 64-Bit Server VM (mixed mode, sharing), Eclipse Adoptium
	Memory: 15133590864 bytes (14432 MiB) / 16106127360 bytes (15360 MiB) up to 16106127360 bytes (15360 MiB)
	CPUs: 20
	Processor Vendor: GenuineIntel
	Processor Name: 12th Gen Intel(R) Core(TM) i7-12700K
	Identifier: Intel64 Family 6 Model 151 Stepping 2
	Microarchitecture: Alder Lake
	Frequency (GHz): 3.61
	Number of physical packages: 1
	Number of physical CPUs: 12
	Number of logical CPUs: 20
	Graphics card #0 name: Intel(R) UHD Graphics 770
	Graphics card #0 vendor: Intel Corporation
	Graphics card #0 VRAM (MiB): 2048.00
	Graphics card #0 deviceId: VideoController1
	Graphics card #0 versionInfo: 32.0.101.6129
	Memory slot #0 capacity (MiB): 16384.00
	Memory slot #0 clockSpeed (GHz): 3.20
	Memory slot #0 type: DDR4
	Memory slot #1 capacity (MiB): 16384.00
	Memory slot #1 clockSpeed (GHz): 3.20
	Memory slot #1 type: DDR4
	Virtual memory max (MiB): 34583.84
	Virtual memory used (MiB): 30282.63
	Swap memory total (MiB): 2048.00
	Swap memory used (MiB): 22.54
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 806157.88, total: 953204.19
	Space in storage for workdir (MiB): available: 806157.88, total: 953204.19
	JVM Flags: 2 total; -Xmx15G -Xms15G
	CraftBukkit Information: 
   BrandInfo: Paper (papermc:paper) version 1.21.4-147-main@3bd69f2 (2025-02-10T22:59:40Z)
   Running: Paper version 1.21.4-147-3bd69f2 (MC: 1.21.4) (Implementing API version 1.21.4-R0.1-SNAPSHOT) false
   Plugins: { EliteMobs v9.3.1 com.magmaguy.elitemobs.EliteMobs [MagmaGuy],}
   Warnings: DEFAULT
   Reload Count: 0
   Threads: { RUNNABLE DestroyJavaVM: [], RUNNABLE Paper Common Worker #4: [java.base@21.0.6/java.lang.Thread.dumpThreads(Native Method), java.base@21.0.6/java.lang.Thread.getAllStackTraces(Thread.java:2522), org.bukkit.craftbukkit.CraftCrashReport.get(CraftCrashReport.java:35), org.bukkit.craftbukkit.CraftCrashReport.get(CraftCrashReport.java:17), net.minecraft.SystemReport.setDetail(SystemReport.java:71), net.minecraft.CrashReport.<init>(CrashReport.java:38), net.minecraft.CrashReport.forThrowable(CrashReport.java:216), net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.createMisplacedChunkReport(ChunkIOErrorReporter.java:14), net.minecraft.world.level.chunk.storage.ChunkIOErrorReporter.reportMisplacedChunk(ChunkIOErrorReporter.java:23), net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:357), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:94), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], RUNNABLE WindowsStreamPump: [org.jline.nativ.Kernel32.WaitForSingleObject(Native Method), org.jline.terminal.impl.jni.win.NativeWinSysTerminal.processConsoleInput(NativeWinSysTerminal.java:210), org.jline.terminal.impl.AbstractWindowsTerminal.pump(AbstractWindowsTerminal.java:469), org.jline.terminal.impl.AbstractWindowsTerminal$$Lambda/0x0000023c1013dd38.run(Unknown Source), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING User Authenticator #0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING mysql-cj-abandoned-connection-cleanup: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), java.base@21.0.6/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:84), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Timer hack thread: [java.base@21.0.6/java.lang.Thread.sleep0(Native Method), java.base@21.0.6/java.lang.Thread.sleep(Thread.java:509), net.minecraft.Util$8.run(Util.java:905)], TIMED_WAITING Craft Scheduler Thread - 6: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Paper Common Worker #0: [java.base@21.0.6/java.io.DataInputStream.readFully(DataInputStream.java:208), java.base@21.0.6/java.io.DataInputStream.readUTF(DataInputStream.java:594), java.base@21.0.6/java.io.DataInputStream.readUTF(DataInputStream.java:550), net.minecraft.nbt.StringTag$1.readAccounted(StringTag.java:23), net.minecraft.nbt.StringTag$1.load(StringTag.java:13), net.minecraft.nbt.StringTag$1.load(StringTag.java:10), net.minecraft.nbt.CompoundTag.readNamedTagData(CompoundTag.java:532), net.minecraft.nbt.CompoundTag$1.loadCompound(CompoundTag.java:57), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:42), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:35), net.minecraft.nbt.CompoundTag.readNamedTagData(CompoundTag.java:532), net.minecraft.nbt.CompoundTag$1.loadCompound(CompoundTag.java:57), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:42), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:35), net.minecraft.nbt.ListTag$1.loadList(ListTag.java:41), net.minecraft.nbt.ListTag$1.load(ListTag.java:21), net.minecraft.nbt.ListTag$1.load(ListTag.java:14), net.minecraft.nbt.CompoundTag.readNamedTagData(CompoundTag.java:532), net.minecraft.nbt.CompoundTag$1.loadCompound(CompoundTag.java:57), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:42), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:35), net.minecraft.nbt.CompoundTag.readNamedTagData(CompoundTag.java:532), net.minecraft.nbt.CompoundTag$1.loadCompound(CompoundTag.java:57), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:42), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:35), net.minecraft.nbt.ListTag$1.loadList(ListTag.java:41), net.minecraft.nbt.ListTag$1.load(ListTag.java:21), net.minecraft.nbt.ListTag$1.load(ListTag.java:14), net.minecraft.nbt.CompoundTag.readNamedTagData(CompoundTag.java:532), net.minecraft.nbt.CompoundTag$1.loadCompound(CompoundTag.java:57), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:42), net.minecraft.nbt.CompoundTag$1.load(CompoundTag.java:35), net.minecraft.nbt.NbtIo.readTagSafe(NbtIo.java:196), net.minecraft.nbt.NbtIo.readUnnamedTag(NbtIo.java:190), net.minecraft.nbt.NbtIo.read(NbtIo.java:126), net.minecraft.nbt.NbtIo.read(NbtIo.java:117), net.minecraft.world.level.chunk.storage.RegionFileStorage.moonrise$finishRead(RegionFileStorage.java:233), ca.spottedleaf.moonrise.patches.chunk_system.io.datacontroller.ChunkDataController.finishRead(ChunkDataController.java:48), ca.spottedleaf.moonrise.patches.chunk_system.io.MoonriseRegionFileIO$ChunkIOTask.performReadDecompress(MoonriseRegionFileIO.java:1140), ca.spottedleaf.moonrise.patches.chunk_system.io.MoonriseRegionFileIO$ChunkIOTask$$Lambda/0x0000023c11056578.run(Unknown Source), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:72), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], TIMED_WAITING Paper Common Worker #1: [java.base@21.0.6/java.lang.AbstractStringBuilder.ensureCapacityInternal(AbstractStringBuilder.java:243), java.base@21.0.6/java.lang.AbstractStringBuilder.append(AbstractStringBuilder.java:587), java.base@21.0.6/java.lang.StringBuilder.append(StringBuilder.java:179), java.base@21.0.6/java.lang.Object.toString(Object.java:257), java.base@21.0.6/java.lang.StringConcatHelper.stringOf(StringConcatHelper.java:467), java.base@21.0.6/java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(DirectMethodHandle$Holder), java.base@21.0.6/java.lang.invoke.LambdaForm$MH/0x0000023c101b9800.invoke(LambdaForm$MH), java.base@21.0.6/java.lang.invoke.Invokers$Holder.linkToTargetMethod(Invokers$Holder), com.mojang.serialization.Decoder$5.toString(Decoder.java:131), java.base@21.0.6/java.lang.StringConcatHelper.stringOf(StringConcatHelper.java:467), java.base@21.0.6/java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(DirectMethodHandle$Holder), java.base@21.0.6/java.lang.invoke.LambdaForm$MH/0x0000023c101c0c00.invoke(LambdaForm$MH), java.base@21.0.6/java.lang.invoke.Invokers$Holder.linkToTargetMethod(Invokers$Holder), com.mojang.serialization.codecs.RecordCodecBuilder$Instance$3.toString(RecordCodecBuilder.java:264), java.base@21.0.6/java.lang.StringConcatHelper.stringOf(StringConcatHelper.java:467), java.base@21.0.6/java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(DirectMethodHandle$Holder), java.base@21.0.6/java.lang.invoke.LambdaForm$MH/0x0000023c101b9800.invoke(LambdaForm$MH), java.base@21.0.6/java.lang.invoke.Invokers$Holder.linkToTargetMethod(Invokers$Holder), com.mojang.serialization.codecs.RecordCodecBuilder$2.toString(RecordCodecBuilder.java:122), com.mojang.serialization.MapCodec$MapCodecCodec.toString(MapCodec.java:168), com.mojang.serialization.Codec.comapFlatMap(Codec.java:243), net.minecraft.world.level.chunk.PalettedContainer.codec(PalettedContainer.java:73), net.minecraft.world.level.chunk.PalettedContainer.codecRW(PalettedContainer.java:54), net.minecraft.world.level.chunk.storage.SerializableChunkData.makeBiomeCodecRW(SerializableChunkData.java:496), net.minecraft.world.level.chunk.storage.SerializableChunkData.parse(SerializableChunkData.java:225), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:346), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:72), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], WAITING spark-java-sampler-0-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING IO-Worker-5: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING IO-Worker-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING spark-java-sampler-0-3: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Finalizer: [java.base@21.0.6/java.lang.Object.wait0(Native Method), java.base@21.0.6/java.lang.Object.wait(Object.java:366), java.base@21.0.6/java.lang.Object.wait(Object.java:339), java.base@21.0.6/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158), java.base@21.0.6/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89), java.base@21.0.6/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)], TIMED_WAITING pool-8-thread-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING IO-Worker-2: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Reference Handler: [java.base@21.0.6/java.lang.ref.Reference.waitForReferencePendingList(Native Method), java.base@21.0.6/java.lang.ref.Reference.processPendingReferences(Reference.java:246), java.base@21.0.6/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)], TIMED_WAITING Worker-Main-4: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], RUNNABLE Notification Thread: [], TIMED_WAITING Paper Watchdog Thread: [java.base@21.0.6/java.lang.Thread.sleep0(Native Method), java.base@21.0.6/java.lang.Thread.sleep(Thread.java:509), org.spigotmc.WatchdogThread.run(WatchdogThread.java:155)], TIMED_WAITING Yggdrasil Key Fetcher: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING spark-java-sampler-0-5: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Async Task Handler Thread - 1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING IO-Worker-3: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Craft Async Scheduler Management Thread: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Attach Listener: [], RUNNABLE Server thread: [net.minecraft.world.level.LevelReader.getBiome(LevelReader.java:53), net.minecraft.world.level.NaturalSpawner.mobsAt(NaturalSpawner.java:434), net.minecraft.world.level.NaturalSpawner.canSpawnMobAt(NaturalSpawner.java:426), net.minecraft.world.level.NaturalSpawner.isValidSpawnPostitionForType(NaturalSpawner.java:381), net.minecraft.world.level.NaturalSpawner.spawnCategoryForPosition(NaturalSpawner.java:287), net.minecraft.world.level.NaturalSpawner.spawnCategoryForChunk(NaturalSpawner.java:224), net.minecraft.world.level.NaturalSpawner.spawnForChunk(NaturalSpawner.java:193), net.minecraft.server.level.ServerChunkCache.tickChunks(ServerChunkCache.java:620), net.minecraft.server.level.ServerChunkCache.tickChunks(ServerChunkCache.java:522), net.minecraft.server.level.ServerChunkCache.tick(ServerChunkCache.java:494), net.minecraft.server.level.ServerLevel.tick(ServerLevel.java:776), net.minecraft.server.MinecraftServer.tickChildren(MinecraftServer.java:1724), net.minecraft.server.MinecraftServer.tickServer(MinecraftServer.java:1529), net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1251), net.minecraft.server.MinecraftServer.lambda$spin$2(MinecraftServer.java:310), net.minecraft.server.MinecraftServer$$Lambda/0x0000023c10de2318.run(Unknown Source), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Server console handler: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), org.jline.utils.NonBlockingPumpReader.read(NonBlockingPumpReader.java:78), org.jline.utils.NonBlockingReader.read(NonBlockingReader.java:56), org.jline.keymap.BindingReader.readCharacter(BindingReader.java:160), org.jline.keymap.BindingReader.readBinding(BindingReader.java:110), org.jline.keymap.BindingReader.readBinding(BindingReader.java:61), org.jline.reader.impl.LineReaderImpl.doReadBinding(LineReaderImpl.java:923), org.jline.reader.impl.LineReaderImpl.readBinding(LineReaderImpl.java:956), org.jline.reader.impl.LineReaderImpl.readLine(LineReaderImpl.java:651), org.jline.reader.impl.LineReaderImpl.readLine(LineReaderImpl.java:468), net.minecrell.terminalconsole.SimpleTerminalConsole.readCommands(SimpleTerminalConsole.java:158), net.minecrell.terminalconsole.SimpleTerminalConsole.start(SimpleTerminalConsole.java:141), net.minecraft.server.dedicated.DedicatedServer$1.run(DedicatedServer.java:109)], RUNNABLE IO-Worker-4: [java.base@21.0.6/java.lang.StringBuilder.<init>(StringBuilder.java:106), java.base@21.0.6/sun.nio.fs.WindowsPath.resolve(WindowsPath.java:606), java.base@21.0.6/sun.nio.fs.WindowsPath.resolve(WindowsPath.java:42), java.base@21.0.6/java.nio.file.Path.resolve(Path.java:516), net.minecraft.server.MinecraftServer.getFile(MinecraftServer.java:1801), net.minecraft.server.MinecraftServer.lambda$storeChunkIoError$41(MinecraftServer.java:2754), net.minecraft.server.MinecraftServer$$Lambda/0x0000023c11949790.run(Unknown Source), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-5: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING spark-java-sampler-0-4: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 4: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING spark-java-sampler-0-2: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 5: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-2: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Worker-Main-3: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING JNA Cleaner: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), java.base@21.0.6/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), com.sun.jna.internal.Cleaner$CleanerThread.run(Cleaner.java:154)], TIMED_WAITING ForkJoinPool.commonPool-worker-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Worker-Main-6: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING spark-monitoring-thread: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Paper Common Worker #3: [java.base@21.0.6/java.lang.String.hashCode(String.java:2369), net.minecraft.resources.ResourceLocation.hashCode(ResourceLocation.java:161), java.base@21.0.6/java.util.HashMap.hash(HashMap.java:338), java.base@21.0.6/java.util.HashMap.getNode(HashMap.java:576), java.base@21.0.6/java.util.HashMap.get(HashMap.java:564), net.minecraft.core.MappedRegistry.getValue(MappedRegistry.java:226), net.minecraft.world.level.chunk.storage.SerializableChunkData.unpackStructureReferences(SerializableChunkData.java:827), net.minecraft.world.level.chunk.storage.SerializableChunkData.read(SerializableChunkData.java:447), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkLoadTask$ChunkDataLoadTask.runOffMain(ChunkLoadTask.java:355), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.GenericDataLoadTask$ProcessOffMainTask.run(GenericDataLoadTask.java:311), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:72), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], TIMED_WAITING Common-Cleaner: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), java.base@21.0.6/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.6/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), java.base@21.0.6/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583), java.base@21.0.6/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)], TIMED_WAITING Paper I/O Worker #0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:70), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], RUNNABLE Netty Server IO #0: [java.base@21.0.6/sun.nio.ch.WEPoll.wait(Native Method), java.base@21.0.6/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114), java.base@21.0.6/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.6/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147), io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68), io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879), io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Netty Server IO #2: [java.base@21.0.6/sun.nio.ch.WEPoll.wait(Native Method), java.base@21.0.6/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114), java.base@21.0.6/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.6/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142), io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:62), io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:883), io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.6/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.6/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.6/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.6/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING bStats-Metrics: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.6/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.6/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], RUNNABLE Signal Dispatcher: [], RUNNABLE Paper Common Worker #2: [com.mojang.datafixers.util.Either$Right.map(Either.java:99), net.minecraft.world.level.biome.MultiNoiseBiomeSource.parameters(MultiNoiseBiomeSource.java:41), net.minecraft.world.level.biome.MultiNoiseBiomeSource.getNoiseBiome(MultiNoiseBiomeSource.java:66), net.minecraft.world.level.biome.MultiNoiseBiomeSource.getNoiseBiome(MultiNoiseBiomeSource.java:61), net.minecraft.world.level.chunk.LevelChunkSection.fillBiomesFromNoise(LevelChunkSection.java:318), net.minecraft.world.level.chunk.ChunkAccess.fillBiomesFromNoise(ChunkAccess.java:549), net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator.doCreateBiomes(NoiseBasedChunkGenerator.java:86), net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator.lambda$createBiomes$5(NoiseBasedChunkGenerator.java:78), net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator$$Lambda/0x0000023c11960c98.get(Unknown Source), java.base@21.0.6/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768), net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator$$Lambda/0x0000023c11960eb0.execute(Unknown Source), java.base@21.0.6/java.util.concurrent.CompletableFuture.asyncSupplyStage(CompletableFuture.java:1782), java.base@21.0.6/java.util.concurrent.CompletableFuture.supplyAsync(CompletableFuture.java:2005), net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator.createBiomes(NoiseBasedChunkGenerator.java:77), net.minecraft.world.level.chunk.status.ChunkStatusTasks.generateBiomes(ChunkStatusTasks.java:76), net.minecraft.world.level.chunk.status.ChunkPyramid$$Lambda/0x0000023c109f2988.doWork(Unknown Source), net.minecraft.world.level.chunk.status.ChunkStep.apply(ChunkStep.java:66), ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkUpgradeGenericStatusTask.run(ChunkUpgradeGenericStatusTask.java:99), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue$PrioritisedQueuedTask.execute(PrioritisedTaskQueue.java:281), ca.spottedleaf.concurrentutil.executor.queue.PrioritisedTaskQueue.executeTask(PrioritisedTaskQueue.java:101), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$ExecutorGroup$ThreadPoolExecutor.executeTask(PrioritisedThreadPool.java:533), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedThreadPool$PrioritisedThread.pollTasks(PrioritisedThreadPool.java:354), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:72), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], RUNNABLE Log4j2-AsyncAppenderEventDispatcher-1-Async: [java.base@21.0.6/java.util.Calendar$Builder.build(Calendar.java:1496), java.base@21.0.6/sun.util.locale.provider.CalendarProviderImpl.getInstance(CalendarProviderImpl.java:87), java.base@21.0.6/java.util.Calendar.createCalendar(Calendar.java:1700), java.base@21.0.6/java.util.Calendar.getInstance(Calendar.java:1679), org.apache.logging.log4j.core.util.datetime.FastDatePrinter.newCalendar(FastDatePrinter.java:464), org.apache.logging.log4j.core.util.datetime.FastDatePrinter.format(FastDatePrinter.java:490), org.apache.logging.log4j.core.util.datetime.FastDateFormat.format(FastDateFormat.java:457), org.apache.logging.log4j.core.pattern.DatePatternConverter$PatternFormatter.formatToBuffer(DatePatternConverter.java:80), org.apache.logging.log4j.core.pattern.DatePatternConverter.formatWithoutAllocation(DatePatternConverter.java:314), org.apache.logging.log4j.core.pattern.DatePatternConverter.format(DatePatternConverter.java:307), org.apache.logging.log4j.core.pattern.DatePatternConverter.format(DatePatternConverter.java:284), org.apache.logging.log4j.core.pattern.PatternFormatter.format(PatternFormatter.java:44), org.apache.logging.log4j.core.layout.PatternLayout$PatternSelectorSerializer.toSerializable(PatternLayout.java:567), org.apache.logging.log4j.core.layout.PatternLayout.toText(PatternLayout.java:252), org.apache.logging.log4j.core.layout.PatternLayout.encode(PatternLayout.java:238), org.apache.logging.log4j.core.layout.PatternLayout.encode(PatternLayout.java:58), org.apache.logging.log4j.core.appender.AbstractOutputStreamAppender.directEncodeEvent(AbstractOutputStreamAppender.java:227), org.apache.logging.log4j.core.appender.AbstractOutputStreamAppender.tryAppend(AbstractOutputStreamAppender.java:220), org.apache.logging.log4j.core.appender.AbstractOutputStreamAppender.append(AbstractOutputStreamAppender.java:211), org.apache.logging.log4j.core.appender.RollingRandomAccessFileAppender.append(RollingRandomAccessFileAppender.java:275), org.apache.logging.log4j.core.config.AppenderControl.tryCallAppender(AppenderControl.java:160), org.apache.logging.log4j.core.config.AppenderControl.callAppender0(AppenderControl.java:133), org.apache.logging.log4j.core.config.AppenderControl.callAppenderPreventRecursion(AppenderControl.java:124), org.apache.logging.log4j.core.config.AppenderControl.callAppender(AppenderControl.java:88), org.apache.logging.log4j.core.appender.rewrite.RewriteAppender.append(RewriteAppender.java:89), org.apache.logging.log4j.core.config.AppenderControl.tryCallAppender(AppenderControl.java:160), org.apache.logging.log4j.core.config.AppenderControl.callAppender0(AppenderControl.java:133), org.apache.logging.log4j.core.config.AppenderControl.callAppenderPreventRecursion(AppenderControl.java:124), org.apache.logging.log4j.core.config.AppenderControl.callAppender(AppenderControl.java:88), org.apache.logging.log4j.core.appender.rewrite.RewriteAppender.append(RewriteAppender.java:89), org.apache.logging.log4j.core.config.AppenderControl.tryCallAppender(AppenderControl.java:160), org.apache.logging.log4j.core.config.AppenderControl.callAppender0(AppenderControl.java:133), org.apache.logging.log4j.core.config.AppenderControl.callAppenderPreventRecursion(AppenderControl.java:124), org.apache.logging.log4j.core.config.AppenderControl.callAppender(AppenderControl.java:88), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.dispatch(AsyncAppenderEventDispatcher.java:127), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.dispatchAll(AsyncAppenderEventDispatcher.java:91), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.run(AsyncAppenderEventDispatcher.java:73)], WAITING Paper Async Command Builder Thread Pool - 1: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Async Task Handler Thread - 0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Netty Server IO #1: [java.base@21.0.6/sun.nio.ch.WEPoll.wait(Native Method), java.base@21.0.6/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114), java.base@21.0.6/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.6/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:147), io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68), io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879), io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Async Command Builder Thread Pool - 0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.6/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.6/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.6/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING spark-java-sampler-0-0: [java.base@21.0.6/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.6/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.6/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.6/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.6/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.6/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.6/java.lang.Thread.run(Thread.java:1583)],}
   
   Force Loaded Chunks: { world: {}, world_nether: {}, world_the_end: {},}