#----------------------------------------------------------------------------------------------------------------#
#							  MyCommand Plugin config file (last update v5.7.2)								     #
#																											     #
#  					BukkitDev Help page : http://dev.bukkit.org/server-mods/mycommand/  					     #
#																											     #
# Manage the Listeners to disable completely plugin parts. Use "true" for active and "false" for deactive them   #
# DELAY_SEC in Signs and Block are used to prevent command flood. Put it on 0, to disable that.		             #
# The Economy features require Vault installed. Download here : https://www.spigotmc.org/resources/vault.34315/  #
#----------------------------------------------------------------------------------------------------------------#
LISTENERS:
  # Active this if you want use the /mycmd-blockset features. (Run commands through blocks)
  BLOCK_LISTENER: true
  # This allow you to execute commands from the ChatListener, Example, without the slash / prefix.
  CHAT_LISTENER: false
  # Turn this one on, to allow the execution of (unregistered) custom commands in-game. by Default MyCommand custom commands are unregistered. Use register: true under a command to register it.
  CUSTOM_COMMANDS_GAME: true
  # Use the ConsoleListener to be able to run (unregistered) custom commands from the console.
  CUSTOM_COMMANDS_CONSOLE: true
  # Extra Listener, allows you to execute commands on some extra events, like death,join and someone else. Check playerevents.yml
  EXTRA_LISTENER: false
  # Enable this if you want use the /mycmd-itemset feature.
  ITEM_LISTENER: true
  # Active the move listener to cancel WarmUp's when a player move.
  MOVE_LISTENER: false
  # Active the NPC one, to be able to use the /mycmd-npcs features and run commands through (Villager) npc like entity.
  NPCS_LISTENER: false
  # Turn the plugin message on, if you want to interface with "MyCommand for BungeeCord" and executing proxy (BungeeCord) commands from local server (Spigot). Also this allow's you to use some proxy-side placeholders.
  PLUGIN_MESSAGE_LISTENER: false
  # The Sign listener allow the use of [MyCmd] signs.
  SIGN_LISTENER: true
  # The Vehicle listener it's used only for the DETECTOR_RAIL Material in /mycmd-blockset feature, and allows you to execute commands when a player trigger a detector_rail.
  VEHICLE_LISTENER: false
  # Enable this if you want to use the dynamic ICON_MENU's feature. Without it you can't check when a player closes the menu with ESC key. Also turn this on if you want run commands from the MERCHANT_GUI
  INVENTORY_LISTENER: true
  # Enable the Hologram listener if you want to launch commands through holograms made with /mycmd hologram
  HOLOGRAM_LISTENER: false
  # Enable the ItemFrame listener if you want to launch commands through itemframes /mycmd-blockset itemframe 
  ITEMFRAME_LISTENER: false
  # Enable the tab-suggestion blocker listener, to enable the AntiTAB feature. Use tabsuggestionblocker.yml to customize it.
  TAB_SUGGESTION_BLOCKER_LISTENER: false  
# MAX_ARGUMENTS = Determinate how many $args can be typed in a command. (lower = faster) | AUTO_REGISTER register any non defined command by default.
COMMANDS:
  MAX_ARGUMENTS: 9
  AUTO_REGISTER: false
  DEFAULT_DESCRIPTION: "Registered MyCommand Command"
#Run Commands, if SENT_BY_CHAT is set to true, the commands will be performed as a chat message. If false only real commands (registered) can be performed. This can help sending color codes trought the chat
RUN_COMMANDS:
  SENT_BY_CHAT: true
HOOKS:
  PLACEHOLDER_API: true
  PROTOCOLLIB: true
#Misc options.
DEBUG:
  GAME: false
  CONSOLE: true
  DISABLE_PERMISSIONS: false
  USE_UUID_FOR_PLAYERDATA: true
  USE_NAME_INSTEAD_OF_ID_IN_THE_PERMISSION_NAME: true
  USE_THE_UPDATER: true
  SAVE_PENDING_COOLDOWNS: true
  USE_AT_SELECTORS_PLACEHOLDERS: false
  ROUND_DOUBLE_DECIMALS_AT: 2
  REPLACE_ARGS_WITH_NOTHING_IF_EMPTY: true
#Manage what characters an user can type changing the global regex pattern. By setting USE to false, you're still able to use it per command by using "regex_pattern" command field.
REGEX:
  USE: true
  PATTERN: "^[-a-zA-Z0-9&._ ]+"
  RAW_TEXT_SPLITTER: ";"
ECONOMY:
  ALLOW_DEBT: false
  #Hook MyCommand into Vault to make it working as a standalone economy plugin. Balances will get saved as a playerdata value.
  USE_MYCOMMAND_AS_ECONOMY_PLUGIN: false
  PLAYERDATA_VALUE: money
  SUFFIX: "$"
SCHEDULER:
  ENABLED: false
  DATE_FORMAT: "d M yyyy"
  FIRST_CHECK_AFTER_SEC: 60
  LAUNCH_CHECK_ON_EVERY_N_MIN: 60
TEXT_OPTIONS:
  BROADCAST_SHOW_NO_PLAYER_FOUND: true
SIGNS:
  HEADER: "[MYCMD]"
  DELAY_SEC: 5
NPCS:
  DELAY_SEC: 3
INTERACTION:
  DELAY_SEC: 3
HOLOGRAMS:
  DELAY_SEC: 5
BLOCKS:
  DELAY_SEC: 5
  MATERIAL:
    LEFT_CLICK_INTERACTION:
    - STONE_BUTTON
    RIGHT_CLICK_INTERACTION:
    - LEVER
    - STONE_BUTTON
    - OAK_BUTTON
    - OAK_DOOR
    PHYSICAL_INTERACTION:
    - STONE_PRESSURE_PLATE
    - OAK_PRESSURE_PLATE
    - DETECTOR_RAIL
# MYSQL support for PlayerData's values storage only.
MYSQL:
  USE: true
  HOST: 'mysql'
  PORT: '3306'
  USERNAME: 'hamza'
  PASSWORD: 'Hh@#2021'
  DATABASE: 'minecraft-database'
  AUTO_RECONNECT: true
  #If true use "com.mysql.cj.jdbc.Driver" instead of "com.mysql.jdbc.Driver"
  USE_NEWER_DRIVER: true