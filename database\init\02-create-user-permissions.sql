-- Use the minecraft database
USE `minecraft-database`;

-- Grant permissions to the hamza user
GRANT ALL PRIVILEGES ON `minecraft-database`.* TO 'hamza'@'%';

-- Create additional users if needed for specific plugins
-- Create a read-only user for monitoring/backup purposes
CREATE USER IF NOT EXISTS 'minecraft_readonly'@'%' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON `minecraft-database`.* TO 'minecraft_readonly'@'%';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Insert default data for LuckPerms groups
INSERT IGNORE INTO `luckperms_groups` (`name`) VALUES 
('default'),
('admin'),
('moderator'),
('vip'),
('builder');

-- Insert default permissions for default group
INSERT IGNORE INTO `luckperms_group_permissions` (`name`, `permission`, `value`, `server`, `world`, `expiry`, `contexts`) VALUES
('default', 'minecraft.command.help', 1, 'global', 'global', 0, '{}'),
('default', 'minecraft.command.me', 1, 'global', 'global', 0, '{}'),
('default', 'minecraft.command.tell', 1, 'global', 'global', 0, '{}'),
('default', 'essentials.spawn', 1, 'global', 'global', 0, '{}'),
('default', 'essentials.home', 1, 'global', 'global', 0, '{}'),
('default', 'essentials.sethome', 1, 'global', 'global', 0, '{}'),
('default', 'essentials.tpa', 1, 'global', 'global', 0, '{}'),
('default', 'essentials.tpaccept', 1, 'global', 'global', 0, '{}'),
('default', 'essentials.tpdeny', 1, 'global', 'global', 0, '{}');

-- Insert admin permissions
INSERT IGNORE INTO `luckperms_group_permissions` (`name`, `permission`, `value`, `server`, `world`, `expiry`, `contexts`) VALUES
('admin', '*', 1, 'global', 'global', 0, '{}');

-- Insert moderator permissions
INSERT IGNORE INTO `luckperms_group_permissions` (`name`, `permission`, `value`, `server`, `world`, `expiry`, `contexts`) VALUES
('moderator', 'essentials.kick', 1, 'global', 'global', 0, '{}'),
('moderator', 'essentials.ban', 1, 'global', 'global', 0, '{}'),
('moderator', 'essentials.unban', 1, 'global', 'global', 0, '{}'),
('moderator', 'essentials.mute', 1, 'global', 'global', 0, '{}'),
('moderator', 'essentials.unmute', 1, 'global', 'global', 0, '{}'),
('moderator', 'essentials.tp', 1, 'global', 'global', 0, '{}'),
('moderator', 'essentials.teleport', 1, 'global', 'global', 0, '{}');

-- Insert VIP permissions
INSERT IGNORE INTO `luckperms_group_permissions` (`name`, `permission`, `value`, `server`, `world`, `expiry`, `contexts`) VALUES
('vip', 'essentials.fly', 1, 'global', 'global', 0, '{}'),
('vip', 'essentials.speed', 1, 'global', 'global', 0, '{}'),
('vip', 'essentials.heal', 1, 'global', 'global', 0, '{}'),
('vip', 'essentials.feed', 1, 'global', 'global', 0, '{}'),
('vip', 'essentials.home.multiple', 1, 'global', 'global', 0, '{}');

-- Insert builder permissions
INSERT IGNORE INTO `luckperms_group_permissions` (`name`, `permission`, `value`, `server`, `world`, `expiry`, `contexts`) VALUES
('builder', 'worldedit.*', 1, 'global', 'global', 0, '{}'),
('builder', 'essentials.gamemode', 1, 'global', 'global', 0, '{}'),
('builder', 'essentials.time', 1, 'global', 'global', 0, '{}'),
('builder', 'essentials.weather', 1, 'global', 'global', 0, '{}');

-- Create some default warps for AdvancedTeleport
INSERT IGNORE INTO `advancedtp_warps` (`name`, `world`, `x`, `y`, `z`, `yaw`, `pitch`, `created_by`) VALUES
('spawn', 'HUB', 0, 64, 0, 0, 0, NULL),
('hub', 'HUB', 0, 64, 0, 0, 0, NULL);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_ls_players_login_attempts` ON `ls_players` (`login_attempts`);
CREATE INDEX IF NOT EXISTS `idx_player_data_session` ON `player_data` (`session_timeout`);
CREATE INDEX IF NOT EXISTS `idx_luckperms_permissions_server` ON `luckperms_user_permissions` (`server`);
CREATE INDEX IF NOT EXISTS `idx_luckperms_permissions_world` ON `luckperms_user_permissions` (`world`);
CREATE INDEX IF NOT EXISTS `idx_mycommand_data_key` ON `mycommand_playerdata` (`data_key`);
CREATE INDEX IF NOT EXISTS `idx_advancedtp_homes_world` ON `advancedtp_homes` (`world`);
CREATE INDEX IF NOT EXISTS `idx_advancedtp_warps_world` ON `advancedtp_warps` (`world`);
CREATE INDEX IF NOT EXISTS `idx_elitemobs_currency` ON `elitemobs_players` (`currency`);
CREATE INDEX IF NOT EXISTS `idx_elitemobs_guild_rank` ON `elitemobs_players` (`guild_rank`);
