[12:27:19] [ServerMain/INFO]: [bootstrap] Running Java 24 (Java HotSpot(TM) 64-Bit Server VM 24.0.1+9-30; Oracle Corporation null) on Windows 11 10.0 (amd64)
[12:27:19] [ServerMain/INFO]: [bootstrap] Loading Paper 1.21.4-147-main@3bd69f2 (2025-02-10T22:59:40Z) for Minecraft 1.21.4
[12:27:19] [ServerMain/INFO]: [PluginInitializerManager] Initializing plugins...
[12:27:21] [Paper Plugin Remapper Thread - 1/INFO]: [PluginRemapper] Remapping plugin 'plugins\SkinsRestorer.jar'...
[12:27:23] [Paper Plugin Remapper Thread - 1/INFO]: [PluginRemapper] Done remapping plugin 'plugins\SkinsRestorer.jar' in 1703ms.
[12:27:24] [ServerMain/INFO]: [PluginInitializerManager] Initialized 31 plugins
[12:27:24] [ServerMain/INFO]: [PluginInitializerManager] Paper plugins (2):
 - FancyHolograms (2.4.2), FancyNpcs (2.4.4)
[12:27:24] [ServerMain/INFO]: [PluginInitializerManager] Bukkit plugins (29):
 - AdvancedTeleport (6.1.2), AntiPopup (10.1), EliteMobs (9.3.1), Essentials (2.21.1-dev+5-dabe687), EssentialsSpawn (2.21.1-dev+5-dabe687), GSit (2.2.1), Gender (1.1), IridiumSkyblock (4.1.0-B5), LobbyHeadItem (1.9.21), LoginSecurity (3.3.0), LuckPerms (5.4.156), Multiverse-Core (4.3.14), Multiverse-Inventories (4.2.6), Multiverse-NetherPortals (4.2.3), Multiverse-Portals (4.2.3), MyCommand (5.7.4), PerWorldPlugins (1.5.9), PlaceholderAPI (2.11.6), PlayerParticles (8.8), Scale (1.0.1), SkinsRestorer (15.6.5), TAB (5.0.7), TerraformGenerator (18.1.2), Vault (1.7.3-b131), ViaBackwards (5.2.1), ViaRewind (4.0.5), ViaVersion (5.2.1), WorldEdit (7.3.10+7004-768a436), WorldServer (1.9.4)
[12:27:28] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[12:27:28] [ServerMain/INFO]: [MCTypeRegistry] Initialising converters for DataConverter...
[12:27:29] [ServerMain/INFO]: [MCTypeRegistry] Finished initialising converters for DataConverter in ٢٣٣٫٩ms
[12:27:30] [ServerMain/INFO]: Loaded 1370 recipes
[12:27:30] [ServerMain/INFO]: Loaded 1481 advancements
[12:27:30] [Server thread/INFO]: Starting minecraft server version 1.21.4
[12:27:30] [Server thread/INFO]: Loading properties
[12:27:30] [Server thread/INFO]: This server is running Paper version 1.21.4-147-main@3bd69f2 (2025-02-10T22:59:40Z) (Implementing API version 1.21.4-R0.1-SNAPSHOT)
[12:27:30] [Server thread/INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling
[12:27:30] [Server thread/INFO]: Server Ping Player Sample Count: ١٢
[12:27:30] [Server thread/INFO]: Using ٤ threads for Netty based IO
[12:27:30] [Server thread/INFO]: [MoonriseCommon] Paper is using 5 worker threads, 1 I/O threads
[12:27:30] [Server thread/INFO]: [ChunkTaskScheduler] Chunk system is using population gen parallelism: true
[12:27:31] [Server thread/INFO]: Default game type: SURVIVAL
[12:27:31] [Server thread/INFO]: Generating keypair
[12:27:31] [Server thread/INFO]: Starting Minecraft server on *:25565
[12:27:31] [Server thread/INFO]: Using default channel type
[12:27:31] [Server thread/INFO]: Paper: Using Java compression from Velocity.
[12:27:31] [Server thread/INFO]: Paper: Using Java cipher from Velocity.
[12:27:32] [Server thread/WARN]: [org.bukkit.craftbukkit.legacy.CraftLegacy] Initializing Legacy Material Support. Unless you have legacy plugins and/or data this is a bug!
[12:27:38] [Server thread/WARN]: Legacy plugin Gender v1.1 does not specify an api-version.
[12:27:38] [Server thread/INFO]: [ViaVersion] Loading server plugin ViaVersion v5.2.1
[12:27:38] [Server thread/INFO]: [ViaVersion] ViaVersion 5.2.1 is now loaded. Registering protocol transformers and injecting...
[12:27:38] [Via-Mappingloader-0/INFO]: [ViaVersion] Loading block connection mappings ...
[12:27:38] [Via-Mappingloader-0/INFO]: [ViaVersion] Using FastUtil Long2ObjectOpenHashMap for block connections
[12:27:39] [Server thread/INFO]: [ViaBackwards] Loading translations...
[12:27:39] [Server thread/INFO]: [ViaBackwards] Registering protocols...
[12:27:39] [Server thread/INFO]: [ViaRewind] Registering protocols...
[12:27:39] [Server thread/INFO]: [LuckPerms] Loading server plugin LuckPerms v5.4.156
[12:27:40] [Server thread/INFO]: [Vault] Loading server plugin Vault v1.7.3-b131
[12:27:40] [Server thread/INFO]: [PlaceholderAPI] Loading server plugin PlaceholderAPI v2.11.6
[12:27:40] [Server thread/INFO]: [WorldEdit] Loading server plugin WorldEdit v7.3.10+7004-768a436
[12:27:40] [Server thread/INFO]: Got request to register class com.sk89q.worldedit.bukkit.BukkitServerInterface with WorldEdit [com.sk89q.worldedit.extension.platform.PlatformManager@7cfdb9d3]
[12:27:40] [Server thread/INFO]: [PlayerParticles] Loading server plugin PlayerParticles v8.8
[12:27:40] [Server thread/INFO]: [AdvancedTeleport] Loading server plugin AdvancedTeleport v6.1.2
[12:27:40] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for gson
[12:27:40] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for gson
[12:27:40] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for jar-relocator
[12:27:40] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for jar-relocator
[12:27:40] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for asm
[12:27:40] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for asm
[12:27:40] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for asm-commons
[12:27:41] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for asm-commons
[12:27:41] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for gson
[12:27:41] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for gson
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-api
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-api
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-key
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-key
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for examination-api
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for examination-api
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for examination-string
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for examination-string
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-text-minimessage
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-text-minimessage
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-nbt
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-nbt
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-platform-bukkit
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-platform-bukkit
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-text-serializer-gson-legacy-impl
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-text-serializer-gson-legacy-impl
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-text-serializer-gson
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-text-serializer-gson
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-text-serializer-json
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-text-serializer-json
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for option
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for option
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-platform-facet
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-platform-facet
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-platform-api
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-platform-api
[12:27:42] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-platform-viaversion
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-platform-viaversion
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-text-serializer-bungeecord
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-text-serializer-bungeecord
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for adventure-text-serializer-legacy
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for adventure-text-serializer-legacy
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for bstats-bukkit
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for bstats-bukkit
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for bstats-base
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for bstats-base
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for paperlib
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for paperlib
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Verifying checksum for ConfigurationMaster-API
[12:27:43] [Server thread/INFO]: [AdvancedTeleport] Checksum matched for ConfigurationMaster-API
[12:27:43] [Server thread/INFO]: [Essentials] Loading server plugin Essentials v2.21.1-dev+5-dabe687
[12:27:43] [Server thread/INFO]: [EssentialsSpawn] Loading server plugin EssentialsSpawn v2.21.1-dev+5-dabe687
[12:27:43] [Server thread/INFO]: [IridiumSkyblock] Loading server plugin IridiumSkyblock v4.1.0-B5
[12:27:46] [Server thread/INFO]: [IridiumSkyblock] GENERATOR TYPE: VOID
[12:27:46] [Server thread/INFO]: [IridiumSkyblock] Loading world generator...
[12:27:46] [Server thread/INFO]: [IridiumSkyblock] Generator Type = VOID
[12:27:46] [Server thread/INFO]: [ViaBackwards] Loading server plugin ViaBackwards v5.2.1
[12:27:46] [Server thread/INFO]: [Multiverse-Core] Loading server plugin Multiverse-Core v4.3.14
[12:27:46] [Server thread/INFO]: [ViaRewind] Loading server plugin ViaRewind v4.0.5
[12:27:46] [Server thread/INFO]: [Multiverse-Portals] Loading server plugin Multiverse-Portals v4.2.3
[12:27:46] [Server thread/INFO]: [FancyNpcs] Loading server plugin FancyNpcs v2.4.4
[12:27:46] [Server thread/INFO]: [WorldServer] Loading server plugin WorldServer v1.9.4
[12:27:46] [Server thread/INFO]: [WorldServer] Registered with Vault!
[12:27:46] [Server thread/INFO]: [TerraformGenerator] Loading server plugin TerraformGenerator v18.1.2
[12:27:46] [Server thread/INFO]: [TAB] Loading server plugin TAB v5.0.7
[12:27:46] [Server thread/INFO]: [SkinsRestorer] Loading server plugin SkinsRestorer v15.6.5
[12:27:46] [Server thread/INFO]: [Scale] Loading server plugin Scale v1.0.1
[12:27:46] [Server thread/INFO]: [PerWorldPlugins] Loading server plugin PerWorldPlugins v1.5.9
[12:27:46] [Server thread/INFO]: [PerWorldPlugins] Enabling PerWorldPlugins v1.5.9
[12:27:47] [Server thread/INFO]: <---------------------------------------->
[12:27:47] [Server thread/INFO]: PerWorldPlugins 1.5.9 has been enabled.
[12:27:47] [Server thread/INFO]: <---------------------------------------->
[12:27:47] [Server thread/INFO]: [MyCommand] Loading server plugin MyCommand v5.7.4
[12:27:47] [Server thread/INFO]: [Multiverse-NetherPortals] Loading server plugin Multiverse-NetherPortals v4.2.3
[12:27:47] [Server thread/INFO]: [Multiverse-Inventories] Loading server plugin Multiverse-Inventories v4.2.6
[12:27:47] [Server thread/INFO]: [LoginSecurity] Loading server plugin LoginSecurity v3.3.0
[12:27:47] [Server thread/INFO]: [LobbyHeadItem] Loading server plugin LobbyHeadItem v1.9.21
[12:27:47] [Server thread/INFO]: [GSit] Loading server plugin GSit v2.2.1
[12:27:47] [Server thread/INFO]: [Gender] Loading server plugin Gender v1.1
[12:27:47] [Server thread/INFO]: [FancyHolograms] Loading server plugin FancyHolograms v2.4.2
[12:27:47] [Server thread/INFO]: [EliteMobs] Loading server plugin EliteMobs v9.3.1
[12:27:47] [FancyLogger/INFO]: [FancyHolograms] (Server thread) INFO: Successfully loaded FancyHolograms version 2.4.2
[12:27:47] [Server thread/INFO]: [EliteMobs] MagmaCore v1.7 initialized!
[12:27:47] [Server thread/WARN]: [EliteMobs] Error loading WorldGuard. EliteMob-specific flags will not work. Except if you just reloaded the plugin, in which case they will totally work.
[12:27:47] [Server thread/INFO]: [AntiPopup] Loading server plugin AntiPopup v10.1
[12:27:49] [Server thread/INFO]: [AntiPopup] Loaded PacketEvents.
[12:27:49] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[12:27:49] [Server thread/INFO]: [LuckPerms] Enabling LuckPerms v5.4.156
[12:27:50] [Server thread/INFO]:         __    
[12:27:50] [Server thread/INFO]:   |    |__)   LuckPerms v5.4.156
[12:27:50] [Server thread/INFO]:   |___ |      Running on Bukkit - Paper
[12:27:50] [Server thread/INFO]: 
[12:27:50] [Server thread/INFO]: [LuckPerms] Loading configuration...
[12:27:50] [Server thread/INFO]: [LuckPerms] Loading storage provider... [H2]
[12:27:50] [Server thread/INFO]: [LuckPerms] Loading internal permission managers...
[12:27:50] [Server thread/INFO]: [LuckPerms] Performing initial data load...
[12:27:51] [Server thread/INFO]: [LuckPerms] Successfully enabled. (took 1266ms)
[12:27:51] [Server thread/INFO]: [Vault] Enabling Vault v1.7.3-b131
[12:27:51] [Server thread/INFO]: [Vault] [Economy] Essentials Economy found: Waiting
[12:27:51] [Server thread/INFO]: [Vault] [Permission] SuperPermissions loaded as backup permission system.
[12:27:51] [Server thread/INFO]: [Vault] Enabled Version 1.7.3-b131
[12:27:51] [Server thread/INFO]: [LuckPerms] Registered Vault permission & chat hook.
[12:27:51] [Server thread/INFO]: [WorldEdit] Enabling WorldEdit v7.3.10+7004-768a436
[12:27:51] [Server thread/INFO]: Registering commands with com.sk89q.worldedit.bukkit.BukkitServerInterface
[12:27:51] [Server thread/INFO]: WEPIF: Vault detected! Using Vault for permissions
[12:27:51] [Server thread/INFO]: Using com.sk89q.worldedit.bukkit.adapter.impl.v1_21_4.PaperweightAdapter as the Bukkit adapter
[12:27:52] [Server thread/INFO]: [ViaRewind] Enabling ViaRewind v4.0.5
[12:27:52] [Server thread/INFO]: [TerraformGenerator] Enabling TerraformGenerator v18.1.2
[12:27:52] [Server thread/INFO]: [TerraformGenerator] Custom Logger Initialized
[12:27:52] [Server thread/INFO]: [TerraformGenerator] bStats Metrics enabled.
[12:27:52] [Server thread/INFO]: [TerraformGenerator] Detected version: 1.21.4, number: 21.4
[12:27:52] [Server thread/INFO]: [SkinsRestorer] Enabling SkinsRestorer v15.6.5
[12:27:53] [Server thread/INFO]: [SkinsRestorer] Running on Minecraft 1.21.4.
[12:27:53] [Server thread/INFO]: [SkinsRestorer] Using paper join listener!
[12:27:53] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: skinsrestorer [15.6.5]
[12:27:53] [Server thread/INFO]: [SkinsRestorer] PlaceholderAPI expansion registered!
[12:27:53] [Server thread/WARN]: **** SERVER IS RUNNING IN OFFLINE/INSECURE MODE!
[12:27:53] [Server thread/WARN]: The server will make no attempt to authenticate usernames. Beware.
[12:27:53] [Server thread/WARN]: While this makes the game possible to play without internet access, it also opens up the ability for hackers to connect with any username they choose.
[12:27:53] [Server thread/WARN]: To change this, set "online-mode" to "true" in the server.properties file.
[12:27:53] [Server thread/INFO]: Preparing level "HUB"
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] ----------------------------------------------
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     +==================+
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     |   SkinsRestorer  |
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     |------------------|
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     |  Standalone Mode |
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     +==================+
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] ----------------------------------------------
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     Version: 15.6.5
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     Commit: f8c262a
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     A new version (15.6.6) is available! Downloading update...
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] ----------------------------------------------
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] Do you have issues? Read our troubleshooting guide: https://skinsrestorer.net/docs/troubleshooting
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] Want to support SkinsRestorer? Consider donating: https://skinsrestorer.net/donate
[12:27:53] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] [GitHubUpdate] Downloading update...
[12:27:54] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[12:27:54] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:54] [Server thread/INFO]: Time elapsed: 306 ms
[12:27:54] [Server thread/INFO]: [ViaVersion] Enabling ViaVersion v5.2.1
[12:27:54] [Server thread/INFO]: [ViaVersion] ViaVersion detected server version: 1.21.4 (٧٦٩)
[12:27:54] [Server thread/INFO]: [PlaceholderAPI] Enabling PlaceholderAPI v2.11.6
[12:27:54] [Server thread/INFO]: [PlaceholderAPI] Fetching available expansion information...
[12:27:54] [Server thread/INFO]: [PlayerParticles] Enabling PlayerParticles v8.8
[12:27:54] [Server thread/INFO]: [PlayerParticles] Data handler connected using SQLite.
[12:27:55] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: playerparticles [8.8]
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Enabling AdvancedTeleport v6.1.2
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Performing server version check.
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Detected major version: 21
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Advanced Teleport is now enabling...
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Setting up permissions integration with Vault.
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] LuckPerms hooked into successfully.
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Loading MainConfig.
[12:27:55] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] [GitHubUpdate] Downloaded update in ١٠٧٩ms
[12:27:55] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] [GitHubUpdate] Update saved as SkinsRestorer.jar
[12:27:55] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] [GitHubUpdate] The update will be loaded on the next server restart
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Registered the image warp_default!
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Registered the image home_default!
[12:27:55] [Server thread/INFO]: [AdvancedTeleport] Registered the image spawn_default!
[12:27:55] [Server thread/INFO]: [Essentials] Enabling Essentials v2.21.1-dev+5-dabe687
[12:27:55] [Server thread/INFO]: [Essentials] Attempting to convert old kits in config.yml to new kits.yml
[12:27:55] [Server thread/INFO]: [Essentials] No kits found to migrate.
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.8.3+ Spawner Item Provider as the provider for SpawnerItemProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Paper Known Commands Provider as the provider for KnownCommandsProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.14.4+ Persistent Data Container Provider as the provider for PersistentDataProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Paper Container Provider as the provider for ContainerProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Reflection Formatted Command Alias Provider as the provider for FormattedCommandAliasProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Paper Tick Count Provider as the provider for TickCountProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.12.2+ Player Locale Provider as the provider for PlayerLocaleProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Reflection Online Mode Provider as the provider for OnlineModeProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.12+ Spawner Block Provider as the provider for SpawnerBlockProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.11+ Item Unbreakable Provider as the provider for ItemUnbreakableProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Paper Material Tag Provider as the provider for MaterialTagProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.14+ Sign Data Provider as the provider for SignDataProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.17.1+ World Info Provider as the provider for WorldInfoProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.21.4+ Sync Commands Provider as the provider for SyncCommandsProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.13+ Spawn Egg Provider as the provider for SpawnEggProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.21+ InventoryView Interface ABI Provider as the provider for InventoryViewProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.20.5+ Banner Data Provider as the provider for BannerDataProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Paper Biome Key Provider as the provider for BiomeKeyProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.20.6+ Potion Meta Provider as the provider for PotionMetaProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected 1.20.4+ Damage Event Provider as the provider for DamageEventProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Paper Server State Provider as the provider for ServerStateProvider
[12:27:55] [Server thread/INFO]: [Essentials] Selected Paper Serialization Provider as the provider for SerializationProvider
[12:27:55] [Server thread/INFO]: [Essentials] Loaded 43465 items from items.json.
[12:27:55] [Server thread/INFO]: [Essentials] Using locale en_US
[12:27:55] [Server thread/INFO]: [Essentials] ServerListPingEvent: Spigot iterator API
[12:27:56] [Server thread/INFO]: [Essentials] Starting Metrics. Opt-out using the global bStats config.
[12:27:56] [Server thread/INFO]: [Vault] [Economy] Essentials Economy hooked.
[12:27:56] [Server thread/INFO]: [Essentials] Using Vault based permissions (LuckPerms)
[12:27:56] [Server thread/INFO]: [EssentialsSpawn] Enabling EssentialsSpawn v2.21.1-dev+5-dabe687
[12:27:56] [Server thread/INFO]: [EssentialsSpawn] Starting Metrics. Opt-out using the global bStats config.
[12:27:56] [Server thread/INFO]: [IridiumSkyblock] Enabling IridiumSkyblock v4.1.0-B5
[12:27:56] [Server thread/INFO]: Preparing start region for dimension minecraft:iridiumskyblock
[12:27:56] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:56] [Server thread/INFO]: Time elapsed: 61 ms
[12:27:56] [Server thread/INFO]: Preparing start region for dimension minecraft:iridiumskyblock_nether
[12:27:56] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:56] [Server thread/INFO]: Time elapsed: 46 ms
[12:27:56] [Server thread/INFO]: Preparing start region for dimension minecraft:iridiumskyblock_the_end
[12:27:56] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:56] [Server thread/INFO]: Time elapsed: 47 ms
[12:27:56] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'IridiumSkyblock_the_end'
[12:27:56] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'IridiumSkyblock_the_end'
[12:27:56] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'IridiumSkyblock_the_end'
[12:27:56] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'IridiumSkyblock_the_end' in ٠٫٠٠s
[12:27:56] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'IridiumSkyblock_the_end'
[12:27:56] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'IridiumSkyblock_the_end'
[12:27:56] [Server thread/INFO]: [NBTAPI] [NBTAPI] Found Minecraft: 1.21.4! Trying to find NMS support
[12:27:56] [Server thread/INFO]: [NBTAPI] [NBTAPI] NMS support 'MC1_21_R3' loaded!
[12:27:56] [Server thread/INFO]: [NBTAPI] [NBTAPI] Using the plugin 'IridiumSkyblock' to create a bStats instance!
[12:27:56] [Server thread/INFO]: Preparing start region for dimension minecraft:iridiumskyblock_the_end
[12:27:56] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:56] [Server thread/INFO]: Time elapsed: 43 ms
[12:27:56] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: iridiumskyblock [4.1.0-B5]
[12:27:56] [Server thread/INFO]: [IridiumSkyblock] Successfully registered Placeholders for PlaceholderAPI.
[12:27:56] [Server thread/INFO]: [IridiumSkyblock] -------------------------------
[12:27:56] [Server thread/INFO]: [IridiumSkyblock] 
[12:27:56] [Server thread/INFO]: [IridiumSkyblock] IridiumSkyblock Enabled!
[12:27:56] [Server thread/INFO]: [IridiumSkyblock] 
[12:27:56] [Server thread/INFO]: [IridiumSkyblock] -------------------------------
[12:27:56] [Server thread/INFO]: [ViaBackwards] Enabling ViaBackwards v5.2.1
[12:27:56] [Server thread/INFO]: [Multiverse-Core] Enabling Multiverse-Core v4.3.14
[12:27:56] [Server thread/WARN]: [Multiverse-Core] "Multiverse-Core v4.3.14" has registered a listener for org.bukkit.event.entity.EntityCreatePortalEvent on method "public void com.onarandombox.MultiverseCore.listeners.MVPortalListener.entityPortalCreate(org.bukkit.event.entity.EntityCreatePortalEvent)", but the event is Deprecated. "Server performance will be affected"; please notify the authors [dumptruckman, Rigby, fernferret, lithium3141, main--].
[12:27:56] [Server thread/INFO]: [Multiverse-Core] §aWe are aware of the warning about the deprecated event. There is no alternative that allows us to do what we need to do and performance impact is negligible. It is safe to ignore.
[12:27:57] [Server thread/INFO]: Preparing start region for dimension minecraft:em_fireworks
[12:27:57] [Server thread/INFO]: Time elapsed: 2 ms
[12:27:57] [Server thread/INFO]: Preparing start region for dimension minecraft:em_north_pole
[12:27:57] [Server thread/INFO]: Time elapsed: 4 ms
[12:27:57] [Server thread/INFO]: Preparing start region for dimension minecraft:x_survival_spawner
[12:27:57] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:57] [Server thread/INFO]: Time elapsed: 499 ms
[12:27:57] [Server thread/INFO]: Preparing start region for dimension minecraft:em_knight_castle
[12:27:57] [Server thread/INFO]: Time elapsed: 2 ms
[12:27:57] [Server thread/INFO]: [TerraformGenerator] Detected world: em_survival, commencing injection... 
[12:27:57] [Server thread/INFO]: [TerraformGenerator] Injection success! Proceeding with generation.
[12:27:57] [Server thread/INFO]: Preparing start region for dimension minecraft:em_survival
[12:27:57] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:58] [Server thread/INFO]: Time elapsed: 158 ms
[12:27:58] [Server thread/INFO]: [TerraformGenerator] em_survival loaded.
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:skyblock_spawner
[12:27:58] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:58] [Server thread/INFO]: Time elapsed: 90 ms
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:em_sewer_maze
[12:27:58] [Server thread/INFO]: Time elapsed: 4 ms
[12:27:58] [Server thread/INFO]: [TerraformGenerator] Detected world: world, commencing injection... 
[12:27:58] [Server thread/INFO]: [TerraformGenerator] Injection success! Proceeding with generation.
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:world
[12:27:58] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:58] [Server thread/INFO]: Time elapsed: 97 ms
[12:27:58] [Server thread/INFO]: [TerraformGenerator] world loaded.
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:em_world_nether
[12:27:58] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:58] [Server thread/INFO]: Time elapsed: 78 ms
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:em_world_the_end
[12:27:58] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:58] [Server thread/INFO]: Time elapsed: 73 ms
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:em_adventurers_guild
[12:27:58] [Server thread/INFO]: Time elapsed: 2 ms
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:world_nether
[12:27:58] [Server thread/INFO]: Preparing spawn area: 0%
[12:27:58] [Server thread/INFO]: Time elapsed: 81 ms
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:em_hallosseum
[12:27:58] [Server thread/INFO]: Time elapsed: 3 ms
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:em_shadow_of_the_binder_of_worlds
[12:27:58] [Server thread/INFO]: Time elapsed: 3 ms
[12:27:58] [Server thread/INFO]: Preparing start region for dimension minecraft:em_steamworks_lair
[12:27:58] [Server thread/INFO]: Time elapsed: 2 ms
[12:27:58] [Server thread/INFO]: [Multiverse-Core] 19 - World(s) loaded.
[12:27:58] [Server thread/WARN]: [Multiverse-Core] Buscript failed to load! The script command will be disabled! If you would like not to see this message, use `/mv conf enablebuscript false` to disable Buscript from loading.
[12:27:58] [Server thread/INFO]: [Multiverse-Core] Version 4.3.14 (API v24) Enabled - By dumptruckman, Rigby, fernferret, lithium3141 and main--
[12:27:58] [Server thread/INFO]: [Multiverse-Portals] Enabling Multiverse-Portals v4.2.3
[12:27:59] [Server thread/INFO]: [Multiverse-Portals] 1 - Portals(s) loaded
[12:27:59] [Server thread/INFO]: [Multiverse-Portals] Found WorldEdit. Using it for selections.
[12:27:59] [Server thread/INFO]: [Multiverse-Portals 4.2.3]  Enabled - By Rigby and fernferret
[12:27:59] [Server thread/INFO]: [FancyNpcs] Enabling FancyNpcs v2.4.4
[12:27:59] [FancyLogger/INFO]: [FancyNpcs] (Server thread) INFO: FancyNpcs (2.4.4) has been enabled.
[12:27:59] [Server thread/INFO]: [WorldServer] Enabling WorldServer v1.9.4
[12:27:59] [Server thread/INFO]: [WorldServer] Loaded all config files
[12:27:59] [Server thread/INFO]: [WorldServer] Loaded WorldServer v1.9.4 in ٢٥ms!
[12:27:59] [Server thread/INFO]: [TAB] Enabling TAB v5.0.7
[12:27:59] [Server thread/INFO]: [TAB] Loaded NMS hook in 42ms
[12:27:59] [ForkJoinPool.commonPool-worker-1/WARN]: [FancyNpcs] 
-------------------------------------------------------
You are not using the latest version of the FancyNpcs plugin.
Please update to the newest version (2.5.0).
https://modrinth.com/plugin/FancyNpcs
-------------------------------------------------------

[12:27:59] [FancyLogger/INFO]: [FancyNpcs] (ForkJoinPool.commonPool-worker-1) WARN: You are not using the latest version of the FancyNpcs plugin.
[12:27:59] [Server thread/INFO]: [TAB] Enabled in 196ms
[12:27:59] [Server thread/INFO]: [Scale] Enabling Scale v1.0.1
[12:27:59] [Server thread/INFO]: [Scale] Scale has been enabled!
[12:27:59] [Server thread/INFO]: [MyCommand] Enabling MyCommand v5.7.4
[12:27:59] [Server thread/INFO]: *-=-=-=-=-=-=-=-=-* MyCommand v.5.7.4*-=-=-=-=-=-=-=-=-=-*
[12:27:59] [Server thread/INFO]: | Hooked on Vault 1.7.3-b131
[12:27:59] [Server thread/INFO]: | Command file(s) found : 1
[12:27:59] [Server thread/INFO]: | Config : Ready.
[12:27:59] [Server thread/INFO]: | ProtocolLib not found.
[12:27:59] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: mycommand [1.0.0]
[12:27:59] [Server thread/INFO]: | Placeholder_API : Hooked, Ok.
[12:27:59] [Server thread/INFO]: | Custom commands loaded : 49
[12:28:00] [Server thread/INFO]: | New update available : MyCommand v5.7.5
[12:28:00] [Server thread/INFO]: |          by emmerrei a.k.a. ivanfromitaly.           
[12:28:00] [Server thread/INFO]: *-=-=-=-=-=-=-=-=-=-*   Done!   *-=-=-=-=-=-=-=-=-=-=-*
[12:28:00] [Server thread/INFO]: [Multiverse-NetherPortals] Enabling Multiverse-NetherPortals v4.2.3
[12:28:00] [Server thread/INFO]: [Multiverse-NetherPortals 4.2.3]  Enabled - By Rigby and fernferret
[12:28:00] [Server thread/INFO]: [Multiverse-Inventories] Enabling Multiverse-Inventories v4.2.6
[12:28:00] [Server thread/INFO]: [Multiverse-Inventories] The following worlds for group 'default' are not loaded: world_the_end
[12:28:00] [Server thread/INFO]: [Multiverse-Inventories 4.2.6] enabled.
[12:28:00] [Server thread/INFO]: [LoginSecurity] Enabling LoginSecurity v3.3.0
[12:28:00] [Server thread/INFO]: [LoginSecurity] Loading base translations from "en_us"
[12:28:00] [Server thread/INFO]: [LoginSecurity] Loading specified translations from "en_us"
[12:28:00] [Server thread/INFO]: [LoginSecurity] Registering commands...
[12:28:00] [Server thread/INFO]: [LoginSecurity] Registering listeners...
[12:28:00] [Server thread/INFO]: [LoginSecurity] Using 1.12+ map captcha renderer
[12:28:00] [Server thread/INFO]: [LobbyHeadItem] Enabling LobbyHeadItem v1.9.21
[12:28:00] [Server thread/INFO]: [Lobby Head Item] Successfully enabled v1.9.21
[12:28:00] [Server thread/INFO]: --------------------------------------------------------------------------------------
[12:28:00] [Server thread/INFO]:          Developed by Ruben_Artz
[12:28:00] [Server thread/INFO]: [Lobby Head Item] Version: 1.9.21 is loading... (Current)
[12:28:00] [Server thread/INFO]: [Lobby Head Item] Server: 1.21.4-147-3bd69f2 (MC: 1.21.4)
[12:28:00] [Server thread/INFO]: [Lobby Head Item] Loading necessary files...
[12:28:00] [Server thread/INFO]:  
[12:28:00] [Server thread/INFO]: Lobby Head Item Starting plugin...
[12:28:00] [Server thread/INFO]: 
[12:28:00] [Server thread/INFO]: --------------------------------------------------------------------------------------
[12:28:00] [Server thread/INFO]: [GSit] Enabling GSit v2.2.1
[12:28:00] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: gsit [2.2.1]
[12:28:00] [Server thread/INFO]: [GSit] The plugin was successfully enabled.
[12:28:00] [Server thread/INFO]: [GSit] Link with PlaceholderAPI successful!
[12:28:00] [Server thread/INFO]: [Gender] Enabling Gender v1.1*
[12:28:00] [Server thread/INFO]: [Gender] [STDOUT] [Gender] Enabled v1.1
[12:28:00] [Server thread/WARN]: Nag author(s): '[]' of 'Gender v1.1' about their usage of System.out/err.print. Please use your plugin's logger instead (JavaPlugin#getLogger).
[12:28:00] [Server thread/INFO]: [FancyHolograms] Enabling FancyHolograms v2.4.2
[12:28:00] [FancyLogger/INFO]: [FancyHolograms] (Server thread) INFO: Successfully enabled FancyHolograms version 2.4.2
[12:28:00] [Server thread/INFO]: [EliteMobs] Enabling EliteMobs v9.3.1
[12:28:00] [Server thread/INFO]:  _____ _     _____ _____ ________  ______________  _____
[12:28:00] [Server thread/INFO]: |  ___| |   |_   _|_   _|  ___|  \/  |  _  | ___ \/  ___|
[12:28:00] [Server thread/INFO]: | |__ | |     | |   | | | |__ | .  . | | | | |_/ /\ `--.
[12:28:00] [Server thread/INFO]: |  __|| |     | |   | | |  __|| |\/| | | | | ___ \ `--. \
[12:28:00] [Server thread/INFO]: | |___| |_____| |_  | | | |___| |  | \ \_/ / |_/ //\__/ /
[12:28:00] [Server thread/INFO]: \____/\_____/\___/  \_/ \____/\_|  |_/\___/\____/ \____/
[12:28:00] [Server thread/INFO]: By MagmaGuy - v. 9.3.1
[12:28:00] [Server thread/INFO]: [EliteMobs] Supported server version detected: v1_21_R3
[12:28:00] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 
[12:28:00] [FancyLogger/INFO]:  holograms for all loaded worlds
[12:28:00] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٣٣ ms to scan ١ urls, producing ١ keys and ١٤ values
[12:28:00] [FancyLogger/INFO]: [FancyHolograms] (ForkJoinPool.commonPool-worker-1) WARN: 
[12:28:00] [FancyLogger/INFO]: -------------------------------------------------------
[12:28:00] [FancyLogger/INFO]: You are not using the latest version of the FancyHolograms plugin.
[12:28:00] [FancyLogger/INFO]: Please update to the newest version (2.5.0).
[12:28:00] [FancyLogger/INFO]: https://modrinth.com/plugin/FancyHolograms
[12:28:00] [FancyLogger/INFO]: -------------------------------------------------------
[12:28:00] [FancyLogger/INFO]: 
[12:28:00] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٤ ms to scan ١ urls, producing ١ keys and ٥٩ values
[12:28:01] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٦ ms to scan ١ urls, producing ١ keys and ١٦ values
[12:28:02] [Server thread/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[12:28:10] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٤ ms to scan ١ urls, producing ١ keys and ٧١ values
[12:28:11] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ١٩ ms to scan ١ urls, producing ١٠ keys and ٢١٦ values
[12:28:11] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٣ ms to scan ١ urls, producing ١ keys and ١٥ values
[12:28:11] [Server thread/INFO]: [EliteMobs] WorldGuard compatibility is not enabled!
[12:28:11] [Server thread/INFO]: [EliteMobs] [(EliteMobs] Vault detected.
[12:28:11] [Server thread/INFO]: [EliteMobs] Opened database successfully
[12:28:12] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: elitemobs [9.3.1]
[12:28:12] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٥ ms to scan ١ urls, producing ١ keys and ١١٦ values
[12:28:15] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٥ ms to scan ١ urls, producing ١ keys and ٧١ values
[12:28:15] [Server thread/WARN]: [EliteMobs] File  C:\Users\<USER>\Desktop\xMine\simMC\.\em_primis_wormhole does not exist!
[12:28:15] [Server thread/WARN]: [EliteMobs] File  C:\Users\<USER>\Desktop\xMine\simMC\.\em_oasis_wormhole does not exist!
[12:28:15] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٧ ms to scan ١ urls, producing ١ keys and ١٣٤ values
[12:28:17] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ١ ms to scan ١ urls, producing ١ keys and ١ values
[12:28:17] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٤ ms to scan ١ urls, producing ١ keys and ٣٣ values
[12:28:17] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ١ ms to scan ١ urls, producing ١ keys and ٢٢ values
[12:28:18] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٢ ms to scan ١ urls, producing ١ keys and ١ values
[12:28:18] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ١ ms to scan ١ urls, producing ١ keys and ٨ values
[12:28:18] [Server thread/INFO]: [org.reflections.Reflections] Reflections took ٢ ms to scan ١ urls, producing ١ keys and ٤ values
[12:28:18] [Server thread/INFO]: [AntiPopup] Enabling AntiPopup v10.1
[12:28:18] [Server thread/INFO]: [AntiPopup] Config enabled.
[12:28:18] [Server thread/INFO]: [AntiPopup] Loaded a hook for ViaVersion.
[12:28:18] [Server thread/INFO]: [AntiPopup] Initiated PacketEvents.
[12:28:18] [Server thread/INFO]: [AntiPopup] Hooked on 1.21.4
[12:28:18] [Server thread/INFO]: [AntiPopup] Commands registered.
[12:28:18] [Server thread/INFO]: [AntiPopup] Logger filter enabled.
[12:28:18] [Server thread/INFO]: [spark] Starting background profiler...
[12:28:18] [Server thread/INFO]: [spark] The async-profiler engine is not supported for your os/arch (windows11/amd64), so the built-in Java engine will be used instead.
[12:28:18] [Server thread/INFO]: [PlaceholderAPI] Placeholder expansion registration initializing...
[12:28:18] [Server thread/INFO]: 0 placeholder hook(s) registered!
[12:28:18] [Server thread/INFO]: Done preparing level "HUB" (24.883s)
[12:28:18] [Server thread/INFO]: Running delayed init tasks
[12:28:18] [Craft Scheduler Thread - 5 - ViaVersion/INFO]: [ViaVersion] Finished mapping loading, shutting down loader executor.
[12:28:18] [Craft Scheduler Thread - 15 - AdvancedTeleport/INFO]: [AdvancedTeleport] Preparing random teleportation locations. If your server performance or memory suffers, please set `use-rapid-response` to false in the config.yml file.
[12:28:18] [Craft Scheduler Thread - 16 - Essentials/INFO]: [Essentials] Fetching version information...
[12:28:18] [Server thread/INFO]: [PerWorldPlugins] Converted all Listeners correctly.
[12:28:18] [Craft Scheduler Thread - 8 - PlayerParticles/INFO]: [RoseGarden] An update for PlayerParticles (v8.9) is available! You are running v8.8.
[12:28:19] [Craft Scheduler Thread - 15 - AdvancedTeleport/INFO]: [AdvancedTeleport] A new version is available!
[12:28:19] [Craft Scheduler Thread - 15 - AdvancedTeleport/INFO]: [AdvancedTeleport] Current version you're using: 6.1.2
[12:28:19] [Craft Scheduler Thread - 15 - AdvancedTeleport/INFO]: [AdvancedTeleport] Latest version available: 6.1.3
[12:28:19] [Craft Scheduler Thread - 15 - AdvancedTeleport/INFO]: [AdvancedTeleport] Download link: https://www.spigotmc.org/resources/advancedteleport.64139/
[12:28:19] [Craft Scheduler Thread - 21 - EliteMobs/INFO]: [EliteMobs] Latest public release is 9.4.3
[12:28:19] [Craft Scheduler Thread - 21 - EliteMobs/INFO]: [EliteMobs] Your version is 9.3.1
[12:28:19] [Craft Scheduler Thread - 21 - EliteMobs/WARN]: [EliteMobs] [EliteMobs] A newer version of this plugin is available for download!
[12:28:19] [Craft Scheduler Thread - 22 - EliteMobs/WARN]: [EliteMobs] Content §2[lvl 020] §aThe Fireworks is outdated! You should go download the updated version! Your version: 8 / remote version: 9 / Link: https://nightbreak.io/plugin/elitemobs/#fireworks
[12:28:19] [Craft Scheduler Thread - 22 - EliteMobs/WARN]: [EliteMobs] Content §2[lvl 020-035] §8The Sewers is outdated! You should go download the updated version! Your version: 7 / remote version: 8 / Link: https://nightbreak.io/plugin/elitemobs/#the-sewers
[12:28:19] [Server thread/INFO]: [Essentials] Essentials found a compatible payment resolution method: Vault Compatibility Layer (v1.7.3-b131)!
[12:28:19] [Server thread/INFO]: The sewers echo with the clamor of a returned king...
[12:28:19] [Server thread/INFO]: Sparks fill the sky!
[12:28:19] [Server thread/INFO]: Don't lose your head now, the headless horseman walks the earth once again!
[12:28:19] [Server thread/INFO]: Santa Claus is getting ready for Christmas!!
[12:28:19] [Server thread/INFO]: The shadow of binder of worlds approaches!
[12:28:19] [Server thread/INFO]: [Multiverse-Inventories] First run!
[12:28:19] [Server thread/WARN]: [ViaVersion] There is a newer plugin version available: 5.3.2, you're on: 5.2.1
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] ********************************************************
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] * There is a new version of IridiumSkyblock available!
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] *  
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] * Your version:   §c4.1.0-B5
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] * Latest version: §a4.1.0
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] *  
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] * Please update to the newest version.
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] *  
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] * Download:
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] *   https://www.spigotmc.org/resources/62480
[12:28:19] [Server thread/WARN]: [IridiumSkyblock] ********************************************************
[12:28:19] [Server thread/INFO]: Done (61.775s)! For help, type "help"
[12:28:19] [Craft Scheduler Thread - 15 - Vault/INFO]: [Vault] Checking for Updates ... 
[12:28:19] [Craft Scheduler Thread - 15 - Vault/INFO]: [Vault] No new version available
[12:28:19] [Craft Scheduler Thread - 16 - Essentials/WARN]: [Essentials] You're ٢٢ EssentialsX dev build(s) out of date!
[12:28:19] [Craft Scheduler Thread - 16 - Essentials/WARN]: [Essentials] Download it here: https://essentialsx.net/downloads.html
[12:28:28] [Server thread/INFO]: [MyCommand] found an update for MyCommand. Type /mycommand for more infos.
[12:30:29] [User Authenticator #0/INFO]: UUID of player RootCraft is ddbba869-fa9f-36ea-87f5-1c0e36feab65
[12:30:30] [Server thread/INFO]: RootCraft joined the game
[12:30:30] [Server thread/INFO]: RootCraft[/127.0.0.1:54174] logged in with entity id 89 at ([HUB]12.564814760482971, 1.0, 11.46867782474209)
[12:30:31] [Craft Scheduler Thread - 30 - EliteMobs/INFO]: [EliteMobs] User ddbba869-fa9f-36ea-87f5-1c0e36feab65 data successfully read!
[12:31:10] [Server thread/INFO]: RootCraft issued server command: /go_em_survival
[12:31:10] [Server thread/INFO]: RootCraft issued mycmd (go_to_em_survival) command /go_em_survival
[12:31:23] [Server thread/INFO]: RootCraft issued server command: /spawn
[12:31:29] [Server thread/INFO]: RootCraft issued server command: /hub
[12:31:29] [Server thread/INFO]: RootCraft issued mycmd (go_to_hub) command /hub
[12:31:34] [Server thread/INFO]: RootCraft issued server command: /go_em_survival
[12:31:34] [Server thread/INFO]: RootCraft issued mycmd (go_to_em_survival) command /go_em_survival
[12:31:41] [Server thread/INFO]: RootCraft issued server command: /kill RootCraft
[12:31:41] [Server thread/INFO]: RootCraft died
[12:32:04] [Server thread/INFO]: RootCraft issued server command: /home
[12:32:10] [Server thread/INFO]: RootCraft issued server command: /rtp em_survival
[12:32:10] [Server thread/INFO]: RootCraft issued mycmd (rtp) command /rtp em_survival
[12:32:10] [Paper Common Worker #1/WARN]: [ca.spottedleaf.moonrise.patches.chunk_system.scheduling.task.ChunkUpgradeGenericStatusTask] Future status not complete after scheduling: minecraft:biomes, generate: true
[12:33:18] [Server thread/INFO]: [EliteMobs] Event forgotten_experiment_event.yml has been queued!
[12:37:30] [Server thread/INFO]: RootCraft issued server command: /gamemode creative
[12:38:27] [Server thread/WARN]: Can't keep up! Is the server overloaded? Running 5090ms or 101 ticks behind
[12:38:27] [Server thread/WARN]: RootCraft moved too quickly! 29.617421159646256,0.5993773063680266,-85.83018940343072
[12:39:29] [Server thread/INFO]: RootCraft lost connection: Disconnected
[12:39:29] [Server thread/INFO]: RootCraft left the game
[12:39:38] [Server thread/INFO]: Stopping the server
[12:39:38] [Server thread/INFO]: Stopping server
[12:39:38] [Server thread/INFO]: [AntiPopup] Disabling AntiPopup v10.1
[12:39:38] [Server thread/INFO]: [AntiPopup] Disabled PacketEvents.
[12:39:38] [Server thread/INFO]: [EliteMobs] Disabling EliteMobs v9.3.1
[12:39:38] [Server thread/INFO]: [EliteMobs] Starting EliteMobs shutdown sequence...
[12:39:38] [Server thread/INFO]: [EliteMobs] Saving EliteMobs databases...
[12:39:38] [Server thread/INFO]: [EliteMobs] All done! Good night.
[12:39:38] [Server thread/INFO]: [FancyHolograms] Disabling FancyHolograms v2.4.2
[12:39:38] [FancyLogger/INFO]: [FancyHolograms] (Server thread) INFO: Successfully disabled FancyHolograms version 2.4.2
[12:39:38] [Server thread/INFO]: [Gender] Disabling Gender v1.1
[12:39:38] [Server thread/INFO]: [Gender] [STDOUT] [Gender] Disabled v1.1
[12:39:38] [Server thread/WARN]: Nag author(s): '[]' of 'Gender v1.1' about their usage of System.out/err.print. Please use your plugin's logger instead (JavaPlugin#getLogger).
[12:39:38] [Server thread/INFO]: [GSit] Disabling GSit v2.2.1
[12:39:38] [Server thread/INFO]: [GSit] The plugin was successfully disabled.
[12:39:38] [Server thread/INFO]: [LobbyHeadItem] Disabling LobbyHeadItem v1.9.21
[12:39:38] [Server thread/INFO]: [LoginSecurity] Disabling LoginSecurity v3.3.0
[12:39:38] [Server thread/INFO]: [LoginSecurity] Waiting for queued tasks...
[12:39:38] [Server thread/INFO]: [LoginSecurity] ExecutorService shut down, ready to disable.
[12:39:38] [Server thread/INFO]: [Multiverse-Inventories] Disabling Multiverse-Inventories v4.2.6
[12:39:38] [Server thread/INFO]: [Multiverse-NetherPortals] Disabling Multiverse-NetherPortals v4.2.3
[12:39:38] [Server thread/INFO]: [Multiverse-NetherPortals] - Disabled
[12:39:38] [Server thread/INFO]: [MyCommand] Disabling MyCommand v5.7.4
[12:39:38] [Server thread/INFO]: *-=-=-=-=-=-=-=-=-* MyCommand v.5.7.4*-=-=-=-=-=-=-=-=-=-*
[12:39:38] [Server thread/INFO]: | Tasks : Stopped all tasks.
[12:39:38] [Server thread/INFO]: *-=-=-=-=-=-=-=-=-=-*    Bye!   *-=-=-=-=-=-=-=-=-=-=-*
[12:39:38] [Server thread/INFO]: [PerWorldPlugins] Disabling PerWorldPlugins v1.5.9
[12:39:38] [Server thread/INFO]: [Scale] Disabling Scale v1.0.1
[12:39:38] [Server thread/INFO]: [Scale] Scale is now disabled!
[12:39:38] [Server thread/INFO]: [SkinsRestorer] Disabling SkinsRestorer v15.6.5
[12:39:38] [Server thread/INFO]: [TAB] Disabling TAB v5.0.7
[12:39:38] [Server thread/INFO]: [TAB] Disabled in 9ms
[12:39:38] [Server thread/INFO]: [TerraformGenerator] Disabling TerraformGenerator v18.1.2
[12:39:38] [Server thread/INFO]: [WorldServer] Disabling WorldServer v1.9.4
[12:39:38] [Server thread/INFO]: [FancyNpcs] Disabling FancyNpcs v2.4.4
[12:39:38] [FancyLogger/INFO]: [FancyNpcs] (Server thread) INFO: FancyNpcs has been disabled.
[12:39:38] [Server thread/INFO]: [Multiverse-Portals] Disabling Multiverse-Portals v4.2.3
[12:39:38] [Server thread/INFO]: [ViaRewind] Disabling ViaRewind v4.0.5
[12:39:38] [Server thread/INFO]: [Multiverse-Core] Disabling Multiverse-Core v4.3.14
[12:39:38] [Server thread/INFO]: [ViaBackwards] Disabling ViaBackwards v5.2.1
[12:39:38] [Server thread/INFO]: [IridiumSkyblock] Disabling IridiumSkyblock v4.1.0-B5
[12:39:38] [Server thread/INFO]: [IridiumSkyblock] -------------------------------
[12:39:38] [Server thread/INFO]: [IridiumSkyblock] 
[12:39:38] [Server thread/INFO]: [IridiumSkyblock] IridiumSkyblock Disabled!
[12:39:38] [Server thread/INFO]: [IridiumSkyblock] 
[12:39:38] [Server thread/INFO]: [IridiumSkyblock] -------------------------------
[12:39:38] [Server thread/INFO]: [EssentialsSpawn] Disabling EssentialsSpawn v2.21.1-dev+5-dabe687
[12:39:38] [Server thread/INFO]: [Essentials] Disabling Essentials v2.21.1-dev+5-dabe687
[12:39:38] [Server thread/INFO]: [Vault] [Economy] Essentials Economy unhooked.
[12:39:38] [Server thread/INFO]: [AdvancedTeleport] Disabling AdvancedTeleport v6.1.2
[12:39:38] [Server thread/INFO]: [PlayerParticles] Disabling PlayerParticles v8.8
[12:39:38] [Server thread/INFO]: [WorldEdit] Disabling WorldEdit v7.3.10+7004-768a436
[12:39:38] [Server thread/INFO]: Unregistering com.sk89q.worldedit.bukkit.BukkitServerInterface from WorldEdit
[12:39:38] [Server thread/INFO]: [PlaceholderAPI] Disabling PlaceholderAPI v2.11.6
[12:39:38] [Server thread/INFO]: [Vault] Disabling Vault v1.7.3-b131
[12:39:38] [Server thread/INFO]: [LuckPerms] Disabling LuckPerms v5.4.156
[12:39:38] [Server thread/INFO]: [LuckPerms] Starting shutdown process...
[12:39:38] [Server thread/INFO]: [LuckPerms] Closing storage...
[12:39:38] [Server thread/INFO]: [LuckPerms] Goodbye!
[12:39:38] [Server thread/INFO]: [ViaVersion] Disabling ViaVersion v5.2.1
[12:39:38] [Server thread/INFO]: [ViaVersion] ViaVersion has been disabled; uninjected the platform and shut down the scheduler.
[12:39:38] [Server thread/INFO]: Saving players
[12:39:38] [Server thread/INFO]: Saving worlds
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[HUB]'/minecraft:overworld
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'HUB'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'HUB'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'HUB'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'HUB' in ٠٫٠٢s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'HUB'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'HUB'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[IridiumSkyblock]'/minecraft:iridiumskyblock
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'IridiumSkyblock'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'IridiumSkyblock'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'IridiumSkyblock'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'IridiumSkyblock' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'IridiumSkyblock'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'IridiumSkyblock'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[IridiumSkyblock_nether]'/minecraft:iridiumskyblock_nether
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'IridiumSkyblock_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'IridiumSkyblock_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'IridiumSkyblock_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 2 block chunks, 0 entity chunks, 0 poi chunks in world 'IridiumSkyblock_nether' in ٠٫٠١s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'IridiumSkyblock_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'IridiumSkyblock_nether'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[IridiumSkyblock_the_end]'/minecraft:iridiumskyblock_the_end
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'IridiumSkyblock_the_end'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'IridiumSkyblock_the_end'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'IridiumSkyblock_the_end'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'IridiumSkyblock_the_end' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'IridiumSkyblock_the_end'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'IridiumSkyblock_the_end'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_fireworks]'/minecraft:em_fireworks
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_fireworks'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_fireworks'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_fireworks'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'em_fireworks' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_fireworks'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_fireworks'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_north_pole]'/minecraft:em_north_pole
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_north_pole'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_north_pole'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_north_pole'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'em_north_pole' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_north_pole'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_north_pole'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[x_survival_spawner]'/minecraft:x_survival_spawner
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'x_survival_spawner'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'x_survival_spawner'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'x_survival_spawner'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 21 entity chunks, 0 poi chunks in world 'x_survival_spawner' in ٠٫٠٢s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'x_survival_spawner'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'x_survival_spawner'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_knight_castle]'/minecraft:em_knight_castle
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_knight_castle'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_knight_castle'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_knight_castle'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'em_knight_castle' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_knight_castle'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_knight_castle'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_survival]'/minecraft:em_survival
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_survival'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_survival'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_survival'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 18 entity chunks, 0 poi chunks in world 'em_survival' in ٠٫٠٩s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_survival'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_survival'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[skyblock_spawner]'/minecraft:skyblock_spawner
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'skyblock_spawner'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'skyblock_spawner'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'skyblock_spawner'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'skyblock_spawner' in ٠٫٠١s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'skyblock_spawner'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'skyblock_spawner'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_sewer_maze]'/minecraft:em_sewer_maze
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_sewer_maze'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_sewer_maze'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_sewer_maze'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'em_sewer_maze' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_sewer_maze'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_sewer_maze'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[world]'/minecraft:world
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'world'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'world'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'world'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 2 entity chunks, 0 poi chunks in world 'world' in ٠٫٠١s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'world'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'world'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_world_nether]'/minecraft:em_world_nether
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_world_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_world_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_world_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 6 entity chunks, 0 poi chunks in world 'em_world_nether' in ٠٫٠١s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_world_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_world_nether'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_world_the_end]'/minecraft:em_world_the_end
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_world_the_end'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_world_the_end'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_world_the_end'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 10 entity chunks, 0 poi chunks in world 'em_world_the_end' in ٠٫٠١s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_world_the_end'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_world_the_end'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_adventurers_guild]'/minecraft:em_adventurers_guild
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_adventurers_guild'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_adventurers_guild'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_adventurers_guild'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'em_adventurers_guild' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_adventurers_guild'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_adventurers_guild'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[world_nether]'/minecraft:world_nether
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'world_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'world_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'world_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 6 entity chunks, 0 poi chunks in world 'world_nether' in ٠٫٠١s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'world_nether'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'world_nether'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_hallosseum]'/minecraft:em_hallosseum
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_hallosseum'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_hallosseum'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_hallosseum'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'em_hallosseum' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_hallosseum'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_hallosseum'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_shadow_of_the_binder_of_worlds]'/minecraft:em_shadow_of_the_binder_of_worlds
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_shadow_of_the_binder_of_worlds'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_shadow_of_the_binder_of_worlds'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_shadow_of_the_binder_of_worlds'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'em_shadow_of_the_binder_of_worlds' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_shadow_of_the_binder_of_worlds'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_shadow_of_the_binder_of_worlds'
[12:39:38] [Server thread/INFO]: Saving chunks for level 'ServerLevel[em_steamworks_lair]'/minecraft:em_steamworks_lair
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'em_steamworks_lair'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted chunk system for world 'em_steamworks_lair'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saving all chunkholders for world 'em_steamworks_lair'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'em_steamworks_lair' in ٠٫٠٠s
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'em_steamworks_lair'
[12:39:38] [Server thread/INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'em_steamworks_lair'
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (HUB): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (IridiumSkyblock): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM1): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (em_fireworks): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (em_north_pole): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (x_survival_spawner): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (em_knight_castle): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (em_survival): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (skyblock_spawner): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (em_sewer_maze): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (world): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM1): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (em_adventurers_guild): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (DIM1): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage (em_steamworks_lair): All chunks are saved
[12:39:38] [Server thread/INFO]: ThreadedAnvilChunkStorage: All dimensions are saved
[12:39:38] [Server thread/INFO]: Waiting for I/O tasks to complete...
[12:39:38] [Server thread/INFO]: All I/O tasks to complete
[12:39:38] [Server thread/INFO]: [MoonriseCommon] Awaiting termination of worker pool for up to 60s...
[12:39:38] [Server thread/INFO]: [MoonriseCommon] Awaiting termination of I/O pool for up to 60s...
