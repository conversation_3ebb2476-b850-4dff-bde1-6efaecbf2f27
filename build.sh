#!/bin/bash

# Minecraft Server Docker Build Script
# This script builds the Docker containers for the Minecraft server

set -e  # Exit on any error

echo "🚀 Building Minecraft Server Docker Containers..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p database/init
mkdir -p database/config
mkdir -p config/plugins
mkdir -p logs

# Build the containers
echo "🔨 Building Docker containers..."
docker-compose build --no-cache

echo "✅ Build completed successfully!"
echo ""
echo "📋 Next steps:"
echo "   1. Run './start.sh' to start the server"
echo "   2. Access phpMyAdmin at http://localhost:8080"
echo "   3. Connect to Minecraft server at localhost:25565"
echo ""
echo "🔧 Useful commands:"
echo "   - View logs: docker-compose logs -f minecraft"
echo "   - Stop server: docker-compose down"
echo "   - Restart server: docker-compose restart minecraft"
echo "   - Access server console: docker exec -it minecraft-server bash"
