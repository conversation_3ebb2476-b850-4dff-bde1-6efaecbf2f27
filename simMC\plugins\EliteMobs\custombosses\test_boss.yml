isEnabled: true
entityType: ZOMBIE
name: $eventBossLevel &6Test boss
level: dynamic
healthMultiplier: 2.0
damageMultiplier: 0.5
onDeathCommands:
- say $players has killed $name! That was level $level!
deathMessages:
- '&e&l---------------------------------------------'
- '&eThe Test Boss has been debugged!'
- '&c&l    1st Damager: $damager1name &cwith $damager1damage damage!'
- '&6&l    2nd Damager: $damager2name &6with $damager2damage damage!'
- '&e&l    3rd Damager: $damager3name &ewith $damager3damage damage!'
- '&aSlayers: $players'
- '&e&l---------------------------------------------'
uniqueLootList:
- magmaguys_toothpick.yml:1
powers:
- invulnerability_knockback.yml
onDamageMessages:
- I've hit you!
onDamagedMessages:
- I've been hit!
trails:
- BLOCK_MARKER
locationMessage: 'Test entity: $distance'
spawnMessage: A test boss has been spawned!
deathMessage: A test boss has been slain by $players!
escapeMessage: A test boss entity has escaped!
announcementPriority: 3
timeout: 10
helmet: GOLDEN_HELMET
chestplate: IRON_CHESTPLATE
leggings: LEATHER_LEGGINGS
boots: CHAINMAIL_BOOTS
mainHand: GOLDEN_AXE
offHand: SHIELD
onSpawnBlockStates: []
onRemoveBlockStates: []
bossType: NORMAL
