luckperms.logs.actionlog-prefix=Вхід
luckperms.logs.verbose-prefix=ВБ
luckperms.logs.export-prefix=ЕКСПОРТ
luckperms.commandsystem.available-commands=Використовуйте {0}, щоб переглянути доступні команди
luckperms.commandsystem.command-not-recognised=Команда нерозпізнана
luckperms.commandsystem.no-permission=У вас немає дозволу на використання цієї команди\!
luckperms.commandsystem.no-permission-subcommands=У вас немає дозволу на використання будь-яких підкоманд
luckperms.commandsystem.already-executing-command=Виконується інша команда, зачекайте на її завершення...
luckperms.commandsystem.usage.sub-commands-header=Підкоманди
luckperms.commandsystem.usage.usage-header=Використання команд
luckperms.commandsystem.usage.arguments-header=Аргументи
luckperms.first-time.no-permissions-setup=Здається, що ніякі дозволи ще не налаштовані\!
luckperms.first-time.use-console-to-give-access=Перед тим як ви зможете використовувати будь-які команди LuckPerms у грі, вам варто використовувати консоль, щоб надати собі доступ
luckperms.first-time.console-command-prompt=Розгорніть свою консоль і запустіть її
luckperms.first-time.next-step=Після того як ви це виконали, можете починати визначати свої права доступу та групи
luckperms.first-time.wiki-prompt=Не знаєте де почати? Перевірте тут {0}
luckperms.login.try-again=Будь ласка, спробуйте пізніше
luckperms.login.loading-database-error=Помилка бази даних сталась під час завантаження даних для доступу
luckperms.login.server-admin-check-console-errors=Якщо ви адміністратор серверу, будь ласка, перевірте консоль на наявність будь-яких помилок
luckperms.login.server-admin-check-console-info=Будь ласка, перевірте серверну консоль для додаткової інформації
luckperms.login.data-not-loaded-at-pre=Дані дозволів для вашого користувача не були завантажені під час етапу попереднього входу
luckperms.login.unable-to-continue=продовжити неможливо
luckperms.login.craftbukkit-offline-mode-error=це, ймовірно, через конфлікт між CraftBukkit й параметром онлайн режиму
luckperms.login.unexpected-error=Сталась неочікувана помилка під час налаштування бази даних дозволів
luckperms.opsystem.disabled=Система ванільних операторів вимкнена на цьому сервері
luckperms.opsystem.sponge-warning=Будь ласка, зверніть увагу, що статус оператора сервера не має впливу на перевірки щодо дозволів Sponge, коли плагін дозволу інстальований, ви повинні відредагувати дані користувача безпосередньо
luckperms.duration.unit.years.plural={0} років
luckperms.duration.unit.years.singular={0} рік
luckperms.duration.unit.years.short={0}р.
luckperms.duration.unit.months.plural={0} місяців
luckperms.duration.unit.months.singular={0} місяць
luckperms.duration.unit.months.short={0}міс.
luckperms.duration.unit.weeks.plural={0} тижнів
luckperms.duration.unit.weeks.singular={0} тиждень
luckperms.duration.unit.weeks.short={0}тижд
luckperms.duration.unit.days.plural={0} днів
luckperms.duration.unit.days.singular={0} день
luckperms.duration.unit.days.short={0}дн
luckperms.duration.unit.hours.plural={0} годин
luckperms.duration.unit.hours.singular={0} година
luckperms.duration.unit.hours.short={0}г
luckperms.duration.unit.minutes.plural={0} хвилин
luckperms.duration.unit.minutes.singular={0} хвилина
luckperms.duration.unit.minutes.short={0}хв
luckperms.duration.unit.seconds.plural={0} секунд
luckperms.duration.unit.seconds.singular={0} секунда
luckperms.duration.unit.seconds.short={0}сек
luckperms.duration.since={0} тому
luckperms.command.misc.invalid-code=Неправильний код
luckperms.command.misc.response-code-key=код відповіді
luckperms.command.misc.error-message-key=повідомлення
luckperms.command.misc.bytebin-unable-to-communicate=Не вдалося зв’язатися з bytebin
luckperms.command.misc.webapp-unable-to-communicate=Не вдалося здійснити обмін інформацією з веб-додатком
luckperms.command.misc.check-console-for-errors=Перевірте консоль на помилки
luckperms.command.misc.file-must-be-in-data=Файл {0} повинен бути в папці бази даних
luckperms.command.misc.wait-to-finish=Зачекайте, будь ласка, поки воно закінчить і спробуйте знову
luckperms.command.misc.invalid-priority=Неправильний пріоритет {0}
luckperms.command.misc.expected-number=Очікувалося ціле число\!
luckperms.command.misc.date-parse-error=Не вдалося розпізнати дату {0}
luckperms.command.misc.date-in-past-error=Ви не можете встановити минулу дату\!
luckperms.command.misc.page=сторінка {0} з {1}
luckperms.command.misc.page-entries={0} записів
luckperms.command.misc.none=Відсутньо
luckperms.command.misc.loading.error.unexpected=Сталась непередбачена помилка
luckperms.command.misc.loading.error.user=Користувач незавантажений
luckperms.command.misc.loading.error.user-specific=Не вдалося завантажити цільового користувача {0}
luckperms.command.misc.loading.error.user-not-found=Користувача для {0} не знайдено
luckperms.command.misc.loading.error.user-save-error=Під час збереження даних для користувача {0} сталася помилка
luckperms.command.misc.loading.error.user-not-online=Користувач {0} не в мережі
luckperms.command.misc.loading.error.user-invalid={0} неправильне ім''я/uid
luckperms.command.misc.loading.error.user-not-uuid=Недійсне uid {0} цільового користувача
luckperms.command.misc.loading.error.group=Групу не завантажено
luckperms.command.misc.loading.error.all-groups=Не вдається завантажити всі групи
luckperms.command.misc.loading.error.group-not-found=Групу на ім''я {0} не знайдено
luckperms.command.misc.loading.error.group-save-error=Сталася помилка під час збереження даних групи для {0}
luckperms.command.misc.loading.error.group-invalid={0} - некоректна назва групи
luckperms.command.misc.loading.error.track=Трек не завантажений
luckperms.command.misc.loading.error.all-tracks=Не вдалося завантажити всі маршрути
luckperms.command.misc.loading.error.track-not-found=Трек з назвою {0} не знайдено
luckperms.command.misc.loading.error.track-save-error=Сталася помилка під час збереження даних маршруту для {0}
luckperms.command.misc.loading.error.track-invalid={0} некоректна назва маршруту
luckperms.command.editor.no-match=Не вдалося відкрити редактор, жоден об''єкт не відповідає вибраному типу
luckperms.command.editor.start=Підготовка нової сесії редактора, будь ласка, зачекайте...
luckperms.command.editor.url=Натисніть на посилання нижче, щоб відкрити редактор
luckperms.command.editor.unable-to-communicate=Неможливо з''єднатися з редактором
luckperms.command.editor.apply-edits.success=Дані веб-редактора застосовані до {0} {1} успішно
luckperms.command.editor.apply-edits.success-summary={0} {1} і {2} {3}
luckperms.command.editor.apply-edits.success.additions=доповнень
luckperms.command.editor.apply-edits.success.additions-singular=доповнення
luckperms.command.editor.apply-edits.success.deletions=вилучень
luckperms.command.editor.apply-edits.success.deletions-singular=вилучення
luckperms.command.editor.apply-edits.no-changes=Не застосовано жодних змін у веб-редакторі, оскільки отримані дані не містили редагувань
luckperms.command.editor.apply-edits.unknown-type=Не вдалося застосувати редагування до вказаного типу об''єкта
luckperms.command.editor.apply-edits.unable-to-read=Ви не можете прочитати базу даних, використовуючи даний код
luckperms.command.search.searching.permission=Пошук користувачів і груп з {0}
luckperms.command.search.searching.inherit=Пошук користувачів і груп з {0}
luckperms.command.search.result=Знайдено {0} записів користувачів {1} і груп {2}
luckperms.command.search.result.default-notice=Примітка\: при пошуку учасників групи за замовчуванням в автономному режимі гравців без будь-яких прав не відображатиметься\!
luckperms.command.search.showing-users=Показано матеріали користувача
luckperms.command.search.showing-groups=Показано матеріали групи
luckperms.command.tree.start=Створення дерева дозволів, зачекайте, будь ласка...
luckperms.command.tree.empty=Не вдалося згенерувати дерево, результатів не знайдено
luckperms.command.tree.url=Посилання на дерево дозволів
luckperms.command.verbose.invalid-filter={0} - некоректний фільтр
luckperms.command.verbose.enabled=Розширений журнал {0} для перевірки {1}
luckperms.command.verbose.command-exec=Примусово {0} виконувати команду {1} і повідомляти про всі перевірки...
luckperms.command.verbose.off=Докладне ведення журналу{0}
luckperms.command.verbose.command-exec-complete=Виконання команди завершено
luckperms.command.verbose.command.no-checks=Команда виконана, але ніяких перевірок дозволів не зроблені
luckperms.command.verbose.command.possibly-async=Це може бути через те що плагін запускає команди у фоновому режимі (async)
luckperms.command.verbose.command.try-again-manually=Ви досі можете використовувати детальну інформацію вручну для визначення перевірок, зроблених як ця
luckperms.command.verbose.enabled-recording=Розширений журнал {0} для перевірки {1}
luckperms.command.verbose.uploading=Тривале журналювання {0}, завантаження результатів...
luckperms.command.verbose.url=Докладна інформація про результати завантаження URL
luckperms.command.verbose.enabled-term=увімкнено
luckperms.command.verbose.disabled-term=вимкнено
luckperms.command.verbose.query-any=БУДЬ-ЯКИЙ
luckperms.command.info.running-plugin=Запущено
luckperms.command.info.platform-key=Платформа
luckperms.command.info.server-brand-key=Тип сервера
luckperms.command.info.server-version-key=Версія сервера
luckperms.command.info.storage-key=Сховище
luckperms.command.info.storage-type-key=Тип
luckperms.command.info.storage.meta.split-types-key=Типи
luckperms.command.info.storage.meta.ping-key=Пінг
luckperms.command.info.storage.meta.connected-key=З''єднано
luckperms.command.info.storage.meta.file-size-key=Розмір файлу
luckperms.command.info.extensions-key=Розширення
luckperms.command.info.messaging-key=Обмін повідомленнями
luckperms.command.info.instance-key=Зразок
luckperms.command.info.static-contexts-key=Статичний контекст
luckperms.command.info.online-players-key=Гравці онлайн
luckperms.command.info.online-players-unique={0} унікальний
luckperms.command.info.uptime-key=Час роботи
luckperms.command.info.local-data-key=Локальні дані
luckperms.command.info.local-data={0} користувачів, {1} груп – {2} доріжок
luckperms.command.generic.create.success={0} - успішно створено
luckperms.command.generic.create.error=Сталася помилка при створенні {0}
luckperms.command.generic.create.error-already-exists=''{0}'' уже існує\!
luckperms.command.generic.delete.success={0} успішно видалено
luckperms.command.generic.delete.error=Сталася помилка при видаленні {0}
luckperms.command.generic.delete.error-doesnt-exist={0} не існує\!
luckperms.command.generic.rename.success={0} успішно перейменовано на {1}
luckperms.command.generic.clone.success={0} успішно клоновано в {1}
luckperms.command.generic.info.parent.title=Батьківські групи
luckperms.command.generic.info.parent.temporary-title=Тимчасові батьківські групи
luckperms.command.generic.info.expires-in=завершується
luckperms.command.generic.info.inherited-from=успадкований від
luckperms.command.generic.info.inherited-from-self=самого себе
luckperms.command.generic.show-tracks.title={0} треки
luckperms.command.generic.show-tracks.empty={0} не містить жодних маршрутів
luckperms.command.generic.clear.node-removed={0} записи видалені
luckperms.command.generic.clear.node-removed-singular={0} запис видалений
luckperms.command.generic.clear={0} Записи в контексті{1} очищені
luckperms.command.generic.permission.info.title={0} дозволи
luckperms.command.generic.permission.info.empty={0} не має встановлених дозволів
luckperms.command.generic.permission.info.click-to-remove=Натисніть, щоб вилучити цей дозвіл із {0}
luckperms.command.generic.permission.check.info.title=Інформація про дозвіл для {0}
luckperms.command.generic.permission.check.info.directly={0} має {1} з установленим значенням {2} у контексті {3}
luckperms.command.generic.permission.check.info.inherited={0} наслідує {1} з установленим значенням {2} від {3} у контексті {4}
luckperms.command.generic.permission.check.info.not-directly={0} не має встановленого {1}
luckperms.command.generic.permission.check.info.not-inherited={0} не наслідує від {1}
luckperms.command.generic.permission.check.result.title=Перевірка дозволу {0}
luckperms.command.generic.permission.check.result.result-key=Результат
luckperms.command.generic.permission.check.result.processor-key=Процесор
luckperms.command.generic.permission.check.result.cause-key=Причина
luckperms.command.generic.permission.check.result.context-key=Зміст
luckperms.command.generic.permission.set=Установити {0} в {1} для {2} у контексті {3}
luckperms.command.generic.permission.already-has={0} уже має дозвіл {1}, встановлений у контексті {2}
luckperms.command.generic.permission.set-temp=Встановлено право {0} зі значенням {1} для {2} з тривалістю {3} у контексті {4}
luckperms.command.generic.permission.already-has-temp={0} уже має тимчасово встановлений дозвіл {1} у контексті {2}
luckperms.command.generic.permission.unset=Скасувати {0} для {1} у контексті {2}
luckperms.command.generic.permission.doesnt-have={0} не має {1}, установленого в контексті {2}
luckperms.command.generic.permission.unset-temp=Скасувати тимчасовий дозвіл {0} для {1} у контексті {2}
luckperms.command.generic.permission.subtract=Установлює {0} в {1} для {2} тривалістю {3} у контексті {4}, на {5} менше, ніж раніше
luckperms.command.generic.permission.doesnt-have-temp={0} не має {1}, тимчасово встановлене в контексті {2}
luckperms.command.generic.permission.clear=Дозволи {0} у контексті {1} очищено
luckperms.command.generic.parent.info.title=Батьки {0}
luckperms.command.generic.parent.info.empty={0} не має визначених батьків
luckperms.command.generic.parent.info.click-to-remove=Натисніть, щоб вилучити цей батьківський елемент з {0}
luckperms.command.generic.parent.add={0} тепер наслідує дозволи від {1} у контексті {2}
luckperms.command.generic.parent.add-temp={0} тепер успадковує дозволи від {1} на тривалість {2} в контексті {3}
luckperms.command.generic.parent.set=Раніше встановлені батьківські групи {0} очищені й тепер цей користувач наслідує лише {1} у контексті {2}
luckperms.command.generic.parent.set-track=Раніше установлені батьківські групи {0} у треку {1} очищені й тепер цей користувач наслідує лише {2} у контексті {3}
luckperms.command.generic.parent.remove={0} більше не наслідує дозволи від {1} у контексті {2}
luckperms.command.generic.parent.remove-temp={0} тимчасово не наслідує дозволи від {1} у контексті {2}
luckperms.command.generic.parent.subtract={0} наслідуватиме дозволи від {1} на тривалість {2} у контексті {3} терміном на {4} менше, ніж раніше
luckperms.command.generic.parent.clear=Батьківські групи {0} в контексті {1} очищено
luckperms.command.generic.parent.clear-track=Батьківські групи {0} у треку {1} у контексті {2} очищено
luckperms.command.generic.parent.already-inherits={0} уже наслідує від {1} у контексті {2}
luckperms.command.generic.parent.doesnt-inherit={0} не наслідує від {1} у контексті {2}
luckperms.command.generic.parent.already-temp-inherits={0} уже тимчасово наслідує від {1} у контексті {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} тимчасово не наслідує від {1} у контексті {2}
luckperms.command.generic.chat-meta.info.title-prefix=Префікси {0}
luckperms.command.generic.chat-meta.info.title-suffix=Суфікси {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} не має префіксів
luckperms.command.generic.chat-meta.info.none-suffix={0} не має суфіксів
luckperms.command.generic.chat-meta.info.click-to-remove=Натисніть, щоб вилучити {0} з {1}
luckperms.command.generic.chat-meta.already-has={0} уже має {1} {2} встановлену з пріоритетом {3} у контексті {4}
luckperms.command.generic.chat-meta.already-has-temp={0} уже має {1} {2} тимчасово встановлену з пріоритетом {3} у контексті {4}
luckperms.command.generic.chat-meta.doesnt-have={0} не має {1} {2} встановлену з пріоритетом {3} у контексті {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} уже має {1} {2} тимчасово встановлену з пріоритетом {3} у контексті {4}
luckperms.command.generic.chat-meta.add={0} тепер має {1} {2} встановлений з пріоритетом {3} у контексті {4}
luckperms.command.generic.chat-meta.add-temp={0} тепер має {1} {2}, тимчасово встановлений з пріоритетом {3} на тривалість {4} у контексті {5}
luckperms.command.generic.chat-meta.remove={0} тепер не має {1} {2}, який був встановлений з пріоритетом {3} у контексті {4}
luckperms.command.generic.chat-meta.remove-bulk={0} тепер не має все {1}, які були встановлені з пріоритетом {2} у контексті {3}
luckperms.command.generic.chat-meta.remove-temp={0} тимчасово мав {1} {2} у пріоритеті {3}, вилучені в контексті {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} мав усе тимчасово {1}, що було встановлено з пріоритетом {2} у контексті {3}
luckperms.command.generic.meta.info.title=Метадані {0}
luckperms.command.generic.meta.info.none={0} не має метаданих
luckperms.command.generic.meta.info.click-to-remove=Натисніть, щоб видалити ці метадані з групи {0}
luckperms.command.generic.meta.already-has=Група {0} уже має метаключ {1} з установленим значенням {2} у контексті {3}
luckperms.command.generic.meta.already-has-temp={0} уже має метаключ {1} з тимчасово встановленим значенням {2} у контексті {3}
luckperms.command.generic.meta.doesnt-have={0} не має метаключа{1}, установленого в контексті {2}
luckperms.command.generic.meta.doesnt-have-temp={0} не має метаключа {1} тимчасово встановленого в контексті {2}
luckperms.command.generic.meta.set=Установити метаключ {0} зі значенням {1} для {2} у контексті {3}
luckperms.command.generic.meta.set-temp=Установити метаключ {0} зі значенням {1} для {2} тривалістю на {3} у контексті {4}
luckperms.command.generic.meta.unset=Зняти метаключ {0} зі значенням {1} у контексті {2}
luckperms.command.generic.meta.unset-temp=Зняти тимчасовий метаключ {0} зі значенням {1} у контексті {2}
luckperms.command.generic.meta.clear=Метадата для {0} відповідного типу {1} очищена в контексті {2}
luckperms.command.generic.contextual-data.title=Контекстуальні дані
luckperms.command.generic.contextual-data.mode.key=режим
luckperms.command.generic.contextual-data.mode.server=сервер
luckperms.command.generic.contextual-data.mode.active-player=активний гравець
luckperms.command.generic.contextual-data.contexts-key=Контексти
luckperms.command.generic.contextual-data.prefix-key=Префікс
luckperms.command.generic.contextual-data.suffix-key=Суфікс
luckperms.command.generic.contextual-data.primary-group-key=Первинна група
luckperms.command.generic.contextual-data.meta-key=Мета
luckperms.command.generic.contextual-data.null-result=Жодного
luckperms.command.user.info.title=Інформація про користувача
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=тип
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=офлайн
luckperms.command.user.info.status-key=Статус
luckperms.command.user.info.status.online=Онлайн
luckperms.command.user.info.status.offline=Офлайн
luckperms.command.user.removegroup.error-primary=Ви не можете вилучити користувача з первинної групи
luckperms.command.user.primarygroup.not-member={0} доданий у трек {1}, оскільки раніше цей користувач не був пов''язаний з ним
luckperms.command.user.primarygroup.already-has={0} уже має групу {1}, установлену в ролі головної
luckperms.command.user.primarygroup.warn-option=Попередження\: Метод підрахунку користувачів з основної групи, використаний на сервері ({0}), може не показувати наведені зміни
luckperms.command.user.primarygroup.set=Основна група {0} установлена на групу {1}
luckperms.command.user.track.error-not-contain-group={0} ще не має жодної групи, яка розташована в треці {1}
luckperms.command.user.track.unsure-which-track=На жаль, не вдалося вибрати необхідний трек для використання, будь ласка, укажіть його назву в ролі аргументу
luckperms.command.user.track.missing-group-advice=Або створіть групу, або видаляйте її з треку й спробуйте знову
luckperms.command.user.promote.added-to-first={0} доданий в групу {2} у контексті {3}, оскільки користувач не мав груп, які розташовані в треку {1}
luckperms.command.user.promote.not-on-track=Не вдалося підвищити групу {0}, оскільки цей користувач не має груп, які розташовані в треку {1}
luckperms.command.user.promote.success=Підвищення групи {0} в треці {1} з {2} до {3} у контексті {4}
luckperms.command.user.promote.end-of-track=Досягнуто кінця треку {0}, тому не вдалося підвищити групу {1}
luckperms.command.user.promote.next-group-deleted=Наступна група в треці, {0}, більше не існує
luckperms.command.user.promote.unable-to-promote=Не вдалося підвищити групу користувача
luckperms.command.user.demote.success=Зниження групи {0} у треці {1} з {2} до {3} у контексті {4}
luckperms.command.user.demote.end-of-track=Кінець маршруту досягнено {0}, тому {1} вилучено з {2}
luckperms.command.user.demote.end-of-track-not-removed=Досягнуто кінця маршруту {0}, але {1} не вилучено з першої групи
luckperms.command.user.demote.previous-group-deleted=Попередньої групи на маршруті {0} більше не існує
luckperms.command.user.demote.unable-to-demote=Не вдалося знизити групу користувача
luckperms.command.group.list.title=Групи
luckperms.command.group.delete.not-default=Не можна видалити групу за замовчуванням
luckperms.command.group.info.title=Інформація про групу
luckperms.command.group.info.display-name-key=Ім''я
luckperms.command.group.info.weight-key=Вага
luckperms.command.group.setweight.set=Встановити вагу {0} для групи {1}
luckperms.command.group.setdisplayname.doesnt-have={0} не має набору імен
luckperms.command.group.setdisplayname.already-has={0} уже має ім''я {1}
luckperms.command.group.setdisplayname.already-in-use=Ім''я {0} уже використовується групою {1}
luckperms.command.group.setdisplayname.set=Установлено ім''я {0} для групи {1} у контексті {2}
luckperms.command.group.setdisplayname.removed=Ім''я для групи {0} у контексті {1} вилучено
luckperms.command.track.list.title=Треки
luckperms.command.track.path.empty=Жодного
luckperms.command.track.info.showing-track=Відображання треку
luckperms.command.track.info.path-property=Шлях
luckperms.command.track.clear=Всі групи треку {0} очищено
luckperms.command.track.append.success=Групу {0} додано для відстеження {1}
luckperms.command.track.insert.success=Групу {0} додано в трек {1} з позицією {2}
luckperms.command.track.insert.error-number=Для задання позиції потрібно вказати число, але натомість уведено\: {0}
luckperms.command.track.insert.error-invalid-pos=Не вдалося додати в позицію {0}
luckperms.command.track.insert.error-invalid-pos-reason=неправильний номер позиції
luckperms.command.track.remove.success=Групу {0} вилучено з треку {1}
luckperms.command.track.error-empty=Трек {0} не може використовуватися, оскільки він порожній або складає лише одну групу
luckperms.command.track.error-multiple-groups={0} є учасником кількох груп на цьому треку
luckperms.command.track.error-ambiguous=Не вдалося визначити місце розташування
luckperms.command.track.already-contains=Група {1} уже є в треку {0}
luckperms.command.track.doesnt-contain={0} не включає {1}
luckperms.command.log.load-error=Не вдалося завантажити лог
luckperms.command.log.invalid-page=Неправильний номер сторінки
luckperms.command.log.invalid-page-range=Будь ласка, укажіть значення від {0} до {1}
luckperms.command.log.empty=Нема записів логу для показу
luckperms.command.log.notify.error-console=Не можна змінити налаштування сповіщень для консолі
luckperms.command.log.notify.enabled-term=Увімкнені
luckperms.command.log.notify.disabled-term=Вимкнені
luckperms.command.log.notify.changed-state=Лог-сповіщення {0}
luckperms.command.log.notify.already-on=Отримання лог-сповіщень уже ввімкнено
luckperms.command.log.notify.already-off=Отримання сповіщень уже вимкнено
luckperms.command.log.notify.invalid-state=Неправильне значення, тому виберіть {0} або {1}
luckperms.command.log.show.search=Показ останніх дій за запитом {0}
luckperms.command.log.show.recent=Показ останніх дій
luckperms.command.log.show.by=Показ останніх дій {0}
luckperms.command.log.show.history=Показ історії дій для {0} {1}
luckperms.command.export.error-term=Помилка
luckperms.command.export.already-running=На цей момент виконується інший процес експортування
luckperms.command.export.file.already-exists=Файл з ім''ям {0} уже існує
luckperms.command.export.file.not-writable=Файл {0} недоступний для запису
luckperms.command.export.file.success=Успішно експортовано у {0}
luckperms.command.export.file-unexpected-error-writing=Трапилася неочікувана помилка при записі файлу
luckperms.command.export.web.export-code=Код для експорту
luckperms.command.export.web.import-command-description=Використайте наступну команду для імпорту
luckperms.command.import.term=Імпортувати
luckperms.command.import.error-term=Помилка
luckperms.command.import.already-running=На цей час виконується інший процес імпортування
luckperms.command.import.file.doesnt-exist=Файлу {0} не існує
luckperms.command.import.file.not-readable=Файл {0} недоступний для читання
luckperms.command.import.file.unexpected-error-reading=Трапилася неочікувана помилка під час читання даних із файлу імпорту
luckperms.command.import.file.correct-format=чи правильний формат?
luckperms.command.import.web.unable-to-read=Не вдалося отримати інформацію з використанням цього коду
luckperms.command.import.progress.percent={0}% завершено
luckperms.command.import.progress.operations={0}/{1} операцій завершено
luckperms.command.import.starting=Запуск процесу імпорту
luckperms.command.import.completed=ЗАВЕРШЕНО
luckperms.command.import.duration=зайняло {0} секунд
luckperms.command.bulkupdate.must-use-console=Виконання команди масового оновлення можливе лише з консолі
luckperms.command.bulkupdate.invalid-data-type=Неправильний тип, оскільки очікувалося {0}
luckperms.command.bulkupdate.invalid-constraint=Неправильне обмеження {0}
luckperms.command.bulkupdate.invalid-constraint-format=Обмеження повинні бути у форматі {0}
luckperms.command.bulkupdate.invalid-comparison=Неправильний оператор порівняння {0}
luckperms.command.bulkupdate.invalid-comparison-format=Очікувалося одне з наведених\: {0}
luckperms.command.bulkupdate.queued=Операція масового оновлення додана в чергу
luckperms.command.bulkupdate.confirm=Введіть {0}, щоб виконати оновлення
luckperms.command.bulkupdate.unknown-id=Операції з ID {0} не існує або її відлік очікування вичерпався
luckperms.command.bulkupdate.starting=Виконання масового оновлення
luckperms.command.bulkupdate.success=Процес масового оновлення успішно завершений
luckperms.command.bulkupdate.success.statistics.nodes=Зачеплених дозволів усього
luckperms.command.bulkupdate.success.statistics.users=Усього зачеплених користувачів
luckperms.command.bulkupdate.success.statistics.groups=Усього зачеплених груп
luckperms.command.bulkupdate.failure=На жаль, не вдалося виконати масове оновлення, перевірте консоль на наявність помилок
luckperms.command.update-task.request=Запит на оновлення прийнято, будь ласка, зачекайте
luckperms.command.update-task.complete=Оновлення даних завершено
luckperms.command.update-task.push.attempting=Виконуємо спробу надсилання змін на інші сервери
luckperms.command.update-task.push.complete=Інші сервери успішно отримали сповіщення через {0}
luckperms.command.update-task.push.error=Сталася помилка під час надсилання змін на інші сервери
luckperms.command.update-task.push.error-not-setup=Не вдалося надіслати зміни на інші сервери, оскільки розділ "messaging service" не налаштований
luckperms.command.reload-config.success=Файл конфігурації перезавантажено
luckperms.command.reload-config.restart-note=деякі налаштування будуть застосовані лише після перезавантаження серверу
luckperms.command.translations.searching=Пошук доступних перекладів, будь ласка, зачекайте...
luckperms.command.translations.searching-error=На жаль, не вдалося отримати список доступних перекладів
luckperms.command.translations.installed-translations=Встановлені переклади
luckperms.command.translations.available-translations=Доступні переклади
luckperms.command.translations.percent-translated={0}% перекладено
luckperms.command.translations.translations-by=⠀
luckperms.command.translations.installing=Встановлення перекладів, будь ласка, зачекайте...
luckperms.command.translations.download-error=Не вдалося завантажити переклад для {0}
luckperms.command.translations.installing-specific=Встановлення мови {0}...
luckperms.command.translations.install-complete=Установку завершено
luckperms.command.translations.download-prompt=Використайте {0}, щоб завантажити та інсталювати актуальну версію перекладів, наданих спільнотою
luckperms.command.translations.download-override-warning=Будь ласка, зверніть увагу, що всі зміни, внесені вами для цих мов, будуть перевизначені
luckperms.usage.user.description=Набір команд для керування користувачами в LuckPerms (користувач в LuckPerms - це гравець, що може посилатися на UUID або на ім''я користувача)
luckperms.usage.group.description=Набір команд для керування групами в LuckPerms. Групи - це згруповані дозволи, які можуть бути видані користувачам. Нові групи можуть бути створені за допомогою команди "creategroup".
luckperms.usage.track.description=Набір команд для керування треками в межах LuckPerms. Треки - це упорядкована послідовність груп, які використовуються для підвищення чи зниження групи.
luckperms.usage.log.description=Набір команд для керування функціями логування в LuckPerms.
luckperms.usage.sync.description=Перезавантажує всі дані із плагінів у пам''ять і застосовує будь-які зміни, які виявлено.
luckperms.usage.info.description=Друкує основну інформацію про активний екземпляр плагіна.
luckperms.usage.editor.description=Створює нову сесію веб-редактора
luckperms.usage.editor.argument.type=типи для завантаження в редактор. (''all'', ''users'' або ''groups'')
luckperms.usage.editor.argument.filter=дозвіл на фільтрування записів користувача
luckperms.usage.verbose.description=Керування системою моніторингу дозволів плагінів.
luckperms.usage.verbose.argument.action=щоб увімкнути/вимкнути логування чи завантажити
luckperms.usage.verbose.argument.filter=фільтр, що відповідає записам
luckperms.usage.verbose.argument.commandas=гравець/команда для виконання
luckperms.usage.tree.description=Генерує вид дерева (упорядкована ієрархія списків) усіх дозволів у LuckPerms.
luckperms.usage.tree.argument.scope=кореневий каталог дерева. Укажіть "." щоб вибрати всі дозволи
luckperms.usage.tree.argument.player=ім''я гравця для перевірки
luckperms.usage.search.description=Пошук усіх користувачів/груп з певним дозволом
luckperms.usage.search.argument.permission=дозвіл на пошук
luckperms.usage.search.argument.page=сторінка для перегляду
luckperms.usage.network-sync.description=Синхронізувати зміни зі сховищем і надіслати запит іншим серверам у мережі робити те саме
luckperms.usage.import.description=Імпортує дані з (попередньо створеного) файлу експорту
luckperms.usage.import.argument.file=файл, з якого необхідно виконати імпорт
luckperms.usage.import.argument.replace=замінити наявні дані замість їхнього об''єднання
luckperms.usage.import.argument.upload=вивантажити дані з наявного файлу експорту
luckperms.usage.export.description=Експортувати дані дозволів у спеціальний файл для експорту. Пізніше завдяки ньому ви зможете відновити дані, імпортувавши його.
luckperms.usage.export.argument.file=файл, у який необхідно виконати експорт
luckperms.usage.export.argument.without-users=не експортувати даних користувачів
luckperms.usage.export.argument.without-groups=не експортувати дані групи
luckperms.usage.export.argument.upload=Вивантажити всі дані про дозволи на вебредактор. Пізніше дані можна буде імпортувати.
luckperms.usage.reload-config.description=Перезавантажити деякі параметри конфігурації
luckperms.usage.bulk-update.description=Виконати запити масового оновлення для всіх даних
luckperms.usage.bulk-update.argument.data-type=тип даних, що змінюється (''all'', ''users'' або ''groups'')
luckperms.usage.bulk-update.argument.action=дія, яку можна виконати над даними (''update'' чи ''delete'')
luckperms.usage.bulk-update.argument.action-field=поле вибору дії. Потрібне лише для опції ''update'' (''permission'', ''server'' чи ''world'')
luckperms.usage.bulk-update.argument.action-value=значення для заміни. Вимагається лише для опції ''update''.
luckperms.usage.bulk-update.argument.constraint=обмеження, необхідні для оновлення
luckperms.usage.translations.description=Керування перекладами
luckperms.usage.translations.argument.install=субкоманда для встановлення перекладів
luckperms.usage.apply-edits.description=Застосовує зміни в дозволах, виконані у вебредакторі
luckperms.usage.apply-edits.argument.code=унікальний код для застосування змін
luckperms.usage.apply-edits.argument.target=хто застосовує зміни даних
luckperms.usage.create-group.description=Створити нову групу
luckperms.usage.create-group.argument.name=ім''я групи
luckperms.usage.create-group.argument.weight=вага групи
luckperms.usage.create-group.argument.display-name=відображена назва групи
luckperms.usage.delete-group.description=Видалити групу
luckperms.usage.delete-group.argument.name=назва групи
luckperms.usage.list-groups.description=Список усіх груп на платформі
luckperms.usage.create-track.description=Створити новий трек
luckperms.usage.create-track.argument.name=ім''я треку
luckperms.usage.delete-track.description=Вилучити трек
luckperms.usage.delete-track.argument.name=ім''я треку
luckperms.usage.list-tracks.description=Список усіх треків на платформі
luckperms.usage.user-info.description=Показує інформацію про користувача
luckperms.usage.user-switchprimarygroup.description=Змінює основну групу користувача
luckperms.usage.user-switchprimarygroup.argument.group=група, яку потрібно замінити на наявну
luckperms.usage.user-promote.description=Просуває групу користувача далі по треку
luckperms.usage.user-promote.argument.track=трек для просування групи користувача
luckperms.usage.user-promote.argument.context=контексти, у яких повинно бути виконано просування групи користувача
luckperms.usage.user-promote.argument.dont-add-to-first=просуває групу користувача, якщо він уже пов''язаний з треком
luckperms.usage.user-demote.description=Знижує групу користувача на попередню в треці
luckperms.usage.user-demote.argument.track=трек для зниження групи користувача
luckperms.usage.user-demote.argument.context=контексти, у яких повинно бути виконано зниження групи користувача
luckperms.usage.user-demote.argument.dont-remove-from-first=запобігти вилученню користувача з першої групи треку при її зниженні
luckperms.usage.user-clone.description=Скопіювати користувача
luckperms.usage.user-clone.argument.user=ім''я/uuid користувача, щоб скопіювати його
luckperms.usage.group-info.description=Надає інформацію про групу
luckperms.usage.group-listmembers.description=Показувати користувачів/групи, що наслідують дозволи від цієї групи
luckperms.usage.group-listmembers.argument.page=сторінка для перегляду
luckperms.usage.group-setweight.description=Установити вагу групи
luckperms.usage.group-setweight.argument.weight=вагу, яку необхідно встановити 
luckperms.usage.group-set-display-name.description=Установити відображуване ім''я групи
luckperms.usage.group-set-display-name.argument.name=ім''я для встановлення
luckperms.usage.group-set-display-name.argument.context=контексти, в яких потрібно встановити ім''я
luckperms.usage.group-rename.description=Перейменувати групу
luckperms.usage.group-rename.argument.name=нове ім''я
luckperms.usage.group-clone.description=Клонує групу
luckperms.usage.group-clone.argument.name=ім''я клонованої групи на
luckperms.usage.holder-editor.description=Відкриває вебредактор дозволів
luckperms.usage.holder-showtracks.description=Показує список треків, у яких знаходиться об''єкт
luckperms.usage.holder-clear.description=Вилучає всі дозволи, батьківські групи й мета
luckperms.usage.holder-clear.argument.context=контексти для фільтрації
luckperms.usage.permission.description=Редагувати дозволи
luckperms.usage.parent.description=Редагувати успадкування
luckperms.usage.meta.description=Редагувати значення метаданих
luckperms.usage.permission-info.description=Показує список дозволів, які має об''єкт
luckperms.usage.permission-info.argument.page=сторінка для перегляду
luckperms.usage.permission-info.argument.sort-mode=як сортувати записи
luckperms.usage.permission-set.description=Установлює дозвіл для об''єкта
luckperms.usage.permission-set.argument.node=дозволи для встановлення
luckperms.usage.permission-set.argument.value=значення дозволу
luckperms.usage.permission-set.argument.context=контексти, у які потрібно додати дозвіл
luckperms.usage.permission-unset.description=Скасовує дозволи в користувача
luckperms.usage.permission-unset.argument.node=дозволи, які необхідно скасувати
luckperms.usage.permission-unset.argument.context=контексти, у яких необхідно вилучити дозвіл
luckperms.usage.permission-settemp.description=Тимчасово встановлює дозвіл для об''єкта
luckperms.usage.permission-settemp.argument.node=дозвіл на встановлення
luckperms.usage.permission-settemp.argument.value=значення дозволу
luckperms.usage.permission-settemp.argument.duration=час, протягом якого діятиме дозвіл
luckperms.usage.permission-settemp.argument.temporary-modifier=як тимчасовий дозвіл повинен бути застосованим
luckperms.usage.permission-settemp.argument.context=контексти, у які потрібно додати дозвіл
luckperms.usage.permission-unsettemp.description=Скасувати тимчасовий дозвіл для об''єкта
luckperms.usage.permission-unsettemp.argument.node=дозволи, які необхідно скасувати
luckperms.usage.permission-unsettemp.argument.duration=тривалість віднімання
luckperms.usage.permission-unsettemp.argument.context=контексти, у яких необхідно вилучити дозвіл
luckperms.usage.permission-check.description=Перевіряє, чи в об''єкта є певний дозвіл
luckperms.usage.permission-check.argument.node=дозвіл для перевірки
luckperms.usage.permission-clear.description=Вилучає всі дозволи
luckperms.usage.permission-clear.argument.context=контексти для фільтрації
luckperms.usage.parent-info.description=Показує список груп, які наслідують цей об''єкт
luckperms.usage.parent-info.argument.page=сторінка для перегляду
luckperms.usage.parent-info.argument.sort-mode=як сортувати записи
luckperms.usage.parent-set.description=Вилучає всі батьківські групи, які вже наслідує користувач, і додає вказану
luckperms.usage.parent-set.argument.group=назва групи для встановлення
luckperms.usage.parent-set.argument.context=контексти, у яких повинна бути встановлена група
luckperms.usage.parent-add.description=Додає групу користувачу для наслідування дозволів від неї
luckperms.usage.parent-add.argument.group=група для наслідування
luckperms.usage.parent-add.argument.context=контексти, у яких повинна наслідуватися група
luckperms.usage.parent-remove.description=Вилучає раніше встановлені наслідування групи
luckperms.usage.parent-remove.argument.group=група для вилучення
luckperms.usage.parent-remove.argument.context=контексти, у яких група повинна бути вилученою
luckperms.usage.parent-set-track.description=Вилучає всі групи, які вже наслідує користувач у вказаному треці, і додає вибрану
luckperms.usage.parent-set-track.argument.track=трек для встановлення
luckperms.usage.parent-set-track.argument.group=назва групи чи номер розташування групи в даному треці для встановлення
luckperms.usage.parent-set-track.argument.context=контексти, у яких повинна бути встановлена група
luckperms.usage.parent-add-temp.description=Тимчасово додає групу користувачу для наслідування дозволів від неї
luckperms.usage.parent-add-temp.argument.group=група для наслідування
luckperms.usage.parent-add-temp.argument.duration=тривалість членства в групі
luckperms.usage.parent-add-temp.argument.temporary-modifier=як варто застосовувати тимчасовий дозвіл
luckperms.usage.parent-add-temp.argument.context=контексти, у яких повинна наслідуватися група
luckperms.usage.parent-remove-temp.description=Вилучає раніше встановлені наслідування групи
luckperms.usage.parent-remove-temp.argument.group=група для вилучення
luckperms.usage.parent-remove-temp.argument.duration=тривалість віднімання
luckperms.usage.parent-remove-temp.argument.context=контексти, у яких група повинна бути вилученою
luckperms.usage.parent-clear.description=Очищає всі батьківські групи
luckperms.usage.parent-clear.argument.context=контексти для фільтрації
luckperms.usage.parent-clear-track.description=Очищає всі батьківські групи, які знаходяться в цьому треці
luckperms.usage.parent-clear-track.argument.track=трек для вилучення
luckperms.usage.parent-clear-track.argument.context=контексти для фільтрації
luckperms.usage.meta-info.description=Показує всі метадані для чату
luckperms.usage.meta-set.description=Установлює значення для мета
luckperms.usage.meta-set.argument.key=ключ для встановлення
luckperms.usage.meta-set.argument.value=значення для встановлення
luckperms.usage.meta-set.argument.context=контексти, у яких повинна бути додана мета
luckperms.usage.meta-unset.description=Скасовує значення мета
luckperms.usage.meta-unset.argument.key=ключ для зняття
luckperms.usage.meta-unset.argument.context=контексти, у яких повинна бути видалена мета
luckperms.usage.meta-settemp.description=Тимчасово встановлює значення мета
luckperms.usage.meta-settemp.argument.key=ключ для встановлення
luckperms.usage.meta-settemp.argument.value=значення для встановлення
luckperms.usage.meta-settemp.argument.duration=проміжок часу після якого тривалість метаданих завершиться
luckperms.usage.meta-settemp.argument.context=контексти, у яких повинна бути додана мета
luckperms.usage.meta-unsettemp.description=Скидає тимчасове значення мета
luckperms.usage.meta-unsettemp.argument.key=ключ для скидання
luckperms.usage.meta-unsettemp.argument.context=контексти, у яких повинна бути вилучена мета
luckperms.usage.meta-addprefix.description=Додає префікс
luckperms.usage.meta-addprefix.argument.priority=пріоритет, у якому повинен бути доданий префікс
luckperms.usage.meta-addprefix.argument.prefix=префікс
luckperms.usage.meta-addprefix.argument.context=контексти, у яких повинен бути доданий префікс
luckperms.usage.meta-addsuffix.description=Додає суфікс
luckperms.usage.meta-addsuffix.argument.priority=пріоритет, у якому повинен бути доданий суфікс
luckperms.usage.meta-addsuffix.argument.suffix=суфікс
luckperms.usage.meta-addsuffix.argument.context=контексти, у яких повинен бути доданий суфікс
luckperms.usage.meta-setprefix.description=Установлює префікс
luckperms.usage.meta-setprefix.argument.priority=пріоритет, у якому повинен бути встановленим префікс
luckperms.usage.meta-setprefix.argument.prefix=префікс
luckperms.usage.meta-setprefix.argument.context=контексти, у яких повинен бути встановлений префікс
luckperms.usage.meta-setsuffix.description=Установлює суфікс
luckperms.usage.meta-setsuffix.argument.priority=пріоритет, у якому повинен бути встановлений суфікс
luckperms.usage.meta-setsuffix.argument.suffix=суфікс
luckperms.usage.meta-setsuffix.argument.context=контексти, у яких повинен бути встановлений суфікс
luckperms.usage.meta-removeprefix.description=Вилучає префікс
luckperms.usage.meta-removeprefix.argument.priority=пріоритет видалення префіксу
luckperms.usage.meta-removeprefix.argument.prefix=префікс
luckperms.usage.meta-removeprefix.argument.context=контексти, у яких повинен бути вилучений префікс
luckperms.usage.meta-removesuffix.description=Вилучає суфікс
luckperms.usage.meta-removesuffix.argument.priority=пріоритет вилучення суфікса
luckperms.usage.meta-removesuffix.argument.suffix=суфікс
luckperms.usage.meta-removesuffix.argument.context=контексти, у яких повинен бути вилученим суфікс
luckperms.usage.meta-addtemp-prefix.description=Додає тимчасовий префікс
luckperms.usage.meta-addtemp-prefix.argument.priority=пріоритет, з яким повинен бути доданим префікс
luckperms.usage.meta-addtemp-prefix.argument.prefix=префікс
luckperms.usage.meta-addtemp-prefix.argument.duration=тривалість, протягом якої діятиме префікс
luckperms.usage.meta-addtemp-prefix.argument.context=контексти, у яких повинен бути доданим префікс
luckperms.usage.meta-addtemp-suffix.description=Додає тимчасовий суфікс
luckperms.usage.meta-addtemp-suffix.argument.priority=пріоритет, у якому повинен бути доданий суфікс
luckperms.usage.meta-addtemp-suffix.argument.suffix=суфікс
luckperms.usage.meta-addtemp-suffix.argument.duration=тривалість, протягом якої діятиме суфікс
luckperms.usage.meta-addtemp-suffix.argument.context=контексти, у яких повинен бути доданим суфікс
luckperms.usage.meta-settemp-prefix.description=Установлює тимчасовий префікс
luckperms.usage.meta-settemp-prefix.argument.priority=пріоритет, у якому повинен бути встановленим префікс
luckperms.usage.meta-settemp-prefix.argument.prefix=префікс
luckperms.usage.meta-settemp-prefix.argument.duration=тривалість, протягом якої діятиме префікс
luckperms.usage.meta-settemp-prefix.argument.context=контексти, у яких повинен бути встановленим префікс
luckperms.usage.meta-settemp-suffix.description=Установлює тимчасовий суфікс
luckperms.usage.meta-settemp-suffix.argument.priority=пріоритет, у якому повинен бути встановленим суфікс
luckperms.usage.meta-settemp-suffix.argument.suffix=суфікс
luckperms.usage.meta-settemp-suffix.argument.duration=тривалість, протягом якої діятиме суфікс
luckperms.usage.meta-settemp-suffix.argument.context=контексти, у яких повинен бути встановленим суфікс
luckperms.usage.meta-removetemp-prefix.description=Вилучає тимчасовий префікс
luckperms.usage.meta-removetemp-prefix.argument.priority=пріоритет, який має можливість вилучення префіксу
luckperms.usage.meta-removetemp-prefix.argument.prefix=префікс
luckperms.usage.meta-removetemp-prefix.argument.context=контексти, у яких повинен бути видаленим префікс
luckperms.usage.meta-removetemp-suffix.description=Вилучає тимчасовий суфікс
luckperms.usage.meta-removetemp-suffix.argument.priority=пріоритет, який має можливість вилучення суфікса
luckperms.usage.meta-removetemp-suffix.argument.suffix=суфікс
luckperms.usage.meta-removetemp-suffix.argument.context=контексти, яких повинен бути вилученим суфікс
luckperms.usage.meta-clear.description=Вилучає всі мета значення
luckperms.usage.meta-clear.argument.type=тип мета для вилучення
luckperms.usage.meta-clear.argument.context=контексти для фільтрації
luckperms.usage.track-info.description=Дає інформацію про трек
luckperms.usage.track-editor.description=Розгортає вебредактор дозволів
luckperms.usage.track-append.description=Додає групу в кінець треку
luckperms.usage.track-append.argument.group=група, яку потрібно додати
luckperms.usage.track-insert.description=Додає групу із заданою позицією в трек
luckperms.usage.track-insert.argument.group=група, яку потрібно вставити
luckperms.usage.track-insert.argument.position=позиція для вставлення групи(починаючи із 1)
luckperms.usage.track-remove.description=Вилучає групу із треку
luckperms.usage.track-remove.argument.group=група для вилучення
luckperms.usage.track-clear.description=Вилучає список груп у треці
luckperms.usage.track-rename.description=Перейменувати трек
luckperms.usage.track-rename.argument.name=нове ім''я
luckperms.usage.track-clone.description=Клонувати трек
luckperms.usage.track-clone.argument.name=назва треку, у який необхідно виконати клонування
luckperms.usage.log-recent.description=Перегляд останніх дій
luckperms.usage.log-recent.argument.user=ім''я користувача/uuid для фільтрації по
luckperms.usage.log-recent.argument.page=номер сторінки для перегляду
luckperms.usage.log-search.description=Пошук запису в лозі
luckperms.usage.log-search.argument.query=запит для пошуку
luckperms.usage.log-search.argument.page=номер сторінки для перегляду
luckperms.usage.log-notify.description=Увімкнути сповіщення про логування
luckperms.usage.log-notify.argument.toggle=увімкнути/вимкнути
luckperms.usage.log-user-history.description=Перегляд історії користувача
luckperms.usage.log-user-history.argument.user=ім''я/uuid користувача
luckperms.usage.log-user-history.argument.page=номер сторінки для перегляду
luckperms.usage.log-group-history.description=Перегляд історії для групи
luckperms.usage.log-group-history.argument.group=ім''я групи
luckperms.usage.log-group-history.argument.page=номер сторінки для перегляду
luckperms.usage.log-track-history.description=Перегляд історії для треку
luckperms.usage.log-track-history.argument.track=ім''я треку
luckperms.usage.log-track-history.argument.page=номер сторінки для перегляду
luckperms.usage.sponge.description=Редагувати додаткові дані Sponge
luckperms.usage.sponge.argument.collection=набір для запиту
luckperms.usage.sponge.argument.subject=тема, що змінюється
luckperms.usage.sponge-permission-info.description=Показує інформацію про дозволи суб''єкту
luckperms.usage.sponge-permission-info.argument.contexts=контексти для фільтрації
luckperms.usage.sponge-permission-set.description=Установлює дозвіл для суб''єкта
luckperms.usage.sponge-permission-set.argument.node=дозвіл на встановлення
luckperms.usage.sponge-permission-set.argument.tristate=значення для встановлення дозволу
luckperms.usage.sponge-permission-set.argument.contexts=контексти, у яких повинен бути встановлений дозвіл
luckperms.usage.sponge-permission-clear.description=Вилучає дозволи суб''єкта
luckperms.usage.sponge-permission-clear.argument.contexts=контексти, у яких повинні бути вилучені дозволи
luckperms.usage.sponge-parent-info.description=Показує інформацію про батьківські групи суб''єкта
luckperms.usage.sponge-parent-info.argument.contexts=контексти для фільтрації
luckperms.usage.sponge-parent-add.description=Додає батьківську групу суб''єкта
luckperms.usage.sponge-parent-add.argument.collection=колекція суб''єкта, де батьківським елементом є суб''єкт
luckperms.usage.sponge-parent-add.argument.subject=ім''я батьківського суб''єкта
luckperms.usage.sponge-parent-add.argument.contexts=контексти, у яких повинна бути додана батьківська група
luckperms.usage.sponge-parent-remove.description=Вилучає батьківську групу в суб''єкта
luckperms.usage.sponge-parent-remove.argument.collection=колекція суб''єкта, де батьківським елементом є суб''єкт
luckperms.usage.sponge-parent-remove.argument.subject=ім''я батьківського суб''єкта
luckperms.usage.sponge-parent-remove.argument.contexts=контексти, у яких повинні бути вилучені батьківські групи
luckperms.usage.sponge-parent-clear.description=Очищає батьківські групи суб''єкта
luckperms.usage.sponge-parent-clear.argument.contexts=контексти, у яких повинні бути вилучені батьківські групи
luckperms.usage.sponge-option-info.description=Показує інформацію про параметри суб''єкта
luckperms.usage.sponge-option-info.argument.contexts=контексти для фільтрації
luckperms.usage.sponge-option-set.description=Установлює параметр для суб''єкта
luckperms.usage.sponge-option-set.argument.key=ключ для встановлення
luckperms.usage.sponge-option-set.argument.value=значення ключа для встановлення
luckperms.usage.sponge-option-set.argument.contexts=контексти, у яких повинен бути встановлений параметр
luckperms.usage.sponge-option-unset.description=Скасовує параметри в суб''єкта
luckperms.usage.sponge-option-unset.argument.key=ключ для скидання
luckperms.usage.sponge-option-unset.argument.contexts=контексти, у яких повинні бути скасовані ключі
luckperms.usage.sponge-option-clear.description=Вилучає опції суб''єктів
luckperms.usage.sponge-option-clear.argument.contexts=контексти, у яких повинні бути вилучені опції
