isEnabled: true
entityType: ZOMBIE
name: $eventBossLevel &eTreasure Goblin
level: dynamic
isPersistent: true
healthMultiplier: 4.0
damageMultiplier: 2.0
isBaby: true
deathMessages:
- '&e&l---------------------------------------------'
- '&eThe Treasure Goblin has been pillaged!'
- '&c&l    1st Damager: $damager1name &cwith $damager1damage damage!'
- '&6&l    2nd Damager: $damager2name &6with $damager2damage damage!'
- '&e&l    3rd Damager: $damager3name &ewith $damager3damage damage!'
- '&aSlayers: $players'
- '&e&l---------------------------------------------'
powers:
- gold_explosion.yml
- gold_shotgun.yml
- hyper_loot.yml
- spirit_walk.yml
trails:
- GOLD_NUGGET
locationMessage: '&cTreasure Goblin: $distance blocks away!'
spawnMessage: '&cA Treasure Goblin has been sighted!'
deathMessage: '&aA Treasure Goblin has been slain by $players!'
escapeMessage: '&4A Treasure Goblin has escaped!'
customModel: em_goblin_treasure
announcementPriority: 2
followDistance: 100
helmet: GOLDEN_HELMET
chestplate: GOLDEN_CHESTPLATE
leggings: GOLDEN_LEGGINGS
boots: GOLDEN_BOOTS
onSpawnBlockStates: []
onRemoveBlockStates: []
bossType: NORMAL
