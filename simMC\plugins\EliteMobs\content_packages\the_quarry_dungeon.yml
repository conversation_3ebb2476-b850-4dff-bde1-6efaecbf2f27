isEnabled: true
name: '&2[lvl 040] &3The Quarry Dungeon'
customInfo:
- '&fAn ancient dwarven quarry deep underground.'
- '&6Credits: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>'
dungeonSizeCategory: DUNGEON
worldName: em_id_the_quarry
environment: NORMAL
protect: true
playerInfo: 'Difficulty: &45-man hard content!'
regionEnterMessage: '&bGo down into the quarry and get that lift working.'
regionLeaveMessage: '&bYou have left The Quarry!'
startLocation: em_id_the_quarry,245.5,138,109.5,-90,30
teleportLocation: em_id_the_quarry,230.5,144,102.5,-67,0
dungeonObjectives:
- filename=em_id_the_quarry_quarry_smith.yml
- filename=em_id_the_quarry_royal_wizard_three.yml
- filename=LiftStateFinishDungeon.yml
contentType: INSTANCED_DUNGEON
dungeonConfigFolderName: em_id_the_quarry
contentLevel: 40
difficulties:
- name: normal
  id: 0
  levelSync: 42
- name: hard
  id: 1
  levelSync: 40
- name: mythic
  id: 2
  levelSync: 37
setupMenuDescription: []
