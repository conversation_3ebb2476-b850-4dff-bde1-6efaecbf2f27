isEnabled: true
name: '&2[lvl 000-200] &6Enchantment Challenge 20'
customInfo:
- '&fAn enchantment challenge dungeon!'
dungeonSizeCategory: SANCTUM
worldName: em_id_enchantment_challenge_20
environment: THE_END
protect: true
playerInfo: 'Difficulty: &4solo hard content!'
regionEnterMessage: '&bChallenge time!'
regionLeaveMessage: '&bYou have left the enchantment challenge!'
startLocation: em_id_enchantment_challenge_20,14.5,65,-12.5,45,0
teleportLocation: em_id_enchantment_challenge_20,-15.5,94,15.5,-135,0.0
maxPlayerCount: 1
dungeonObjectives:
- filename=enchantment_boss_soul_trampler.yml
contentType: INSTANCED_DUNGEON
dungeonConfigFolderName: em_id_enchantment_challenge_20
contentLevel: 1
difficulties:
- id: 0
  name: normal
enchantmentChallenge: true
setupMenuDescription: []
