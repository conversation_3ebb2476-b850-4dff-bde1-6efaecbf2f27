{"minecraft:recipes/building_blocks/lime_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/decorations/torch": {"criteria": {"has_stone_pickaxe": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/coarse_dirt": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/light_gray_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/pink_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/orange_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/red_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/magenta_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/brown_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/light_blue_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/green_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:story/upgrade_tools": {"criteria": {"stone_pickaxe": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/blue_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/yellow_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/misc/map": {"criteria": {"has_compass": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/white_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/gray_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/black_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/purple_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:recipes/building_blocks/cyan_concrete_powder": {"criteria": {"has_gravel": "2025-03-28 06:04:48 +0200"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:badlands": "2025-03-29 04:30:06 +0200", "minecraft:beach": "2025-03-28 08:07:56 +0200", "minecraft:dark_forest": "2025-03-28 06:21:06 +0200", "minecraft:forest": "2025-03-29 04:30:08 +0200", "minecraft:taiga": "2025-06-17 12:36:39 +0300", "minecraft:mangrove_swamp": "2025-03-28 08:11:58 +0200", "minecraft:deep_ocean": "2025-03-28 11:00:42 +0200", "minecraft:river": "2025-03-28 08:08:02 +0200", "minecraft:snowy_plains": "2025-03-31 16:17:53 +0200", "minecraft:lush_caves": "2025-03-28 08:09:41 +0200", "minecraft:frozen_ocean": "2025-03-29 04:19:04 +0200", "minecraft:desert": "2025-03-28 06:28:03 +0200", "minecraft:frozen_river": "2025-06-17 12:38:44 +0300", "minecraft:jungle": "2025-03-28 06:04:49 +0200", "minecraft:ocean": "2025-03-28 11:00:41 +0200", "minecraft:swamp": "2025-03-28 08:09:59 +0200", "minecraft:meadow": "2025-03-31 16:21:29 +0200", "minecraft:birch_forest": "2025-03-31 16:22:13 +0200", "minecraft:savanna": "2025-03-28 08:07:41 +0200", "minecraft:plains": "2025-03-28 06:28:28 +0200", "minecraft:jagged_peaks": "2025-03-28 06:34:26 +0200"}, "done": false}, "minecraft:recipes/redstone/dispenser": {"criteria": {"has_bow": "2025-03-28 06:28:28 +0200"}, "done": true}, "minecraft:adventure/root": {"criteria": {"killed_something": "2025-03-28 08:13:53 +0200"}, "done": true}, "minecraft:adventure/kill_all_mobs": {"criteria": {"minecraft:skeleton": "2025-03-28 08:13:58 +0200", "minecraft:zombie": "2025-03-28 08:13:53 +0200"}, "done": false}, "minecraft:adventure/kill_a_mob": {"criteria": {"minecraft:zombie": "2025-03-28 08:13:53 +0200"}, "done": true}, "minecraft:recipes/misc/bone_meal": {"criteria": {"has_bone": "2025-03-28 08:13:59 +0200"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/pale_oak_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-03-28 11:00:41 +0200"}, "done": true}, "minecraft:recipes/misc/mojang_banner_pattern": {"criteria": {"has_enchanted_golden_apple": "2025-03-29 04:20:10 +0200"}, "done": true}, "minecraft:husbandry/balanced_diet": {"criteria": {"enchanted_golden_apple": "2025-03-29 04:20:13 +0200"}, "done": false}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2025-03-29 04:20:13 +0200"}, "done": true}, "minecraft:nether/explore_nether": {"criteria": {"minecraft:soul_sand_valley": "2025-03-29 04:32:27 +0200"}, "done": false}, "minecraft:nether/root": {"criteria": {"entered_nether": "2025-03-29 04:32:27 +0200"}, "done": true}, "minecraft:story/enter_the_nether": {"criteria": {"entered_nether": "2025-03-29 04:32:27 +0200"}, "done": true}, "minecraft:recipes/decorations/black_stained_glass_pane": {"criteria": {"has_glass": "2025-06-14 06:34:00 +0300"}, "done": true}, "DataVersion": 4189}