announcementPriority: 3
bossType: BOSS
cullReinforcements: true
damageMultiplier: 1.75
dropsEliteMobsLoot: true
dropsVanillaLoot: true
entityType: BLAZE
followDistance: 200
healthMultiplier: 30
isEnabled: true
isRegionalBoss: true
leashRadius: 70
level: 20
name: $bossLevel &cSparky
phases:
- fireworks_level_50_boss_phase_2.yml:0.66
- fireworks_level_50_boss_phase_3.yml:0.33
powers:
- arrow_fireworks.yml
- fireworks_barrage.yml
spawnCooldown: 360
spawnLocations:
- em_fireworks,-2.5,71.5,0.5,0.0,0.0:0
spawnMessage: '&eSparks fill the sky!'
eliteScript:
  PushZone:
    Events:
    - EliteMobSpawnEvent
    Zone:
      shape: CYLINDER
      radius: 60
      height: 5
      filter: ELITE
      Target:
        targetType: SELF_SPAWN
        offset: 0,15,0
    Actions:
    - action: PUSH
      vValue: 0,-10,0
      repeatEvery: 10
      Target:
        targetType: ZONE_FULL
      Conditions:
        isAlive: true
        Target:
          targetType: SELF
