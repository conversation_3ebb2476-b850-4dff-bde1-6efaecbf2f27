isEnabled: true
name: '&2[lvl 020] &3The Mines Dungeon'
customInfo:
- '&fReady to step up your dungeon game?'
- '&6Credits: <PERSON><PERSON>G<PERSON>, Frostcone, 69OzCanOfBepis, Realm of Lotheridon, Dali_'
dungeonSizeCategory: DUNGEON
worldName: em_id_the_mines
environment: NORMAL
protect: true
playerInfo: 'Difficulty: &45-man hard content!'
regionEnterMessage: '&bYou''''ve been asked to investigate what lurks in the mines.
  Find your way to the depths of the mine!'
regionLeaveMessage: '&bYou have left The Mines!'
startLocation: em_id_the_mines,256.5,116,-106.5,-29,8
teleportLocation: em_id_the_mines,250.5,116,-113.5,-42.0,7.0
dungeonObjectives:
- filename=the_mines_soulweaver_daine_p0.yml
- filename=the_mines_boss_soulfire_p1.yml
- filename=the_mines_mini_boss_tulpa.yml
- filename=the_mines_mini_boss_forger.yml
- filename=the_mines_mini_boss_tusk.yml
contentType: INSTANCED_DUNGEON
dungeonConfigFolderName: em_id_the_mines
contentLevel: 20
difficulties:
- name: normal
  id: 0
  levelSync: 25
- name: hard
  id: 1
  levelSync: 20
- name: mythic
  id: 2
  levelSync: 15
setupMenuDescription: []
