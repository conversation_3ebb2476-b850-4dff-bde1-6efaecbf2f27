#!/bin/bash

# Minecraft Server Docker Stop Script
# This script stops the Minecraft server and MySQL database containers

set -e  # Exit on any error

echo "🛑 Stopping Minecraft Server..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed."
    exit 1
fi

# Stop the containers gracefully
echo "🐳 Stopping Docker containers..."
docker-compose down

echo "✅ Minecraft server stopped successfully!"
echo ""
echo "📋 To start again:"
echo "   - Run './start.sh' to start the server"
echo ""
echo "🗑️  To remove all data (CAUTION!):"
echo "   - Run 'docker-compose down -v' to remove volumes"
echo "   - Run 'docker system prune' to clean up unused containers/images"
