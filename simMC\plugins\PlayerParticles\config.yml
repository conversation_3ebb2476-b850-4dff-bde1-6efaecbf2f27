#      _________ __                           __________                __   __        __
#      \______  \  | _____  ___ __  __________\______   \_____ ________/  |_|__| ____ |  |   ____   ______
#      |     ___/  | \__  \<   |  |/ __ \_  __ \     ___/\__  \\_  __ \   __\  |/ ___\|  | _/ __ \ /  ___/
#      |    |   |  |__/ __ \\___  \  ___/|  | \/    |     / __ \|  | \/|  | |  \  \___|  |_\  ___/ \___ \
#      |____|   |____(____  / ____|\___  >__|  |____|    (____  /__|   |__| |__|\___  >____/\___  >____  >
#                         \/\/         \/                     \/                    \/          \/     \/

# The locale to use in the /locale folder
# Default: en_US
locale: en_US

# If you're using other plugins to execute commands you may wish to turn off messages
# Default: true
message-enabled: true

# Whether or not to use the message-prefix field when displaying messages
# Default: true
use-message-prefix: true

# If the command /pp gui is enabled
# Disable this if you have your own custom GUI through another plugin
# Default: true
gui-enabled: true

# If the GUI should require the permission playerparticles.gui to open
# Default: false
gui-require-permission: false

# If the GUI should require the player to have permission for at least
# one effect and one style to be able to open the GUI
# Default: true
gui-require-effects-and-styles: true

# If true, only the preset groups will be available in the GUI
# Permissions to open the GUI will change to only open if the user has any preset groups available
# Default: false
gui-presets-only: false

# If true, the particle descriptions in the item lore for preset groups will be hidden
# Default: false
gui-presets-hide-particles-descriptions: false

# If true, the GUI will close after selecting a group (either saved or preset)
# Default: true
gui-close-after-group-selected: true

# If clicking a GUI button should make a noise
# Default: true
gui-button-sound: true

# Valid values: DISPLAY_FEET, DISPLAY_NORMAL, DISPLAY_OVERHEAD, HIDE, NONE
# DISPLAY_FEET will display particles using the feet style while moving
# DISPLAY_NORMAL will display particles using the normal style while moving
# DISPLAY_OVERHEAD will display particles using the overhead style while moving
# HIDE will hide particles while moving
# NONE will make this setting do nothing
# Note: You can change what styles follow this setting in their individual setting files
# Default: NONE
toggle-on-move: NONE

# The time (in ticks) a player has to be standing still before they are considered to be stopped
# This setting has no effect if toggle-on-move is set to false
# The value must be a positive whole number
# Default: 9
toggle-on-move-delay: 9

# If true, particles will be completely disabled while the player is in combat
# Note: You can change what styles follow this setting in their individual setting files
# Default: false
toggle-on-combat: false

# The time (in seconds) a player has to not be damaged/attacked to be considered out of combat
# This setting has no effect if toggle-on-combat is set to false
# The value must be a positive whole number
# Default: 15
toggle-on-combat-delay: 15

# If true, mobs will be included in the combat check in addition to players
# Default: false
toggle-on-combat-include-mobs: false

# A list of worlds that the plugin is disabled in
disabled-worlds:
  - disabled_world_name

# Should particles a player no longer has permission to use be removed on login?
# Default: false
check-permissions-on-login: false

# The maximum number of particles a player can apply at once
# The GUI will only display up to 21, don't set this any higher than that
# Default: 3
max-particles: 3

# The maximum number of groups a player can have saved
# The GUI will only display up to 21, don't set this any higher than that
# Default: 10
max-groups: 10

# Max fixed effects per player
# Default: 5
max-fixed-effects: 5

# Max fixed effect creation distance
# Determines how far away a player may create a fixed effect from themselves
# This measurement is in blocks
# Set to 0 for infinite distance
# Default: 32
max-fixed-effect-creation-distance: 32

# How many ticks to wait before spawning more particles
# Increasing this value may cause less lag (if there was any), but will decrease prettiness
# Only use whole numbers greater than or equal to 1
# Going over 3 will likely look terrible
# Default: 1
ticks-per-particle: 1

# Should particles be displayed when the player is invisible?
# This includes spectator mode or an invisibility potion
# Default: false
display-when-invisible: false

# From how many blocks away should a player be able to see the particles from another player?
# Default: 48
particle-render-range-player: 48

# From how many blocks away should a player be able to see the particles from a fixed effect?
# Default: 192
particle-render-range-fixed-effect: 192

# How many out of 360 hue ticks to move per game tick
# Higher values make the rainbow cycle faster
# Note: Must be a positive whole number
# Default: 2
rainbow-cycle-speed: 2

# How large should dust particles appear?
# Note: Can include decimals
# Only works in 1.13+
# Default: 1.0
dust-size: 1.0

# Should the 'trail' effect have its target at the player's location?
# This setting only works on 1.20.2+
# Default: false
trail-move-to-player: false

# Valid values: ACTION_BAR, TITLE, CHAT
# Where should the GUI group creation countdown message be displayed?
# Note: Server versions less than 1.11.2 will always use CHAT
# Default: ACTION_BAR
gui-group-creation-message-display-area: ACTION_BAR

# If true, a message will be displayed in chat telling the player to enter the command
# This might be required for some servers using bungee chat plugins
# Default: false
gui-group-creation-bungee-support: false

# If true, applying a preset group will not overwrite the current active particles
# Note: This does not check permissions against the max particles setting, use with caution
# Default: false
preset-groups-allow-overlapping: false

# If true, applying a preset group with overlapping enabled will only allow one particle per style
# Existing particles that cause duplicate styles will be overwritten
# This setting has no effect if preset-groups-allow-overlapping is set to false
# Default: false
preset-groups-overlapping-one-per-style: false

# The colors of the glass in the GUI
# Valid colors: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/DyeColor.html
gui-border-colors:

  # Default: WHITE
  default: WHITE

  # Default: MAGENTA
  edit-data: MAGENTA

  # Default: LIGHT_BLUE
  edit-effect: LIGHT_BLUE

  # Default: RED
  edit-particle: RED

  # Default: BLUE
  edit-style: BLUE

  # Default: BROWN
  manage-groups: BROWN

  # Default: ORANGE
  manage-particles: ORANGE

  # Default: GREEN
  load-preset-groups: GREEN

# Settings for WorldGuard
# If WorldGuard is not installed, these settings will do nothing
worldguard-settings:

  # If true, particles will only be able to spawn if they are in an allowed region and not a disallowed region
  # If false, particles will be able to spawn as long as they are not in a disallowed region
  # Default: false
  use-allowed-regions: false

  # Regions that particles will be allowed to spawn in
  # WARNING: This setting is deprecated in favor of region flags, and will be removed in a future update.
  allowed-regions:
    - example_region_1
    - example_region_2

  # Regions that particles will be blocked from spawning in
  # This overrides allowed regions if they overlap
  # WARNING: This setting is deprecated in favor of region flags, and will be removed in a future update.
  disallowed-regions:
    - example_region_3
    - example_region_4

  # How often to check if a player is in a region that allows spawning particles
  # Measured in ticks
  # Default: 10
  check-interval: 10

  # If true, the permission playerparticles.worldguard.bypass will allow
  # the player to bypass the region requirements
  # Default: false
  enable-bypass-permission: false

# This configuration option allows you to change the GUI
# icons to whatever block/item you want. If you want to change an effect
# or style icon, use their respective config files.
# Notes: If any of the block/item names are invalid the icon in the GUI
# will be the barrier icon to show that it failed to load.
# You MUST use the Spigot-given name for it to work. You can see
# all the Spigot-given names at the link below:
# https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html
# If multiple icons are listed, they are used for older MC versions
gui-icon:
  misc:
    particles:
      - BLAZE_POWDER
    groups:
      - CHEST
    preset_groups:
      - ENDER_CHEST
    back:
      - ARROW
    next_page:
      - PAPER
    previous_page:
      - PAPER
    create:
      - WRITABLE_BOOK
      - BOOK_AND_QUILL
    edit_effect:
      - FIREWORK_ROCKET
      - FIREWORK
    edit_style:
      - NETHER_STAR
    edit_data:
      - BOOK
    reset:
      - BARRIER

# Settings for if you want to use MySQL for data management
mysql-settings:

  # Enable MySQL
  # If false, SQLite will be used instead
  # Default: false
  enabled: true

  # MySQL Database Hostname
  # Default:
  hostname: 'mysql'

  # MySQL Database Port
  # Default: 3306
  port: 3306

  # MySQL Database Name
  # Default:
  database-name: 'minecraft-database'

  # MySQL Database User Name
  # Default:
  user-name: 'hamza'

  # MySQL Database User Password
  # Default:
  user-password: 'Hh@#2021'

  # If the database connection should use SSL
  # You should enable this if your database supports SSL
  # Default: false
  use-ssl: false

  # The number of connections to make to the database
  # Default: 3
  connection-pool-size: 3
