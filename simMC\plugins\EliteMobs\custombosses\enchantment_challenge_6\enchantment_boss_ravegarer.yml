bossType: MINIBOSS
damageMultiplier: 1.2
dropsEliteMobsLoot: false
dropsRandomLoot: false
dropsVanillaLoot: false
eliteScript:
  Push1:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 1,1,0
    Zone:
      Target:
        offset: 2,0,0
        targetType: SELF
      shape: CUBOID
      x: 1
      y: 2
      z: 3
  Push2:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: -1,1,0
    Zone:
      Target:
        offset: -2,0,0
        targetType: SELF
      shape: CUBOID
      x: 1
      y: 2
      z: 3
  Push21:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 1,1,0
    Zone:
      Target:
        offset: 4,0,0
        targetType: SELF
      shape: CUBOID
      x: 1
      y: 2
      z: 5
  Push22:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: -1,1,0
    Zone:
      Target:
        offset: -4,0,0
        targetType: SELF
      shape: CUBOID
      x: 1
      y: 2
      z: 5
  Push23:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 0,1,-1
    Zone:
      Target:
        offset: 0,0,-4
        targetType: SELF
      shape: CUBOID
      x: 5
      y: 2
      z: 1
  Push24:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 0,1,1
    Zone:
      Target:
        offset: 0,0,4
        targetType: SELF
      shape: CUBOID
      x: 5
      y: 2
      z: 1
  Push3:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 0,1,-1
    Zone:
      Target:
        offset: 0,0,-2
        targetType: SELF
      shape: CUBOID
      x: 3
      y: 2
      z: 1
  Push31:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 1,1,0
    Zone:
      Target:
        offset: 6,0,0
        targetType: SELF
      shape: CUBOID
      x: 1
      y: 2
      z: 7
  Push32:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: -1,1,0
    Zone:
      Target:
        offset: -6,0,0
        targetType: SELF
      shape: CUBOID
      x: 1
      y: 2
      z: 7
  Push33:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 0,1,-1
    Zone:
      Target:
        offset: 0,0,-6
        targetType: SELF
      shape: CUBOID
      x: 7
      y: 2
      z: 1
  Push34:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 0,1,1
    Zone:
      Target:
        offset: 0,0,6
        targetType: SELF
      shape: CUBOID
      x: 7
      y: 2
      z: 1
  Push4:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: PUSH
      repeatEvery: 5
      times: 4
      vValue: 0,1,1
    Zone:
      Target:
        offset: 0,0,2
        targetType: SELF
      shape: CUBOID
      x: 3
      y: 2
      z: 1
  PushVisual1:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 2,0,0
        targetType: SELF
        track: true
      shape: CUBOID
      x: 1
      y: 2
      z: 3
  PushVisual2:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: -2,0,0
        targetType: SELF
        track: true
      shape: CUBOID
      x: 1
      y: 2
      z: 3
  PushVisual21:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 4,0,0
        targetType: SELF
        track: true
      shape: CUBOID
      x: 1
      y: 2
      z: 5
  PushVisual22:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: -4,0,0
        targetType: SELF
        track: true
      shape: CUBOID
      x: 1
      y: 2
      z: 5
  PushVisual23:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 0,0,-4
        targetType: SELF
        track: true
      shape: CUBOID
      x: 5
      y: 2
      z: 1
  PushVisual24:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 0,0,4
        targetType: SELF
        track: true
      shape: CUBOID
      x: 5
      y: 2
      z: 1
  PushVisual3:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 0,0,-2
        targetType: SELF
        track: true
      shape: CUBOID
      x: 3
      y: 2
      z: 1
  PushVisual31:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 6,0,0
        targetType: SELF
        track: true
      shape: CUBOID
      x: 1
      y: 2
      z: 7
  PushVisual32:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: -6,0,0
        targetType: SELF
        track: true
      shape: CUBOID
      x: 1
      y: 2
      z: 7
  PushVisual33:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 0,0,-6
        targetType: SELF
        track: true
      shape: CUBOID
      x: 7
      y: 2
      z: 1
  PushVisual34:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 0,0,6
        targetType: SELF
        track: true
      shape: CUBOID
      x: 7
      y: 2
      z: 1
  PushVisual4:
    Actions:
    - Target:
        targetType: ZONE_FULL
        track: true
      action: SPAWN_PARTICLE
      particles:
      - particle: EXPLOSION_NORMAL
      repeatEvery: 5
      times: 4
    Zone:
      Target:
        offset: 0,0,2
        targetType: SELF
        track: true
      shape: CUBOID
      x: 3
      y: 2
      z: 1
  Trigger:
    Actions:
    - Target:
        range: 80
        targetType: NEARBY_PLAYERS
      action: MESSAGE
      sValue: '&7The mighty beast starts slamming it''s hooves into the ground.'
    - action: RUN_SCRIPT
      scripts:
      - PushVisual1
      - Push1
      - PushVisual2
      - Push2
      - PushVisual3
      - Push3
      - PushVisual4
      - Push4
      wait: 30
    - action: RUN_SCRIPT
      scripts:
      - PushVisual21
      - Push21
      - PushVisual22
      - Push22
      - PushVisual23
      - Push23
      - PushVisual24
      - Push24
      wait: 50
    - action: RUN_SCRIPT
      scripts:
      - PushVisual31
      - Push31
      - PushVisual32
      - Push32
      - PushVisual33
      - Push33
      - PushVisual34
      - Push34
      wait: 70
    Cooldowns:
      global: 20
      local: 200
    Events:
    - EliteMobDamagedByPlayerEvent
    - PlayerDamagedByEliteMobEvent
entityType: RAVAGER
followDistance: 60
frozen: false
healthMultiplier: 4.0
instanced: true
isEnabled: true
isRegionalBoss: true
leashRadius: 60
movementSpeedAttribute: 0.31
name: $minibossLevel &5Ravegarer
normalizedCombat: true
powers:
- attack_confusing.yml
- invulnerability_arrow.yml
- invulnerability_fire.yml
spawnLocations:
- em_id_enchantment_challenge_6,1.5,65,0.5,0,0
uniqueLootList:
- chance: 0.25
  difficultyID: 0
  filename: ec_06_helmet_normal.yml
- chance: 0.25
  difficultyID: 0
  filename: ec_06_leggings_normal.yml
- chance: 0.15
  difficultyID: 0
  filename: ec_06_hoe_normal.yml
- chance: 0.25
  difficultyID: 0
  filename: enchanted_book_critical_strikes.yml
