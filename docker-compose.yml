version: '3.8'

services:
  # MySQL Database Service
  mysql:
    image: mysql:8.0
    container_name: minecraft-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-Hh@#2021}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-minecraft-database}
      MYSQL_USER: ${MYSQL_USER:-hamza}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-Hh@#2021}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d:ro
      - ./database/config/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    networks:
      - minecraft-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-Hh@#2021}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Minecraft Server Service
  minecraft:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: minecraft-server
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      # Database connection settings
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${MYSQL_DATABASE:-minecraft-database}
      DB_USER: ${MYSQL_USER:-hamza}
      DB_PASSWORD: ${MYSQL_PASSWORD:-Hh@#2021}
      # Server settings
      EULA: "true"
      SERVER_NAME: "Minecraft Server"
      DIFFICULTY: "hard"
      GAMEMODE: "survival"
      MAX_PLAYERS: 100
      ONLINE_MODE: "false"
      PVP: "true"
      VIEW_DISTANCE: 40
      SIMULATION_DISTANCE: 10
      # Memory settings
      MEMORY: "15G"
    ports:
      - "25565:25565"  # Minecraft server port
      - "25575:25575"  # RCON port (if enabled)
    volumes:
      # Persistent data volumes
      - minecraft_world:/opt/minecraft/world
      - minecraft_world_nether:/opt/minecraft/world_nether
      - minecraft_world_the_end:/opt/minecraft/world_the_end
      - minecraft_plugins_data:/opt/minecraft/plugins
      - minecraft_logs:/opt/minecraft/logs
      # Configuration volumes (read-only)
      - ./config/server.properties:/opt/minecraft/server.properties:ro
      - ./config/bukkit.yml:/opt/minecraft/bukkit.yml:ro
      - ./config/spigot.yml:/opt/minecraft/spigot.yml:ro
      - ./config/paper.yml:/opt/minecraft/paper.yml:ro
      # Plugin configurations
      - ./config/plugins:/opt/minecraft/plugins/config:ro
    networks:
      - minecraft-network
    stdin_open: true
    tty: true
    healthcheck:
      test: ["CMD-SHELL", "nc -z localhost 25565 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Optional: phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: minecraft-phpmyadmin
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${MYSQL_USER:-hamza}
      PMA_PASSWORD: ${MYSQL_PASSWORD:-Hh@#2021}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-Hh@#2021}
    ports:
      - "8080:80"
    networks:
      - minecraft-network

# Networks
networks:
  minecraft-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  mysql_data:
    driver: local
  minecraft_world:
    driver: local
  minecraft_world_nether:
    driver: local
  minecraft_world_the_end:
    driver: local
  minecraft_plugins_data:
    driver: local
  minecraft_logs:
    driver: local
