# Docker files
Dockerfile
docker-compose.yml
.dockerignore

# Environment files
.env
.env.local
.env.production

# Git files
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Logs (will be mounted as volumes)
simMC/logs/
*.log

# Cache files
simMC/cache/
simMC/.cache/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Backup files
*.bak
*.backup
backups/

# Database files (will be handled by MySQL container)
*.db
*.sqlite
*.sqlite3

# Large world files that should be volumes
simMC/world/
simMC/world_nether/
simMC/world_the_end/
simMC/HUB/
simMC/IridiumSkyblock/
simMC/IridiumSkyblock_nether/
simMC/IridiumSkyblock_the_end/

# Plugin data that should be persistent
simMC/plugins/*/data/
simMC/plugins/*/playerdata/
simMC/plugins/*/userdata/

# Build artifacts
target/
build/
dist/

# Node modules (if any)
node_modules/

# Python cache
__pycache__/
*.pyc
*.pyo

# Java class files
*.class

# Archives
*.zip
*.tar.gz
*.rar
*.7z
