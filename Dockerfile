# Use OpenJDK 21 as base image (Minecraft 1.21.4 requires Java 21+)
FROM openjdk:21-jdk-slim

# Set maintainer information
LABEL maintainer="Hamza Damra"
LABEL description="Minecraft Server 1.21.4 with Paper"

# Install necessary packages
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Create minecraft user and group
RUN groupadd -r minecraft && useradd -r -g minecraft minecraft

# Create minecraft directory
RUN mkdir -p /opt/minecraft
WORKDIR /opt/minecraft

# Copy server files
COPY simMC/ /opt/minecraft/

# Create necessary directories if they don't exist
RUN mkdir -p /opt/minecraft/logs \
    && mkdir -p /opt/minecraft/plugins \
    && mkdir -p /opt/minecraft/world \
    && mkdir -p /opt/minecraft/world_nether \
    && mkdir -p /opt/minecraft/world_the_end

# Set proper permissions
RUN chown -R minecraft:minecraft /opt/minecraft

# Switch to minecraft user
USER minecraft

# Expose the default Minecraft port
EXPOSE 25565

# Expose RCON port (if enabled)
EXPOSE 25575

# Set environment variables
ENV JAVA_OPTS="-Xmx15G -Xms15G -XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+DisableExplicitGC -XX:+AlwaysPreTouch -XX:G1NewSizePercent=30 -XX:G1MaxNewSizePercent=40 -XX:G1HeapRegionSize=8M -XX:G1ReservePercent=20 -XX:G1HeapWastePercent=5 -XX:G1MixedGCCountTarget=4 -XX:InitiatingHeapOccupancyPercent=15 -XX:G1MixedGCLiveThresholdPercent=90 -XX:G1RSetUpdatingPauseTimePercent=5 -XX:SurvivorRatio=32 -XX:+PerfDisableSharedMem -XX:MaxTenuringThreshold=1 -Dusing.aikars.flags=https://mcflags.emc.gs -Daikars.new.flags=true"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:25565 || exit 1

# Start the server
CMD ["sh", "-c", "java $JAVA_OPTS -jar server.jar nogui"]
