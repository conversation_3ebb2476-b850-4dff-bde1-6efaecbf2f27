@echo off
REM Minecraft Server Docker Start Script for Windows
REM This script starts the Minecraft server and MySQL database containers

echo 🚀 Starting Minecraft Server...

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose is not installed. Please install docker-compose and try again.
    pause
    exit /b 1
)

REM Check if containers are built
docker images | findstr "simmc" >nul 2>&1
if errorlevel 1 (
    echo 🔨 Containers not found. Building them first...
    call build.cmd
)

REM Start the containers
echo 🐳 Starting Docker containers...
docker-compose up -d

REM Wait for MySQL to be ready
echo ⏳ Waiting for MySQL to be ready...
set /a timeout=60
set /a counter=0
:mysql_wait
docker exec minecraft-mysql mysqladmin ping -h localhost -u root -pHh@#2021 --silent >nul 2>&1
if errorlevel 1 (
    timeout /t 2 /nobreak >nul
    set /a counter+=2
    if %counter% geq %timeout% (
        echo ❌ MySQL failed to start within %timeout% seconds
        pause
        exit /b 1
    )
    goto mysql_wait
)

echo ✅ MySQL is ready!

REM Wait for Minecraft server to be ready
echo ⏳ Waiting for Minecraft server to be ready...
set /a timeout=120
set /a counter=0
:minecraft_wait
docker exec minecraft-server nc -z localhost 25565 >nul 2>&1
if errorlevel 1 (
    timeout /t 5 /nobreak >nul
    set /a counter+=5
    if %counter% geq %timeout% (
        echo ⚠️  Minecraft server is taking longer than expected to start
        echo    Check logs with: docker-compose logs minecraft
        goto show_status
    )
    goto minecraft_wait
)

echo ✅ Minecraft server is ready!

:show_status
echo.
echo 🎮 Minecraft Server Status:
echo    Server: localhost:25565
echo    phpMyAdmin: http://localhost:8080
echo    Database: localhost:3306
echo.
echo 📋 Useful commands:
echo    - View server logs: docker-compose logs -f minecraft
echo    - View all logs: docker-compose logs -f
echo    - Stop server: docker-compose down
echo    - Restart server: docker-compose restart minecraft
echo    - Server console: docker exec -it minecraft-server bash
echo    - MySQL console: docker exec -it minecraft-mysql mysql -u hamza -p minecraft-database

pause
