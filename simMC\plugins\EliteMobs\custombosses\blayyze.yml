isEnabled: true
entityType: BLAZE
name: $eventBossLevel &6Blayyze
level: dynamic
isPersistent: true
healthMultiplier: 4.0
damageMultiplier: 2.0
deathMessages:
- '&e&l---------------------------------------------'
- '&6The Blayyze has been repelled!'
- '&c&l    1st Damager: $damager1name &cwith $damager1damage damage!'
- '&6&l    2nd Damager: $damager2name &6with $damager2damage damage!'
- '&e&l    3rd Damager: $damager3name &ewith $damager3damage damage!'
- '&aSlayers: $players'
- '&e&l---------------------------------------------'
uniqueLootList:
- meteor_shower_scroll.yml:1
powers:
- meteor_shower.yml
- summon_embers.yml
- bullet_hell.yml
- spirit_walk.yml
trails:
- FIRE_CHARGE
locationMessage: '&6????: $distance blocks away'
spawnMessage: '&cSomething came out of the meteorite''s crater...'
deathMessage: '&6$players completed first contact.'
escapeMessage: '&6The ayyliens have been taken to area 51!'
announcementPriority: 2
timeout: 30
onSpawnBlockStates: []
onRemoveBlockStates: []
bossType: NORMAL
