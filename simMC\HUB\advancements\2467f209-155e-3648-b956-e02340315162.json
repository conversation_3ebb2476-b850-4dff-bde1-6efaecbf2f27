{"minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-03-29 04:21:18 +0200"}, "done": true}, "minecraft:recipes/misc/map": {"criteria": {"has_compass": "2025-03-29 04:21:18 +0200"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:badlands": "2025-03-29 04:30:10 +0200", "minecraft:dark_forest": "2025-03-29 04:21:26 +0200", "minecraft:forest": "2025-03-29 04:30:12 +0200", "minecraft:jungle": "2025-03-29 04:21:19 +0200", "minecraft:swamp": "2025-03-29 05:13:14 +0200", "minecraft:birch_forest": "2025-03-29 05:02:55 +0200", "minecraft:plains": "2025-03-29 04:21:48 +0200"}, "done": false}, "minecraft:recipes/redstone/dispenser": {"criteria": {"has_bow": "2025-03-29 04:24:42 +0200"}, "done": true}, "minecraft:adventure/root": {"criteria": {"killed_by_something": "2025-03-29 04:25:29 +0200"}, "done": true}, "minecraft:recipes/transportation/detector_rail": {"criteria": {"has_rail": "2025-03-29 04:27:45 +0200"}, "done": true}, "minecraft:recipes/transportation/activator_rail": {"criteria": {"has_rail": "2025-03-29 04:27:45 +0200"}, "done": true}, "minecraft:recipes/transportation/powered_rail": {"criteria": {"has_rail": "2025-03-29 04:27:45 +0200"}, "done": true}, "minecraft:recipes/building_blocks/jungle_planks": {"criteria": {"has_logs": "2025-03-29 04:28:27 +0200"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-03-29 04:28:27 +0200"}, "done": true}, "minecraft:recipes/redstone/jungle_trapdoor": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/decorations/jungle_sign": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/redstone/jungle_door": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/decorations/jungle_fence": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/redstone/jungle_pressure_plate": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/redstone/jungle_button": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/building_blocks/jungle_slab": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/building_blocks/jungle_stairs": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/redstone/jungle_fence_gate": {"criteria": {"has_planks": "2025-03-29 04:28:51 +0200"}, "done": true}, "minecraft:recipes/decorations/ladder": {"criteria": {"has_stick": "2025-03-29 04:28:55 +0200"}, "done": true}, "minecraft:recipes/decorations/campfire": {"criteria": {"has_stick": "2025-03-29 04:28:55 +0200"}, "done": true}, "minecraft:recipes/tools/wooden_hoe": {"criteria": {"has_stick": "2025-03-29 04:28:55 +0200"}, "done": true}, "minecraft:recipes/tools/wooden_pickaxe": {"criteria": {"has_stick": "2025-03-29 04:28:55 +0200"}, "done": true}, "minecraft:recipes/tools/wooden_axe": {"criteria": {"has_stick": "2025-03-29 04:28:55 +0200"}, "done": true}, "minecraft:recipes/tools/wooden_shovel": {"criteria": {"has_stick": "2025-03-29 04:28:55 +0200"}, "done": true}, "minecraft:recipes/combat/wooden_sword": {"criteria": {"has_stick": "2025-03-29 04:28:55 +0200"}, "done": true}, "minecraft:story/root": {"criteria": {"crafting_table": "2025-03-29 04:28:55 +0200"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-03-29 04:29:20 +0200"}, "done": true}, "minecraft:nether/netherite_armor": {"criteria": {"netherite_armor": "2025-03-29 04:29:20 +0200"}, "done": true}, "minecraft:adventure/kill_all_mobs": {"criteria": {"minecraft:vindicator": "2025-03-29 05:13:56 +0200", "minecraft:zombie": "2025-03-29 04:29:31 +0200"}, "done": false}, "minecraft:adventure/kill_a_mob": {"criteria": {"minecraft:zombie": "2025-03-29 04:29:31 +0200"}, "done": true}, "minecraft:husbandry/balanced_diet": {"criteria": {"enchanted_golden_apple": "2025-03-29 05:10:26 +0200", "golden_carrot": "2025-03-29 04:29:43 +0200"}, "done": false}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2025-03-29 04:29:43 +0200"}, "done": true}, "minecraft:recipes/food/suspicious_stew_from_cornflower": {"criteria": {"has_cornflower": "2025-03-29 04:31:13 +0200"}, "done": true}, "minecraft:recipes/misc/blue_dye_from_cornflower": {"criteria": {"has_cornflower": "2025-03-29 04:31:13 +0200"}, "done": true}, "minecraft:nether/explore_nether": {"criteria": {"minecraft:soul_sand_valley": "2025-03-29 04:32:28 +0200"}, "done": false}, "minecraft:nether/root": {"criteria": {"entered_nether": "2025-03-29 04:32:28 +0200"}, "done": true}, "minecraft:story/enter_the_nether": {"criteria": {"entered_nether": "2025-03-29 04:32:28 +0200"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:adventure/trim_with_all_exclusive_armor_patterns": {"criteria": {"armor_trimmed_minecraft:vex_armor_trim_smithing_template_smithing_trim": "2025-03-29 05:04:52 +0200"}, "done": false}, "minecraft:recipes/building_blocks/netherite_block": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/combat/netherite_boots_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/tools/netherite_shovel_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/combat/netherite_chestplate_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/combat/netherite_sword_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/misc/netherite_upgrade_smithing_template": {"criteria": {"has_netherite_upgrade_smithing_template": "2025-03-29 05:03:22 +0200"}, "done": true}, "minecraft:adventure/overoverkill": {"criteria": {"overoverkill": "2025-03-29 05:20:53 +0200"}, "done": true}, "minecraft:recipes/misc/coast_armor_trim_smithing_template": {"criteria": {"has_coast_armor_trim_smithing_template": "2025-03-29 05:01:38 +0200"}, "done": true}, "minecraft:recipes/misc/sentry_armor_trim_smithing_template": {"criteria": {"has_sentry_armor_trim_smithing_template": "2025-03-29 05:04:32 +0200"}, "done": true}, "minecraft:recipes/misc/sentry_armor_trim_smithing_template_smithing_trim": {"criteria": {"has_smithing_trim_template": "2025-03-29 05:04:32 +0200"}, "done": true}, "minecraft:recipes/combat/netherite_leggings_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/tools/netherite_axe_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/misc/vex_armor_trim_smithing_template_smithing_trim": {"criteria": {"has_smithing_trim_template": "2025-03-29 05:04:48 +0200"}, "done": true}, "minecraft:recipes/tools/netherite_hoe_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:end/root": {"criteria": {"entered_end": "2025-03-29 05:16:40 +0200"}, "done": true}, "minecraft:recipes/transportation/pale_oak_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:recipes/misc/coast_armor_trim_smithing_template_smithing_trim": {"criteria": {"has_smithing_trim_template": "2025-03-29 05:01:38 +0200"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:recipes/tools/netherite_pickaxe_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:adventure/trim_with_any_armor_pattern": {"criteria": {"armor_trimmed_minecraft:vex_armor_trim_smithing_template_smithing_trim": "2025-03-29 05:04:52 +0200"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:recipes/decorations/lodestone": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:recipes/misc/vex_armor_trim_smithing_template": {"criteria": {"has_vex_armor_trim_smithing_template": "2025-03-29 05:04:48 +0200"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-03-29 05:13:32 +0200"}, "done": true}, "minecraft:story/enter_the_end": {"criteria": {"entered_end": "2025-03-29 05:16:40 +0200"}, "done": true}, "minecraft:recipes/misc/mojang_banner_pattern": {"criteria": {"has_enchanted_golden_apple": "2025-03-29 05:08:30 +0200"}, "done": true}, "minecraft:recipes/combat/netherite_helmet_smithing": {"criteria": {"has_netherite_ingot": "2025-03-29 05:03:47 +0200"}, "done": true}, "minecraft:adventure/who_needs_rockets": {"criteria": {"who_needs_rockets": "2025-03-29 05:10:15 +0200"}, "done": true}, "DataVersion": 4189}