announcementPriority: 3
boots: DIAMOND_BOOTS
bossType: BOSS
chestplate: DIAMOND_CHESTPLATE
damageMultiplier: 1.75
deathMessage: '&2The Drowned King has been slain!'
deathMessages:
- '&e&l---------------------------------------------'
- '&4The Drowned King has been slain!'
- '&c&l    1st Damager: $damager1name &cwith $damager1damage damage!'
- '&6&l    2nd Damager: $damager2name &6with $damager2damage damage!'
- '&e&l    3rd Damager: $damager3name &ewith $damager3damage damage!'
- '&4Slayers: $players'
- '&e&l---------------------------------------------'
dropsEliteMobsLoot: true
entityType: DROWNED
escapeMessage: '&2The sewers grow quiet, for now...'
followRange: 40
healthMultiplier: 7
helmet: GOLDEN_HELMET
isEnabled: true
isRegionalBoss: true
leashRadius: 50.0
leggings: DIAMOND_LEGGINGS
level: 35
mainHand: TRIDENT
name: $bossLevel &4Drowned King
powers:
- firestorm.yml
- invulnerability_arrow.yml
- movement_speed.yml
- flame_pyre.yml
- hyper_loot.yml
- amount: 1
  chance: 0.5
  filename: sewer_tier_70_boss_reinforcement.yml
  inheritAggro: true
  inheritLevel: true
  spawnNearby: false
  summonType: ON_HIT
spawnCooldown: 120
spawnLocations:
- em_sewer_maze,-384.5,100.5,-171.5,0.0,0.0:0
spawnMessage: '&cThe sewers echo with the clamor of a returned king...'
uniqueLootList:
- sewer_drowned_kings_trident.yml:0.1
- sewer_drowned_kings_boots.yml:0.1
- sewer_drowned_kings_chestplate.yml:0.1
- sewer_drowned_kings_crown.yml:0.1
- sewer_drowned_kings_leggings.yml:0.1
