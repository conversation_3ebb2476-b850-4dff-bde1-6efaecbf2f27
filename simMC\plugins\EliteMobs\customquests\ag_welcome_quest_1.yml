isEnabled: true
customRewards:
- filename=magmaguys_toothpick.yml:amount=1:chance=1
- currencyAmount=750:amount=1:chance=1
questAcceptPermission: ''
questLockoutPermission: elitequest.ag_welcome_quest_1.yml
name: '&2Welcome to the AG!'
questLore:
- '&aMeet the Adventurer''s Guild NPCs!'
temporaryPermissions: []
questAcceptDialog: []
questCompleteMessage: []
questCompleteCommands: []
questLevel: 1
customObjectives:
  Objective9:
    filename: scrapper_config.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aKelly&8]&f Got extra Elite items? Don''t know what to do with them? Give
      them to me, and I''ll turn them into scrap!'
    - You can use scrap to repair Elite items!
    location: under the main building
    npcName: Kelly
  Objective3:
    filename: blacksmith.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aGreg&8]&f Want to buy or sell something? I''ve got a little of everything!'
    location: in the main building
    npcName: <PERSON>:
    filename: combat_instructor.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aCharles&8]&f Want to learn about fighting elites? Just talk to me!'
    location: next to the main building
    npcName: Charles
  Objective1:
    filename: back_teleporter.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aHermes&8]&f I take you back to where you were before! Just talk to me
      again!'
    location: by the gate
    npcName: Hermes
  Objective2:
    filename: barkeep.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aBartley&8]&f Need a stiff drink? My drinks pack an extra punch!'
    location: in the main building
    npcName: Bartley
  Objective7:
    filename: enchanted.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aEden&8]&f Need to enchant your Elite items? Just bring me an elite enchanted
      book!'
    location: under the main building
    npcName: Eden
  Objective8:
    filename: repairman_config.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aReggie&8]&f Need your Elite items repaired? Just bring me some scrap,
      and I''ll make them as good as new!'
    - Remember, the higher the level of your scrap, the better I can repair you Elite
      item!
    location: under the main building
    npcName: Reggie
  Objective5:
    filename: guild_attendant.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aGillian&8]&f Do you seek power? Do you desire to reach new heights? Talk
      to me to unlock new guild ranks, and reach levels of power you never thought
      to be possible!'
    location: in the main building
    npcName: Gillian
  Objective10:
    filename: special_blacksmith.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aGrog&8]&f Want to buy special items? I''ve got just the thing!'
    location: in the main building
    npcName: Grog
  Objective6:
    filename: quest_giver.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aQel''Thuzad&8]&f These Elite monsters, where do they keep coming from?'
    - Ah, never mind that for now, we are always in need of more people to assist
      us in slaying quests!
    - Just talk to me to get one!
    location: in the main building
    npcName: Qel'Thuzad
  Objective11:
    filename: unbinder.yml
    objectiveType: DIALOG
    dialog:
    - '&8[&aUlfric&8]&f If you bring me a very special and rare item, I can unbind
      your Elite items.'
    - You don't yet look prepared to take this challenge on, but you can make killing
      the Binder of Worlds your ultimate goal, if you dare.
    location: under the main building
    npcName: Ulfric
