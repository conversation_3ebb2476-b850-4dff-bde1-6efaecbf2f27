announcementPriority: 3
bossType: BOSS
cullReinforcements: true
damageMultiplier: 1.75
deathMessages:
- '&e&l---------------------------------------------'
- '&eSparky''s out, party''s over!'
- '&c&l    1st Damager: $damager1name &cwith $damager1damage damage!'
- '&6&l    2nd Damager: $damager2name &6with $damager2damage damage!'
- '&e&l    3rd Damager: $damager3name &ewith $damager3damage damage!'
- '&aSlayers: $players'
- '&e&l---------------------------------------------'
dropsEliteMobsLoot: true
entityType: BLAZE
followDistance: 200
healthMultiplier: 30
isEnabled: true
level: 20
name: $bossLevel &cSparky
powers:
- hyper_loot.yml
- fireworks_barrage.yml
- skeleton_tracking_arrow.yml
- arrow_fireworks.yml
- tracking_fireball.yml
- filename: fireworks_phase_3_reinforcement.yml
  inheritLevel: true
  location: 35,0,35
  summonType: ON_COMBAT_ENTER
- filename: fireworks_phase_3_reinforcement.yml
  inheritLevel: true
  location: -35,0,35
  summonType: ON_COMBAT_ENTER
- filename: fireworks_phase_3_reinforcement.yml
  inheritLevel: true
  location: -35,0,35
  summonType: ON_COMBAT_ENTER
- filename: fireworks_phase_3_reinforcement.yml
  inheritLevel: true
  location: -35,0,-35
  summonType: ON_COMBAT_ENTER
spawnMessage: '&6Sparky returns to celebrate!'
uniqueLootList:
- sparky_drop_axe.yml:0.15
- sparky_drop_sword.yml:0.05
- sparky_drop_shield.yml:0.15
eliteScript:
  PushZone:
    Events:
    - EliteMobSpawnEvent
    Zone:
      shape: CYLINDER
      radius: 60
      height: 5
      filter: ELITE
      Target:
        targetType: SELF_SPAWN
        offset: 0,15,0
    Actions:
    - action: PUSH
      vValue: 0,-10,0
      repeatEvery: 10
      Target:
        targetType: ZONE_FULL
      Conditions:
        isAlive: true
        Target:
          targetType: SELF