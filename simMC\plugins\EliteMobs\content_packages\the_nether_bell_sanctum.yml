isEnabled: true
name: '&2[lvl 055] &3The Nether Bell Sanctum'
customInfo:
- '&fVenture into the deepest part of the Nether!'
- '&6Credits: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>e'
dungeonSizeCategory: SANCTUM
worldName: em_id_the_nether_bell
environment: NORMAL
protect: true
playerInfo: 'Difficulty: &45-man hard content!'
regionEnterMessage: '&bNo being should be this deep in the Nether...'
regionLeaveMessage: '&bYou have left The Nether Bell!'
startLocation: em_id_the_nether_bell,184.5,69,-192.5,43,0
teleportLocation: em_id_the_nether_bell,231.5,87,-219.5,44,0
dungeonObjectives:
- filename=em_id_the_nether_bell_boss_void_bell_p1.yml
contentType: INSTANCED_DUNGEON
dungeonConfigFolderName: em_id_the_nether_bell
contentLevel: 55
difficulties:
- name: normal
  id: 0
  levelSync: 57
- name: hard
  id: 1
  levelSync: 55
- name: mythic
  id: 2
  levelSync: 53
setupMenuDescription: []
