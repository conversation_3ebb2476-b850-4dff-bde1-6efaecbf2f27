announcementPriority: 3
bossType: BOSS
cullReinforcements: true
damageMultiplier: 1.75
dropsEliteMobsLoot: true
dropsVanillaLoot: true
entityType: BLAZE
followDistance: 200
healthMultiplier: 30
isEnabled: true
level: 20
name: $bossLevel &cSparky
powers:
- arrow_fireworks.yml
- fireworks_barrage.yml
- skeleton_tracking_arrow.yml
- filename: fireworks_phase_2_reinforcement.yml
  inheritLevel: true
  location: 35,0,0
  summonType: ON_COMBAT_ENTER
- filename: fireworks_phase_2_reinforcement.yml
  inheritLevel: true
  location: -35,0,0
  summonType: ON_COMBAT_ENTER
- filename: fireworks_phase_2_reinforcement.yml
  inheritLevel: true
  location: 0,0,35
  summonType: ON_COMBAT_ENTER
- filename: fireworks_phase_2_reinforcement.yml
  inheritLevel: true
  location: 0,0,-35
  summonType: ON_COMBAT_ENTER
eliteScript:
  PushZone:
    Events:
    - EliteMobSpawnEvent
    Zone:
      shape: CYLINDER
      radius: 60
      height: 5
      filter: ELITE
      Target:
        targetType: SELF_SPAWN
        offset: 0,15,0
    Actions:
    - action: PUSH
      vValue: 0,-10,0
      repeatEvery: 10
      Target:
        targetType: ZONE_FULL
      Conditions:
        isAlive: true
        Target:
          targetType: SELF