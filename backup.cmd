@echo off
REM Minecraft Server Backup Script for Windows
REM This script creates backups of world data and database

setlocal enabledelayedexpansion

REM Create timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

set "BACKUP_DIR=.\backups\%timestamp%"
echo 🗄️  Creating backup in: %BACKUP_DIR%

REM Create backup directory
if not exist "backups" mkdir "backups"
mkdir "%BACKUP_DIR%"

REM Check if containers are running
docker-compose ps | findstr "Up" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Containers are not running. Starting them first...
    docker-compose up -d
    timeout /t 30 /nobreak >nul
)

REM Backup world data
echo 🌍 Backing up world data...
docker cp minecraft-server:/opt/minecraft/world "%BACKUP_DIR%\"
docker cp minecraft-server:/opt/minecraft/world_nether "%BACKUP_DIR%\"
docker cp minecraft-server:/opt/minecraft/world_the_end "%BACKUP_DIR%\"

REM Backup plugin data
echo 🔌 Backing up plugin data...
docker cp minecraft-server:/opt/minecraft/plugins "%BACKUP_DIR%\"

REM Backup database
echo 🗃️  Backing up database...
docker exec minecraft-mysql mysqladmin ping -h localhost -u root -pHh@#2021 --silent >nul 2>&1
if not errorlevel 1 (
    docker exec minecraft-mysql mysqldump -u root -pHh@#2021 --single-transaction --routines --triggers minecraft-database > "%BACKUP_DIR%\database.sql"
) else (
    echo ⚠️  Database not accessible, skipping database backup
)

REM Create backup info file
echo 📋 Creating backup info...
(
echo Backup created: %date% %time%
echo Minecraft Server Version: 1.21.4 Paper
echo Database: minecraft-database
echo User: hamza
echo.
echo Contents:
echo - world/ ^(Overworld^)
echo - world_nether/ ^(Nether^)
echo - world_the_end/ ^(End^)
echo - plugins/ ^(Plugin data and configurations^)
echo - database.sql ^(MySQL database dump^)
echo.
echo To restore:
echo 1. Stop the server: docker-compose down
echo 2. Copy world folders back to container
echo 3. Import database: docker exec -i minecraft-mysql mysql -u root -pHh@#2021 minecraft-database ^< database.sql
echo 4. Start server: docker-compose up -d
) > "%BACKUP_DIR%\backup_info.txt"

echo ✅ Backup completed: %BACKUP_DIR%
echo 📊 Backup location: %CD%\%BACKUP_DIR%

REM Clean up old backups (keep last 7 days)
echo 🧹 Cleaning up old backups...
forfiles /p ".\backups" /m "*.tar.gz" /d -7 /c "cmd /c del @path" 2>nul

echo 🎉 Backup process completed successfully!
pause
