# Minecraft Server Docker Setup

A complete Docker containerization setup for a Minecraft 1.21.4 Paper server with MySQL database integration for user authentication and player data management.

## 🚀 Quick Start

### Prerequisites

- **Docker Desktop** installed and running
- **Docker Compose** (included with Docker Desktop)
- At least **16GB RAM** available for the Minecraft server
- **10GB+ free disk space** for world data and database

### Windows Users

1. **Build the containers:**
   ```cmd
   build.cmd
   ```

2. **Start the server:**
   ```cmd
   start.cmd
   ```

3. **Stop the server:**
   ```cmd
   stop.cmd
   ```

### Linux/macOS Users

1. **Make scripts executable:**
   ```bash
   chmod +x build.sh start.sh stop.sh
   ```

2. **Build the containers:**
   ```bash
   ./build.sh
   ```

3. **Start the server:**
   ```bash
   ./start.sh
   ```

4. **Stop the server:**
   ```bash
   ./stop.sh
   ```

## 📋 What's Included

### Services

- **Minecraft Server** (Paper 1.21.4) - Port 25565
- **MySQL Database** - Port 3306
- **phpMyAdmin** - Port 8080 (http://localhost:8080)

### Database Configuration

- **Database Name:** `minecraft-database`
- **Username:** `hamza`
- **Password:** `Hh@#2021`
- **Root Password:** `Hh@#2021`

### Configured Plugins

The following plugins are configured to use MySQL:

- **LoginSecurity** - User authentication
- **UserLogin** - Alternative authentication system
- **LuckPerms** - Permissions management
- **IridiumSkyblock** - Skyblock plugin data
- **MyCommand** - Custom commands player data
- **PlayerParticles** - Player particle effects
- **AdvancedTeleport** - Teleportation data
- **EliteMobs** - RPG plugin data
- **TAB** - Tablist customization

## 🔧 Configuration

### Environment Variables

Edit the `.env` file to customize settings:

```env
# Database
MYSQL_ROOT_PASSWORD=Hh@#2021
MYSQL_DATABASE=minecraft-database
MYSQL_USER=hamza
MYSQL_PASSWORD=Hh@#2021

# Server
SERVER_NAME=Minecraft Server
DIFFICULTY=hard
GAMEMODE=survival
MAX_PLAYERS=100
MEMORY=15G
```

### Server Properties

Key server settings in `simMC/server.properties`:
- **Port:** 25565
- **Max Players:** 100
- **Difficulty:** Hard
- **Gamemode:** Survival
- **Online Mode:** False (for offline/cracked clients)

## 📊 Database Management

### Access phpMyAdmin

1. Open http://localhost:8080
2. Login with:
   - **Username:** `hamza`
   - **Password:** `Hh@#2021`

### Direct MySQL Access

```bash
# Connect to MySQL container
docker exec -it minecraft-mysql mysql -u hamza -p minecraft-database

# Or using root
docker exec -it minecraft-mysql mysql -u root -p
```

### Database Schema

The setup automatically creates tables for:
- User authentication (LoginSecurity, UserLogin)
- Permissions (LuckPerms)
- Player data (various plugins)
- Teleportation data (AdvancedTeleport)
- RPG data (EliteMobs)

## 🐳 Docker Commands

### View Logs

```bash
# All services
docker-compose logs -f

# Minecraft server only
docker-compose logs -f minecraft

# MySQL only
docker-compose logs -f mysql
```

### Container Management

```bash
# Restart specific service
docker-compose restart minecraft
docker-compose restart mysql

# Access server console
docker exec -it minecraft-server bash

# Access MySQL console
docker exec -it minecraft-mysql bash
```

### Data Management

```bash
# Backup world data
docker cp minecraft-server:/opt/minecraft/world ./backup/world

# Restore world data
docker cp ./backup/world minecraft-server:/opt/minecraft/

# Backup database
docker exec minecraft-mysql mysqldump -u root -pHh@#2021 minecraft-database > backup.sql

# Restore database
docker exec -i minecraft-mysql mysql -u root -pHh@#2021 minecraft-database < backup.sql
```

## 🔍 Troubleshooting

### Common Issues

**1. Server won't start / Out of memory**
- Ensure you have at least 16GB RAM available
- Reduce memory allocation in `.env` file (MEMORY=8G)

**2. Database connection failed**
- Wait for MySQL to fully start (can take 30-60 seconds)
- Check MySQL logs: `docker-compose logs mysql`

**3. Plugins not working**
- Check plugin configurations in `simMC/plugins/`
- Verify database tables were created: Access phpMyAdmin

**4. Permission denied (Linux/macOS)**
```bash
chmod +x *.sh
```

**5. Port already in use**
- Change ports in `docker-compose.yml`
- Stop conflicting services

### Health Checks

```bash
# Check if containers are running
docker-compose ps

# Check container health
docker-compose exec minecraft nc -z localhost 25565
docker-compose exec mysql mysqladmin ping -h localhost -u root -pHh@#2021
```

### Reset Everything

```bash
# Stop and remove all containers and volumes (CAUTION: Deletes all data!)
docker-compose down -v
docker system prune -f

# Rebuild from scratch
./build.sh
./start.sh
```

## 📁 Directory Structure

```
├── Dockerfile                 # Minecraft server container
├── docker-compose.yml         # Service orchestration
├── .env                      # Environment variables
├── .dockerignore             # Docker build exclusions
├── database/
│   ├── init/                 # Database initialization scripts
│   └── config/               # MySQL configuration
├── simMC/                    # Minecraft server files
│   ├── server.jar           # Paper server
│   ├── plugins/             # Server plugins
│   ├── world/               # World data (mounted as volume)
│   └── ...
├── build.sh/.cmd            # Build scripts
├── start.sh/.cmd            # Start scripts
├── stop.sh/.cmd             # Stop scripts
└── README.md                # This file
```

## 🔒 Security Notes

- Change default passwords in production
- Use environment variables for sensitive data
- Consider enabling SSL for database connections
- Restrict network access in production environments

## 🎮 Connecting to the Server

- **Server Address:** `localhost:25565`
- **Version:** Minecraft 1.21.4
- **Mode:** Offline (cracked clients supported)

## 📞 Support

For issues with:
- **Docker setup:** Check Docker Desktop is running and has sufficient resources
- **Plugin configuration:** Refer to individual plugin documentation
- **Database issues:** Check phpMyAdmin for table structure and data

---

**Enjoy your containerized Minecraft server! 🎮**
