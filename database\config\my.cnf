[mysqld]
# Basic settings
default-authentication-plugin=mysql_native_password
bind-address=0.0.0.0
port=3306

# Character set and collation
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init-connect='SET NAMES utf8mb4'

# Performance settings
max_connections=200
max_allowed_packet=64M
thread_cache_size=8
query_cache_size=32M
query_cache_limit=2M

# InnoDB settings
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_file_per_table=1
innodb_flush_log_at_trx_commit=1
innodb_lock_wait_timeout=50

# Logging
general_log=0
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# Binary logging (for replication if needed)
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

# Security settings
local-infile=0
skip-show-database

# Timezone
default-time-zone='+00:00'

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
