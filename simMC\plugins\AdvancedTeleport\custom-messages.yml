
# This messages file uses MiniMessage formatting, a new form of message formatting for newer versions of MC.
# More information about this formatting can be found here: https://docs.advntr.dev/minimessage/format.html# 
# If you prefer to use the Legacy Code format (i.e. &a, &b, etc.) then you can still use that format.
# 
# It is important to note though that this format may be subject to removal in a future version of AT, however nothing is set in stone yet.
Common:
  # The prefixes for messages, the first element of this list will be usable as <prefix>,
  # with each element after that being usable as <prefix:index> with index being the items index in the list.
  prefixes:
  - <aqua>↑</aqua> <dark_gray>»</dark_gray>
Teleport:
  eventBeforeTP: <prefix> <gray>Teleporting in <aqua><countdown> seconds</aqua>, please
    do not move!
  # This is an example use for titles and subtitles in the plugin.
  # This feature is supported only if you're on version 1.8.8 or newer.
  eventBeforeTP_title:
    length: 80
    fade-in: 0
    fade-out: 10
    '0': <gray><b>Teleporting...
    '20': <aqua>></aqua> <gray><b>Teleporting...</b></gray> <aqua><
    '40': <aqua>>></aqua> <gray><b>Teleporting...</b></gray> <aqua><<
    '60': <aqua>>>> <b><yellow>Teleported!</yellow></b> <aqua><<<
  eventBeforeTP_subtitle:
    '0': <aqua>Please do not move!
    '60': ''
  eventBeforeTPMovementAllowed: <prefix> <gray>Teleporting in <aqua><countdown></aqua>
    seconds!
  eventTeleport: <prefix> <gray>Teleporting...
  eventMovement: <prefix> <gray>Teleport has been cancelled due to movement.
  eventMovement_title:
    length: 60
    fade-in: 0
    fade-out: 10
    '0': <yellow><b>! <red>Cancelled</red> !
  teleportingToSpawn: <prefix> <gray>Teleporting you to spawn!
  teleporting: <prefix> <gray>Teleporting to <aqua><player></aqua>!
  teleportingToHome: <prefix> <gray>Teleporting to <aqua><home></aqua>!
  teleportingToHomeOther: <prefix> <gray>Teleporting to <aqua><player></aqua>'s home,
    <aqua><home></aqua>!
  teleportingToWarp: <prefix> <gray>Teleporting you to <aqua><warp></aqua>!
  teleportingPlayerToSelf: <prefix> <gray>Teleporting <aqua><player></aqua> to you!
  teleportingSelfToPlayer: <prefix> <gray>Teleporting you to <aqua><player></aqua>!
  teleportingToRandomPlace: <prefix> <gray>Teleporting you to a random place!
  teleportingToLastLoc: <prefix> <gray>Teleporting to your last location!
  teleportedToOfflinePlayer: <prefix> <gray>Teleported to offline player <aqua><player></aqua>!
  teleportedOfflinePlayerHere: <prefix> <gray>Teleported offline player <aqua><player></aqua>
    to your location!
Error:
  noPermission: <prefix> <gray>You do not have permission to use this command!
  noPermissionSign: <prefix> <gray>You do not have permission to make this sign!
  featureDisabled: <prefix> <gray>This feature has been disabled!
  noRequests: <prefix> <gray>You don't have any pending requests!
  tpOff: <prefix> <aqua><player> <gray>has their teleportation disabled!
  tpBlock: <prefix> <aqua><player> <gray>has blocked you from sending requests to
    them!
  alreadyOn: <prefix> <gray>Your teleport requests are already enabled!
  alreadyOff: <prefix> <gray>Your teleport requests are already disabled!
  alreadyBlocked: <prefix> <gray>This player is already blocked!
  neverBlocked: <prefix> <gray>This player was never blocked!
  onCooldown: <prefix> <gray>Please wait another <aqua><time></aqua> seconds to use
    this command!
  requestSentToSelf: <prefix> <gray>You can't send a request to yourself!
  noSuchPlayer: <prefix> <gray>The player is either currently offline or doesn't exist!
  alreadySentRequest: <prefix> <gray>You've already sent a request to <aqua><player></aqua>!
  notEnoughGeneral: |2
        <prefix> <gray>You cannot afford to teleport there!
        <prefix> <gray>You need <aqua><cost></aqua>!
  notEnoughEXP: |-
    <prefix> <gray>You do not have enough EXP Levels to teleport there!
        <prefix> <gray>You need at least <aqua><levels></aqua> EXP levels!
  notEnoughEXPPoints: |-
    <prefix> <gray>You do not have enough EXP Points to teleport there!
        <prefix> <gray>You need at least <aqua><points></aqua> EXP points!
  notEnoughMoney: |-
    <prefix> <gray>You do not have enough money to teleport there!
        <prefix> <gray>You need at least <aqua><amount></aqua>!
  requestExpired: <prefix> <gray>Your teleport request to <aqua><player></aqua> has
    expired!
  noPlayerInput: <prefix> <gray>You must include a player name!
  blockSelf: <prefix> <gray>You can't block yourself!
  noRequestsFromPlayer: <prefix> <gray>You don't have any pending requests from <aqua><player></aqua>!
  invalidPageNo: <prefix> <gray>You've inserted an invalid page number!
  noHomeInput: <prefix> <gray>You have to include a home name!
  noSuchHome: <prefix> <gray>This home doesn't exist!
  noBedHome: <prefix> <gray>You don't have any bed spawn set!
  noBedHomeOther: <prefix> <aqua><player></aqua> <gray>doesn't have a bed spawn set!
  reachedHomeLimit: <prefix> <gray>You can't set any more homes!
  homeAlreadySet: <prefix> <gray>You already have a home called <aqua><home></aqua>!
  noWarpInput: <prefix> <gray>You have to include the warp's name!
  noSuchWarp: <prefix> <gray>That warp doesn't exist!
  warpAlreadySet: <prefix> <gray>There is already a warp called <aqua><warp></aqua>!
  noSuchWorld: <prefix> <gray>That world doesn't exist!
  worldUnloaded: <prefix> <gray>Sorry, the destination world either doesn't exist
    or isn't loaded. Please tell a server admin!
  noLocation: <prefix> <gray>You don't have any location to teleport back to!
  notAPlayer: <prefix> <gray>You must be a player to run this command!
  noHomes: <prefix> <gray>You haven't got any homes!
  noHomesOther: <prefix> <aqua><player></aqua> <gray>hasn't got any homes!
  tooFarAway: <prefix> <gray>The teleport destination is too far away so you can not
    teleport there!
  noRequestsSent: <prefix> <gray>Couldn't send a request to anyone :(
  onCountdown: <prefix> <gray>You can't use this command whilst waiting to teleport!
  noPermissionWarp: <prefix> <gray>You can't warp to <aqua><warp></aqua>!
  cantTPToWorld: <prefix> <gray>You can't randomly teleport in that world!
  cantTPToWorldLim: <prefix> <gray>You can't teleport to <aqua><world></aqua>!
  tooFewArguments: <prefix> <gray>Too few arguments!
  invalidArgs: <prefix> <gray>Invalid arguments!
  noOthersToTP: <prefix> <gray>There are no players for you to teleport!
  cantTPToPlayer: <prefix> <gray>You can't request a teleportation to <aqua><player></aqua>!
  noWarps: <prefix> <gray>There are no warps as of currently!
  noAccessHome: <prefix> <gray>You cannot access <aqua><home></aqua> as of currently!
  moveHomeFail: <prefix> <gray>The home has been moved but the data has not been stored
    successfully. The plugin will try to fix this itself.
  setMainHomeFail: <prefix> <gray>The main home has been set but the data has not
    been stored successfully. The plugin will try to fix this itself.
  deleteHomeFail: <prefix> <gray>The home has been deleted but the data has not been
    stored successfully. The plugin will try to fix this itself.
  setHomeFail: <prefix> <gray>The home has been set but the data has not been stored
    successfully. The plugin will try to fix this itself.
  deleteWarpFail: <prefix> <gray>The warp has been set but the data has not been stored
    successfully. The plugin will try to fix this itself.
  purgeWarpsFail: <prefix> <gray>Could not purge warps. Please check the console for
    more information.
  purgeHomesFail: <prefix> <gray>Could not purge homes. Please check the console for
    more information.
  homesNotLoaded: <prefix> <gray>Homes for this player haven't loaded yet, please
    wait a little bit (even just a second) before trying this again!
  noOfflineLocation: <prefix> <gray>No offline location was found for <aqua><player></aqua>!
  failedOfflineTeleport: <prefix> <gray>Failed to teleport to offline player <aqua><player></aqua>!
  failedOfflineTeleportHere: <prefix> <gray>Failed to teleport offline player <aqua><player></aqua>
    to your location!
  alreadySearching: <prefix> <gray>Already searching for a location to teleport to!
  mirrorSpawnNoArguments: <prefix> <gray>No worlds/spawn points have been specified!
  mirrorSpawnLackOfArguments: <prefix> <gray>You must be a player to only specify
    one world - please specify a world and a spawnpoint to mirror players to!
  noSuchSpawn: <prefix> <gray>There is no such spawn called <aqua><spawn></aqua>!
  cannotSetMainSpawn: <prefix> <gray>You can only make existing spawnpoints into the
    main spawnpoint rather than create new ones!
  cannotSetMainSpawnConsole: <prefix> <gray>You can only make existing spawnpoints
    into the main spawnpoint rather than create new ones since you are not a player!
  nonAlphanumericSpawn: <prefix> <gray>Spawnpoints need to be alphanumeric!
  removeSpawnNoArgs: <prefix> <gray>You have to specify a spawnpoint to remove!
  noSuchPlugin: <prefix> <gray>This plugin is not supported for importing/exporting
    yet!
  cantImport: <prefix> <gray>Can't import plugin data from <aqua><plugin></aqua> (make
    sure it's enabled and by the correct authors)!
  cantExport: <prefix> <gray>Can't export plugin data from <aqua><plugin></aqua> (make
    sure it's enabled and by the correct authors)!
  noPluginSpecified: <prefix> <gray>You need to specify a plugin to import/export
    from!
  invalidOption: <prefix> <gray>That is not a valid option to import/export!
  notEnoughItems: |-
    <prefix> <gray>You do not have enough items to teleport there!
        <prefix> <gray>You need at least <aqua><amount></aqua> <type>(s)!
  mirrorSpawnFail: <prefix> <gray>Failed to mirror <aqua><from></aqua>'s spawnpoint
    to <aqua><spawn></aqua>!
  removeSpawnFail: <prefix> <gray>Failed to remove the spawnpoint <aqua><spawn></aqua>!
  setMainSpawnFail: <prefix> <gray>Failed to set the main spawnpoint <aqua><spawn></aqua>!
  blockFail: <prefix> <gray>Failed to save the block against <aqua><player></aqua>!
  unblockFail: <prefix> <gray>Failed to save the block removal against <aqua><player></aqua>!
  noParticlePlugins: <prefix> <gray>There are no particle plugins on this server!
    You need at least one (PlayerParticles) to use this command.
  notEnoughArgs: <prefix> <gray>You haven't specified enough arguments to run this
    command!
  failedMapIconUpdate: <prefix> <gray>Failed to update the map icon! Please check
    the console for more information.
  setWarpFail: <prefix> <gray>Failed to set the warp <warp>!
  teleportFailed: <prefix> <gray>Sorry, we couldn't teleport you :(
  randomLocFailed: <prefix> <gray>Sorry, we couldn't find a location to teleport you
    to :(
Info:
  tpOff: <prefix> <gray>Successfully disabled teleport requests!
  tpOn: <prefix> <gray>Successfully enabled teleport requests!
  tpAdminOff: <prefix> <gray>Successfully disabled teleport requests for <aqua><player></aqua>!
  tpAdminOn: <prefix> <gray>Successfully enabled teleport requests for <aqua><player></aqua>!
  requestSent: |2
        <prefix> <gray>Successfully sent request to <aqua><player></aqua>!
        <prefix> <gray>They've got <aqua><lifetime></aqua> to respond!
        <prefix> <gray>To cancel the request use <aqua>/tpcancel</aqua> to cancel it.

                            <click:run_command:'/tpcancel <player>'><hover:show_text:'<red>Click here to cancel the request.'><gray><bold>[CANCEL]</hover></click>
  tpaRequestReceived: |2
        <prefix> <gray>The player <aqua><player></aqua> wants to teleport to you!
        <prefix> <gray>If you want to accept it, use <aqua>/tpayes</aqua>, but if not, use <aqua>/tpano</aqua>.
        <prefix> <gray>You've got <aqua><lifetime></aqua> to respond to it!

                            <click:run_command:'/tpayes <player>'><hover:show_text:'<green>Click here to accept the request.'><green><bold>[ACCEPT]</hover></click>             <click:run_command:'/tpano <player>'><hover:show_text:'<red>Click here to deny the request.'><red><bold>[DENY]</red></bold></hover></click>
  tpaRequestHere: |2
        <prefix> <gray>The player <aqua><player></aqua> wants to teleport you to them!
        <prefix> <gray>If you want to accept it, use <aqua>/tpayes</aqua>, but if not, use <aqua>/tpano</aqua>.
        <prefix> <gray>You've got <aqua><lifetime> seconds</aqua> to respond to it!

                          <click:run_command:'/tpayes <player>'><hover:show_text:'<green>Click here to accept the request.'><green><bold>[ACCEPT]</bold></hover></click>             <click:run_command:'/tpano <player>'><hover:show_text:'<red>Click here to deny the request.'><red><bold>[DENY]</red></bold></hover></click>
  blockPlayer: <prefix> <aqua><player> <gray>has been blocked.
  tpCancel: <prefix> <gray>You have cancelled your teleport request.
  tpCancelResponder: <prefix> <aqua><player> <gray>has cancelled their teleport request.
  multipleRequestsCancel: '<prefix> <gray>You have multiple teleport requests pending!
    Click one of the following to cancel:'
  multipleRequestsIndex: <prefix> <click:run_command:<command> <player>><player></click>
  multipleRequestsList: <prefix> <gray>Do /tpalist <Page Number> To check other requests.
  multipleRequestAccept: '<prefix> <gray>You have multiple teleport requests pending!
    Click one of the following to accept:'
  multipleRequestDeny: '<prefix> <gray>You have multiple teleport requests pending!
    Click one of the following to deny:'
  requestDeclined: <prefix> <gray>You've declined the teleport request!
  requestDeclinedResponder: <prefix> <gray><aqua><player></aqua> has declined your
    teleport request!
  requestDisplaced: <prefix> <gray>Your request has been cancelled because <aqua><player></aqua>
    got another request!
  deletedHome: <prefix> <gray>Successfully deleted the home <aqua><home></aqua>!
  deletedHomeOther: <prefix> <gray>Successfully deleted the home <aqua><home></aqua>
    for <aqua><player></aqua>!
  setHome: <prefix> <gray>Successfully set the home <aqua><home></aqua>!
  setHomeOther: <prefix> <gray>Successfully set the home <aqua><home></aqua> for <aqua><player></aqua>!
  setSpawn: <prefix> <gray>Successfully set the spawnpoint!
  setWarp: <prefix> <gray>Successfully set the warp <aqua><warp></aqua>!
  deletedWarp: <prefix> <gray>Successfully deleted the warp <aqua><warp></aqua>!
  purgeWarpsWorld: <prefix> <gray>Successfully purged warps in <aqua><world></aqua>!
  purgeWarpsCreator: <prefix> <gray>Successfully purged warps created by <aqua><player></aqua>!
  purgeHomesWorld: <prefix> <gray>Successfully purged homes in <aqua><world></aqua>!
  purgeHomesCreator: <prefix> <gray>Successfully purged homes created for <aqua><player></aqua>!
  searching: <prefix> <gray>Searching for a location...
  unblockPlayer: <prefix> <gray>Successfully unblocked <aqua><player></aqua>!
  reloadingConfig: <prefix> <gray>Reloading <aqua>AdvancedTeleport</aqua>'s config...
  reloadedConfig: <prefix> <gray>Finished reloading the config!
  warps: <aqua><bold>Warps <dark_gray>» <reset>
  homes: <aqua><bold>Homes <dark_gray>» <reset>
  homesOther: <aqua><bold><player>'s homes <dark_gray>» <reset>
  requestAccepted: <prefix> <gray>You've accepted the teleport request!
  requestAcceptedResponder: <prefix> <gray><aqua><player></aqua> has accepted the
    teleport request!
  paymentVault: <prefix> <gray>You have paid <aqua><amount></aqua> and now have <aqua><balance></aqua>!
  paymentEXP: <prefix> <gray>You have paid <aqua><amount> EXP Levels</aqua> and now
    have <aqua><levels></aqua> levels!
  paymentPoints: <prefix> <gray>You have paid <aqua><amount> EXP Points</aqua> and
    now have <aqua><points></aqua> points!
  createdWarpSign: <prefix> <gray>Successfully created the warp sign!
  createdRTPSign: <prefix> <gray>Successfully created the RandomTP sign!
  createdSpawnSign: <prefix> <gray>Successfully created the spawn sign!
  tpallRequestSent: <prefix> <gray>Successfully sent a teleport request to <aqua><amount></aqua>
    player(s)!
  teleportedToLoc: '<prefix> <gray>Successfully teleported you to <aqua><x></aqua>,
    <aqua><y></aqua>, <aqua><z></aqua>! (Yaw: <aqua><yaw></aqua>, Pitch: <aqua><pitch></aqua>,
    World: <aqua><world></aqua>)'
  teleportedToLocOther: '<prefix> <gray>Successfully teleported <aqua><player></aqua>
    to <aqua><x></aqua>, <aqua><y></aqua>, <aqua><z></aqua>! (Yaw: <aqua><yaw></aqua>,
    Pitch: <aqua><pitch></aqua>, World: <aqua><world></aqua>)'
  movedWarp: <prefix> <gray>Moved <aqua><warp></aqua> to your current location!
  movedHome: <prefix> <gray>Moved home <aqua><home></aqua> to your current location!
  movedHomeOther: <prefix> <gray>Moved <aqua><player>'s </aqua> home <aqua><home></aqua>
    to your location!
  setMainHome: <prefix> <gray>Made <aqua><home></aqua> your main home!
  setAndMadeMainHome: <prefix> <gray>Set <aqua><home></aqua> at your current location
    and made it your main home!
  setMainHomeOther: <prefix> <gray>Made <aqua><home> <player></aqua>'s main home!
  setAndMadeMainHomeOther: <prefix> <gray>Set <aqua><home></aqua> for <aqua><player></aqua>
    at your current location and made it their main home!
  mirroredSpawn: <prefix> <gray>Mirrored <aqua><from></aqua>'s spawnpoint to <aqua><spawn></aqua>!
  setMainSpawn: <prefix> <gray>Set the main spawnpoint to <aqua><spawn></aqua>! All
    players will teleport there if there are no overriding spawns/permissions.
  removedSpawn: <prefix> <gray>Removed the spawnpoint <aqua><spawn></aqua>!
  setSpawnSpecial: <prefix> <gray>Set spawnpoint <aqua><spawn></aqua>!
  importStarted: <prefix> <gray>Starting import from <aqua><plugin></aqua>...
  importFinished: <prefix> <gray>Finished import from <aqua><plugin></aqua>!
  exportStarted: <prefix> <gray>Starting export to <aqua><plugin></aqua>...
  exportFinished: <prefix> <gray>Finished export to <aqua><plugin></aqua>!
  paymentItems: <prefix> <gray>You have paid <aqua><amount> <type>(s)</aqua> for that
    teleport!
  updateInfo: |-
    <prefix> <hover:show_text:'<aqua>Current Version <dark_gray>» <gray><version>
    <aqua>New Version <dark_gray>» <gray><new-version>
    <aqua>Title <dark_gray>» <gray><title>'><click:open_url:'https://www.spigotmc.org/resources/advancedteleport.64139/'><gray>AdvancedTeleport has an update available! Click/hover over this text for more information.</click></hover>
  defaultParticlesUpdated: <prefix> <gray>The default waiting particles have been
    set to your current particle setup!
  specificParticlesUpdated: <prefix> <gray>The waiting particles settings for <aqua><type></aqua>
    have been set to your current particle setup!
  mapIconUpdateClickTooltip: <prefix> <gray>Updated click tooltip for <type> <aqua><name></aqua>!
    The map should update shortly.
  mapIconUpdateHoverTooltip: <prefix> <gray>Updated hover tooltip for <type> <aqua><name></aqua>!
    The map should update shortly.
  mapIconUpdateIcon: <prefix> <gray>Updated the icon for <type> <aqua><name></aqua>!
    The map should update shortly.
  mapIconUpdateSize: <prefix> <gray>Updated the icon size for <type> <aqua><name></aqua>!
    The map should update shortly.
  mapIconUpdateVisibility: <prefix> <gray>Updated the icon visibility for <type> <aqua><name></aqua>!
    The map should update shortly.
  mirrorSpawnSame: <prefix> <gray>The spawns for <aqua><from></aqua> and <aqua><spawn></aqua>
    already to go the same place! Don't worry :)
Tooltip:
  homes: '<prefix> <gray>Teleports you to your home: <aqua><home>'
  warps: '<prefix> <gray>Teleports you to warp: <aqua><warp>'
  location: |2-

    <aqua>X <dark_gray>» <gray><x>
    <aqua>Y <dark_gray>» <gray><y>
    <aqua>Z <dark_gray>» <gray><z>
    <aqua>World <dark_gray>» <gray><world>
Menu:
  Help:
    header: <aqua>・．<gray>━━━━━━━━━━━</gray> <dark_gray>❰</dark_gray> <bold>Advanced
      Teleport</bold> <gray><current_page>/<total_pages> <dark_gray>❱</dark_gray>
      <gray>━━━━━━━━━━━</gray>．・
    option: <dark_gray>» <aqua><usage></aqua> ~ <gray><description>
Descriptions:
  Subcommands:
    help: Sends the help menu, providing a full list of commands.
    info: Sends information regarding the plugin.
    import: Imports data from another plugin so that it can be used within AT.
    export: Exports data within AT to another plugin.
    reload: Reloads the plugin's configuration.
    clearcache: Clears the RTP cache.
    map:
      setclicktooltip: Sets the tooltip of an AT icon in a map plugin when it is clicked
        (excluding Dynmap).
      sethovertooltip: Sets the tooltip of an AT icon in a map plugin when it is hovered
        over.
      seticon: Sets the image of an AT icon in a map plugin.
      setsize: Sets the size of an AT icon in a map plugin (excluding Dynmap).
      setvisible: Sets the visibility of an AT icon in a map plugin when it is clicked.
  at: The core command for AT.
  tpa: Sends a request to teleport to the player.
  tpahere: Sends a request to the player to teleport to you.
  tpyes: Accepts a player's teleport request.
  tpno: Declines a player's teleport request.
  tpcancel: Cancels your teleport request to a player.
  toggletp: Either stops or allows players to send teleport requests to you.
  tpon: Allows players to send teleport requests to you.
  tpoff: Stops players from sending teleport requests to you.
  tpblock: Stops a specific player from sending teleport requests to you.
  tpunblock: Allows a blocked player to send you teleport requests again.
  back: Teleports you to your previous location.
  tpalist: Lists all of your current teleport requests.
  tpo: Instantly teleports you to a player.
  tpohere: Instantly teleports a player to you.
  tpall: Sends a teleport request to everyone in the server to you.
  tploc: Teleports you to a specific location.
  tpoffline: Teleports you to an offline player.
  tpofflinehere: Teleports an offline player to you.
  tpr: Teleports you to a random location.
  warp: Teleports you to a given warp point.
  warps: Gives you a list of warps you can teleport to.
  setwarp: Sets a warp at your location.
  delwarp: Deletes a warp.
  movewarp: Moves a warp to a new location.
  spawn: Teleports you to the spawnpoint.
  setspawn: Sets a spawn with a name when specified.
  mirrorspawn: Redirects people using /spawn in one world to another spawn point.
  setmainspawn: Sets a specified spawnpoint to become the main spawnpoint. If it does
    not exist, it will be created if you have /setspawn permissions.
  removespawn: Removes a specified spawnpoint. If none is specified, the one in your
    current world is removed.
  home: Teleports you to your home.
  homes: Gives you a list of homes you've set.
  sethome: Sets a home at your current location.
  delhome: Deletes a home.
  movehome: Moves a home to a new location.
  setmainhome: Sets a home at your location or makes an existing one your main home.
  purge: Removes all warps or homes for the specified player or world.
  particles: Ports your current particle selection to the default waiting particles
    configuration, or a command one.
Usages:
  Subcommands:
    help: /at help [Category|Page]
    info: /at info
    import: /at import <Plugin> [All|Homes|LastLocs|Warps|Spawns|Players]
    export: /at export <Plugin> [All|Homes|LastLocs|Warps|Spawns|Players]
    purge: /at purge <Homes|Warps> <Player|World> <Player Name|World Name>
    reload: /at reload
    clearcache: /at clearcache [World]
    particles: /at particles [Tpa|Tpahere|Home|Tpr|Warp|Spawn|Back]
    map:
      setclicktooltip: /at map setclicktooltip <Home|Warp|Spawn> [Home Owner] <Tooltip>
      sethovertooltip: /at map sethovertooltip <Home|Warp|Spawn> [Home Owner] <Tooltip>
      seticon: /at map seticon <Home|Warp|Spawn> [Home Owner] <Image Name>
      setsize: /at map setsize <Home|Warp|Spawn> [Home Owner] <Image Size>
      setvisible: /at map setvisible <Home|Warp|Spawn> [Home Owner] <true|false>
  at: /at <Command>
  tpa: /tpa <Player>
  tpahere: /tpahere <Player>
  tpyes: /tpyes [Player]
  tpno: /tpno [Player]
  tpcancel: /tpcancel [Player]
  toggletp: /toggletp
  tpon: /tpon
  tpoff: /tpoff
  tpblock: /tpblock <Player> [Reason]
  tpunblock: /tpunblock <Player>
  back: /back
  tpalist: /tpalist
  tpo: /tpo <Player>
  tpohere: /tpohere <Player>
  tpall: /tpall
  tploc: /tploc <x|~> <y|~> <z|~> [Yaw|~] [Pitch|~] [World|~] [Player] [precise|noflight]
  tpoffline: /tpoffline <Player>
  tpofflinehere: /tpofflinehere <Player>
  tpr: /tpr [World]
  warp: /warp <Warp>
  warps: /warps
  setwarp: /setwarp <Name>
  delwarp: /delwarp <Name>
  movewarp: /movewarp <Name>
  spawn: /spawn
  setspawn: /setspawn [ID]
  mirrorspawn: /mirrorspawn <To Point>|[From World] [To Point]
  setmainspawn: /setmainspawn [Point]
  removespawn: /removespawn [Point]
  home: /home [Home]
  homes: /homes
  sethome: /sethome <Name>
  delhome: /delhome <Home>
  movehome: /movehome <Home>
  setmainhome: /setmainhome <Home>
Usages-Admin:
  tpr: /tpr [World] [Player]
  home: /home [Home]|<Player> <Home>
  homes: /homes [Player]
  delhome: /delhome <Home>|<Player> <Home>
  sethome: /sethome <Name>|<Player> <Name>
  movehome: /movehome <Home>|<Player> <Home>
  setmainhome: /setmainhome <Home>|<Player> <Home>
  spawn: /spawn <ID>
Signs:
  bed: <blue><bold>[Bed]</bold></blue>
  home: <blue><bold>[Home]</bold></blue>
  homes: <blue><bold>[Homes]</bold></blue>
  randomtp: <blue><bold>[RandomTP]</bold></blue>
  spawn: <blue><bold>[Spawn]</bold></blue>
  warp: <blue><bold>[Warp]</bold></blue>
  warps: <blue><bold>[Warps]</bold></blue>
Forms:
  tpahere-title: TPAHere Request
  tpahere-description: Select a player to send a TPAHere request to.
  tpa-title: TPA Request
  tpa-description: Select a player to send a TPA request to.
  tpa-received-title: TPA Request
  tpa-received-description: The player <player> wants to teleport to you!
  tpa-received-accept: Accept
  tpa-received-deny: Deny
  tpahere-received-title: TPAHere Request
  tpahere-received-description: The player <player> wants you to teleport to them!
  tpahere-received-accept: Accept
  tpahere-received-deny: Deny
  home-title: Homes
  home-description: Select a home to teleport to.
  sethome-title: Set Home
  sethome-description: Enter a home name.
  delhome-title: Delete Home
  delhome-description: Select the home to delete.
  setmainhome-title: Set Main Home
  setmainhome-description: Enter an existing home name or a new one.
  movehome-title: Move Home
  movehome-description: Choose the home to be moved.
  warp-title: Warps
  warp-description: Select a warp to teleport to.
  delwarp-title: Delete Warp
  delwarp-description: Select a warp to delete.
  setwarp-title: Set Warp
  setwarp-description: Enter a warp name.
  movewarp-title: Move Warp
  movewarp-description: Select a warp to move.
  tpblock-title: Block Player
  tpblock-description: Select a player to block.
  tpunblock-title: Unblock Player
  tpunblock-description: Select a player to unblock.
  tpcancel-title: Cancel TP Request
  tpcancel-description: Select a request to cancel.
  tpo-title: Teleport
  tpo-description: Select a player to teleport to.
  tpohere-title: Teleport Here
  tpohere-description: Select a player to teleport to your location.
